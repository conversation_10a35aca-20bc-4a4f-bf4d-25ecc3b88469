
import { Transaction } from "@/types/transaction.types";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";
import { formatCurrency } from "@/utils/formatters";
import { Badge } from "@/components/ui/badge";

interface TransactionDesktopTableProps {
  transactions: Transaction[];
  loading: boolean;
}

export function TransactionDesktopTable({
  transactions,
  loading
}: TransactionDesktopTableProps) {
  // تعريف أعمدة جدول المعاملات
  const columns: TableColumn<Transaction>[] = [
    {
      header: "العميل",
      accessor: (transaction) => (
        <div className="flex flex-col">
          <span className="font-medium">{transaction.customer_name || transaction.customer?.name || "غير محدد"}</span>
          {(transaction.customer_phone || transaction.customer?.phone) && (
            <span className="text-xs text-muted-foreground">
              {transaction.customer_phone || transaction.customer?.phone}
            </span>
          )}
        </div>
      ),
      className: "font-medium"
    },
    {
      header: "الحالة",
      accessor: (transaction) => (
        <Badge 
          variant="outline"
          className="rtl-badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
        >
          مكتملة
        </Badge>
      )
    },
    {
      header: "المبلغ",
      accessor: (transaction) => (
        <span className={transaction.transaction_type === "receive" 
          ? "text-green-600 dark:text-green-400 font-medium" 
          : "text-red-600 dark:text-red-400 font-medium"}>
          {transaction.transaction_type === "receive" ? "+" : "-"}
          {formatCurrency(Number(transaction.amount))}
        </span>
      ),
      className: "text-right"
    },
    {
      header: "العمولة",
      accessor: (transaction) => (
        transaction.commission > 0 
          ? <span className="text-green-600 dark:text-green-400 font-medium">{formatCurrency(Number(transaction.commission))}</span>
          : <span className="text-gray-500">—</span>
      ),
      className: "text-right"
    },
    {
      header: "النوع",
      accessor: (transaction) => (
        <Badge variant={transaction.transaction_type === "receive" ? "success" : "destructive"} className="rtl-badge">
          {transaction.transaction_type === "receive" ? "استلام" : "سحب"}
        </Badge>
      )
    },
    {
      header: "المحفظة",
      accessor: (transaction) => transaction.wallet?.name || "غير محدد",
    },
    {
      header: "التاريخ",
      accessor: (transaction) => new Date(transaction.created_at).toLocaleDateString("ar-EG"),
      className: "text-right"
    },
    {
      header: "الوصف",
      accessor: "description",
      className: "max-w-[200px] truncate"
    }
  ];

  return (
    <ModernTable
      data={transactions}
      columns={columns}
      loading={loading}
      keyField="id"
      emptyMessage="لا توجد معاملات متطابقة"
      dir="rtl"
      mobileCardTitle={(transaction) => `${transaction.customer_name || transaction.customer?.name || 'عميل'} - ${formatCurrency(Number(transaction.amount))}`}
      mobileCardActions={(transaction) => (
        <div className="flex items-center gap-1">
          <Badge 
            variant={transaction.transaction_type === "receive" ? "success" : "destructive"}
            className="rtl-badge text-xs"
          >
            {transaction.transaction_type === "receive" ? "استلام" : "سحب"}
          </Badge>
        </div>
      )}
    />
  );
}
