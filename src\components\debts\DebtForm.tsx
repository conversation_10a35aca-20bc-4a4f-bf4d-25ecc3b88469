
import { useDebtForm } from "@/hooks/useDebtForm";
import { Debt } from "@/types/debt.types";
import { Form } from "@/components/ui/form";
import {
  CustomerSelect,
  AmountField,
  DueDateField,
  StatusField,
  NotesField,
  FormActions
} from "./form";

interface DebtFormProps {
  debt?: Debt;
  onSuccess: () => void;
  onCancel: () => void;
}

export function DebtForm({ debt, onSuccess, onCancel }: DebtFormProps) {
  const { form, loading, customers, loadingCustomers, onSubmit } = useDebtForm({
    debt,
    onSuccess,
  });

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="space-y-4">
        <CustomerSelect 
          loading={loading} 
          customers={customers} 
          loadingCustomers={loadingCustomers}
          form={form}
        />
        <AmountField loading={loading} />
        <DueDateField loading={loading} />
        <StatusField loading={loading} />
        <NotesField loading={loading} />
        <FormActions loading={loading} onCancel={onCancel} />
      </form>
    </Form>
  );
}
