
import { useState, useEffect } from "react";
import { Customer } from "@/types/debt.types";
import { UseFormReturn } from "react-hook-form";
import { 
  FormField,
  FormControl, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { SpecialCustomerSelector } from "@/components/customers/special/SpecialCustomerSelector";

interface CustomerSelectProps {
  loading: boolean;
  customers: Customer[];
  loadingCustomers: boolean;
  form: UseFormReturn<any>;
}

export function CustomerSelect({ loading, customers, loadingCustomers, form }: CustomerSelectProps) {
  const [selectedSpecialCustomer, setSelectedSpecialCustomer] = useState<{id: string; name: string; phone?: string | null} | null>(null);

  // Set initial selected customer if form has a customer_id
  useEffect(() => {
    const customerId = form.getValues("customer_id");
    if (customerId && !selectedSpecialCustomer) {
      // Check if it's a customer from the regular customers list
      const customer = customers.find(c => c.id === customerId);
      if (customer) {
        setSelectedSpecialCustomer({
          id: customer.id,
          name: customer.name,
          phone: customer.phone
        });
      }
    }
  }, [customers, form, selectedSpecialCustomer]);

  const handleSpecialCustomerSelect = (customer: {id: string; name: string; phone?: string | null} | null) => {
    setSelectedSpecialCustomer(customer);
  };

  return (
    <div className="space-y-4">
      <FormField
        name="customer_id"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>العميل</FormLabel>
            <FormControl>
              <SpecialCustomerSelector 
                onSelectCustomer={(customer) => {
                  handleSpecialCustomerSelect(customer);
                  customer ? field.onChange(customer.id) : field.onChange("");
                }}
                selectedCustomer={selectedSpecialCustomer}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
