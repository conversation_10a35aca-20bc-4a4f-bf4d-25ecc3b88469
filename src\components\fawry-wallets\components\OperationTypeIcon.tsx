
import React from "react";
import { ArrowDownSquare, ArrowUpSquare } from "lucide-react";

interface OperationTypeIconProps {
  type: string;
}

const OperationTypeIcon: React.FC<OperationTypeIconProps> = ({ type }) => {
  if (type === "استلام") {
    return (
      <div className="bg-green-100 dark:bg-green-900/30 p-0.5 rounded-md inline-flex">
        <ArrowDownSquare className="text-green-500 mr-1" size={18} />
      </div>
    );
  }
  return (
    <div className="bg-red-100 dark:bg-red-900/30 p-0.5 rounded-md inline-flex">
      <ArrowUpSquare className="text-red-500 mr-1" size={18} />
    </div>
  );
};

export default OperationTypeIcon;
