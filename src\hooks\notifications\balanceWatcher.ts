
import { supabase } from "@/integrations/supabase/client";
import { fetchNotificationRules, fetchTelegramSettings } from './notificationSettings';
import { LowBalanceSim } from './types';
import { sendLowBalanceNotification } from './sendNotifications';

// تتبع آخر إشعارات الرصيد المنخفض المرسلة
const sentLowBalanceNotifications = new Map<string, number>();

/**
 * مراقبة أرصدة المحافظ والإشعار بانخفاض الرصيد
 */
export const checkWalletBalances = async (): Promise<void> => {
  try {
    console.log('🔍 Checking wallet balances');
    
    // الحصول على قواعد الإشعار
    const rules = await fetchNotificationRules();
    
    if (!rules || !rules.balance_alert_enabled) {
      console.log('❌ Balance alerts disabled in notification rules');
      return;
    }
    
    // الحصول على إعدادات تليجرام
    const telegramSettings = await fetchTelegramSettings();
    
    if (!telegramSettings || !telegramSettings.is_enabled) {
      console.log('❌ Telegram notifications disabled in settings');
      return;
    }
    
    console.log('✅ Checking for low balance sims (threshold:', rules.min_balance, ')');
    
    // الحصول على جميع الشرائح ذات الرصيد المنخفض
    const { data: lowBalanceSims, error } = await supabase
      .from('sims')
      .select(`
        id,
        number,
        balance,
        wallet:wallet_id(name)
      `)
      .lt('balance', rules.min_balance)
      .eq('active_status', true); // فقط الشرائح النشطة
    
    if (error) {
      console.error('Error fetching low balance sims:', error);
      throw error;
    }
    
    if (!lowBalanceSims || lowBalanceSims.length === 0) {
      console.log('✅ No low balance sims found');
      return;
    }
    
    console.log(`⚠️ Found ${lowBalanceSims.length} low balance sims`);
    
    // الحصول على آخر إشعارات مرسلة من قواعد البيانات لتجنب التكرار
    const now = Date.now();
    
    // إرسال إشعار لكل شريحة ذات رصيد منخفض
    for (const sim of lowBalanceSims) {
      const notificationKey = `low_balance_sim_${sim.id}`;
      const lastSent = sentLowBalanceNotifications.get(notificationKey) || 0;
      
      // تجنب إرسال إشعارات متكررة خلال ساعة واحدة
      if (now - lastSent < 60 * 60 * 1000) {
        console.log(`⏱️ Skipping duplicate low balance notification for sim ${sim.number}, last sent ${Math.round((now - lastSent)/60000)}m ago`);
        continue;
      }
      
      console.log(`📤 Sending low balance notification for sim ${sim.number} (${sim.balance})`);
      
      // تحديث خريطة الإشعارات المرسلة
      sentLowBalanceNotifications.set(notificationKey, now);
      
      // تحويل بيانات الشريحة إلى الشكل الصحيح لإرسال الإشعار
      const lowBalanceSim: LowBalanceSim = {
        id: sim.id,
        number: sim.number,
        balance: sim.balance,
        wallet_name: sim.wallet?.name || 'غير معروف'
      };
      
      await sendLowBalanceNotification(
        telegramSettings.bot_token,
        telegramSettings.chat_id,
        lowBalanceSim,
        rules.min_balance
      );
    }
  } catch (error) {
    console.error('Error checking wallet balances:', error);
  }
};
