
import { supabase } from "@/integrations/supabase/client";
import { validateBackupData } from "./validationUtils";

/**
 * Restores database from backup data
 */
export const restoreDatabaseFromBackup = async (backupData: any) => {
  try {
    if (!validateBackupData(backupData)) {
      throw new Error("تنسيق ملف النسخة الاحتياطية غير صالح");
    }
    
    // Process each table with proper typing
    const { tables } = backupData;
    const restoreOperations = [];
    
    // Create a type-safe way to restore data to specific tables
    if (tables.transactions && Array.isArray(tables.transactions)) {
      const { error } = await supabase.from('transactions').upsert(tables.transactions);
      restoreOperations.push({ table: 'transactions', success: !error, error: error?.message });
    }
    
    if (tables.customers && Array.isArray(tables.customers)) {
      const { error } = await supabase.from('customers').upsert(tables.customers);
      restoreOperations.push({ table: 'customers', success: !error, error: error?.message });
    }
    
    if (tables.wallets && Array.isArray(tables.wallets)) {
      const { error } = await supabase.from('wallets').upsert(tables.wallets);
      restoreOperations.push({ table: 'wallets', success: !error, error: error?.message });
    }
    
    if (tables.sims && Array.isArray(tables.sims)) {
      const { error } = await supabase.from('sims').upsert(tables.sims);
      restoreOperations.push({ table: 'sims', success: !error, error: error?.message });
    }
    
    if (tables.debts && Array.isArray(tables.debts)) {
      const { error } = await supabase.from('debts').upsert(tables.debts);
      restoreOperations.push({ table: 'debts', success: !error, error: error?.message });
    }
    
    if (tables.employees && Array.isArray(tables.employees)) {
      const { error } = await supabase.from('employees').upsert(tables.employees);
      restoreOperations.push({ table: 'employees', success: !error, error: error?.message });
    }
    
    if (tables.branches && Array.isArray(tables.branches)) {
      const { error } = await supabase.from('branches').upsert(tables.branches);
      restoreOperations.push({ table: 'branches', success: !error, error: error?.message });
    }
    
    if (tables.departments && Array.isArray(tables.departments)) {
      const { error } = await supabase.from('departments').upsert(tables.departments);
      restoreOperations.push({ table: 'departments', success: !error, error: error?.message });
    }
    
    if (tables.commission_settings && Array.isArray(tables.commission_settings)) {
      const { error } = await supabase.from('commission_settings').upsert(tables.commission_settings);
      restoreOperations.push({ table: 'commission_settings', success: !error, error: error?.message });
    }
    
    if (tables.wallet_commissions && Array.isArray(tables.wallet_commissions)) {
      const { error } = await supabase.from('wallet_commissions').upsert(tables.wallet_commissions);
      restoreOperations.push({ table: 'wallet_commissions', success: !error, error: error?.message });
    }
    
    // Check if any operations failed
    const failedOperations = restoreOperations.filter(op => !op.success);
    
    if (failedOperations.length > 0) {
      console.error("Failed operations:", failedOperations);
      throw new Error(`فشل استعادة البيانات في ${failedOperations.length} جداول`);
    }
    
    return restoreOperations;
  } catch (error) {
    console.error("Error restoring backup:", error);
    throw error;
  }
};
