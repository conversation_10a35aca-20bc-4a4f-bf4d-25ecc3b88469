
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Shield, AlertTriangle } from "lucide-react";
import { useSecuritySettings } from "./useSecuritySettings";
import { SecurityForm } from "./SecurityForm";
import { SecurityActions } from "./SecurityActions";
import { AccessDenied } from "./AccessDenied";
import { LoadingState } from "./LoadingState";
import { Button } from "@/components/ui/button";

export function SecuritySettings() {
  const {
    isEnabled,
    setIsEnabled,
    devLoginAlerts,
    setDevLoginAlerts,
    chatId,
    setChatId,
    isSaving,
    currentRole,
    loading,
    error,
    handleSaveSettings,
    sendTestAlert,
    reloadSettings
  } = useSecuritySettings();

  // Check if user is a developer
  const isDeveloper = currentRole === 'developer';

  if (loading) {
    return <LoadingState />;
  }

  if (error) {
    return (
      <Card className="mb-6 overflow-hidden border-b-4 border-b-red-500 shadow-md">
        <CardHeader className="bg-gradient-to-r from-red-50 to-transparent dark:from-red-900/20 dark:to-transparent">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            خطأ في تحميل إعدادات الأمان
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <p className="text-lg font-medium text-red-500 mb-4">{error}</p>
            <Button onClick={reloadSettings} className="bg-amber-500 hover:bg-amber-600">
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!isDeveloper) {
    return <AccessDenied />;
  }

  return (
    <Card className="mb-6 overflow-hidden border-b-4 border-b-amber-500 shadow-md">
      <CardHeader className="bg-gradient-to-r from-amber-50 to-transparent dark:from-amber-900/20 dark:to-transparent">
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-amber-500" />
          إعدادات الأمان والتنبيهات
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <SecurityForm
          isEnabled={isEnabled}
          setIsEnabled={setIsEnabled}
          devLoginAlerts={devLoginAlerts}
          setDevLoginAlerts={setDevLoginAlerts}
          chatId={chatId}
          setChatId={setChatId}
        />
        
        <SecurityActions
          handleSaveSettings={handleSaveSettings}
          sendTestAlert={sendTestAlert}
          isSaving={isSaving}
          isEnabled={isEnabled}
          devLoginAlerts={devLoginAlerts}
          chatId={chatId}
        />
      </CardContent>
    </Card>
  );
}
