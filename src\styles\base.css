
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 0 84% 60%;  /* Updated to red primary color */
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5% 65%;
    --radius: 0.75rem;

    /* متغيرات التدرجات اللونية للجداول */
    --table-gradient-start: rgba(255, 240, 240, 0.3);
    --table-gradient-end: rgba(255, 250, 250, 0.1);

    /* متغيرات للمحافظ */
    --wallet-vodafone: 0 100% 45%;
    --wallet-etisalat: 129 78% 47%;
    --wallet-orange: 32 100% 50%;
    --wallet-instapay: 199 100% 50%;

    /* متغيرات للتباينات */
    --contrast-high: 240 10% 3.9%;
    --contrast-low: 240 5% 65%;

    /* متغيرات جديدة للأزرار */
    --button-primary: 0 84% 60%;
    --button-primary-hover: 0 84% 55%;
    --button-secondary: 240 5.9% 90%;
    --button-secondary-hover: 240 5.9% 85%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 48 96% 53%;  /* Updated to gold/yellow primary color for dark mode */
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    /* متغيرات التدرجات اللونية للجداول */
    --table-gradient-start: rgba(30, 30, 35, 0.7);
    --table-gradient-end: rgba(30, 30, 35, 0.5);

    /* متغيرات للتباينات */
    --contrast-high: 0 0% 98%;
    --contrast-low: 240 5% 64.9%;

    /* متغيرات جديدة للأزرار */
    --button-primary: 48 96% 53%;
    --button-primary-hover: 48 96% 48%;
    --button-secondary: 240 3.7% 15.9%;
    --button-secondary-hover: 240 3.7% 20%;
  }

  * {
    @apply border-border selection:bg-primary/20 selection:text-primary;
  }

  body {
    @apply bg-background text-foreground antialiased;
  }

  /* تحسينات للعناصر المتكررة */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight leading-tight;
  }

  h1 {
    @apply text-3xl md:text-4xl;
  }

  h2 {
    @apply text-2xl md:text-3xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }

  /* تنسيقات للروابط */
  a {
    @apply transition-colors;
  }
  
  /* تحسينات للنصوص المهمة */
  .emphasized {
    @apply font-medium;
  }

  /* تحسينات للنصوص المخففة */
  .subtle {
    @apply text-muted-foreground;
  }

  /* تنسيقات للقوائم */
  ul, ol {
    @apply my-6 ml-6;
  }

  ul {
    @apply list-disc;
  }

  ol {
    @apply list-decimal;
  }

  /* تنسيقات للحدود */
  .border-subtle {
    @apply border border-border/50;
  }

  /* تنسيق للظلال */
  .shadow-subtle {
    @apply shadow-sm;
  }
}
