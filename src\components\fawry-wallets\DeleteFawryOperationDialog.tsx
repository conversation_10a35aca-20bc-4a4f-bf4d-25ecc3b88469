
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useDeleteFawryOperation } from "./hooks/useDeleteFawryOperation";

interface Operation {
  id: string;
  wallet_id: string;
  operation_type: string;
  amount: number;
}

interface DeleteFawryOperationDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  operation: Operation;
  onOperationDeleted: () => void;
}

const DeleteFawryOperationDialog: React.FC<DeleteFawryOperationDialogProps> = ({
  isOpen,
  onOpenChange,
  operation,
  onOperationDeleted,
}) => {
  const { isDeleting, handleDelete } = useDeleteFawryOperation({
    operation,
    onClose: () => onOpenChange(false),
    onOperationDeleted,
  });

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>هل أنت متأكد من حذف هذه العملية؟</AlertDialogTitle>
          <AlertDialogDescription>
            هذا الإجراء لا يمكن التراجع عنه. سيتم حذف العملية وتحديث رصيد المحفظة.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>إلغاء</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete} disabled={isDeleting} className="bg-red-600 hover:bg-red-700">
            {isDeleting ? "جاري الحذف..." : "حذف العملية"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteFawryOperationDialog;
