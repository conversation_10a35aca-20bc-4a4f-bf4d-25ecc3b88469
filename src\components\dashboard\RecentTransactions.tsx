
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useIsMobile } from "@/hooks/use-mobile";
import { TransactionCardContent, TransactionCardHeader } from "./recent-transactions";
import { ProcessedTransaction } from "@/types/transaction.types";

export function RecentTransactions() {
  const [transactions, setTransactions] = useState<ProcessedTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const isMobile = useIsMobile();

  const fetchTransactions = async () => {
    try {
      setIsLoading(true);
      console.log("Fetching recent transactions...");
      
      // Get current date
      const now = new Date();
      
      // Get date 30 days ago to have more chance of finding data
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(now.getDate() - 30);
      
      const { data, error } = await supabase
        .from('transactions')
        .select(`
          id,
          amount,
          commission,
          transaction_type,
          created_at,
          description,
          wallet:wallet_id (name),
          customer:customer_id (name),
          customer_name,
          customer_phone
        `)
        .gte('created_at', thirtyDaysAgo.toISOString())
        .order('created_at', { ascending: false })
        .limit(10);
      
      if (error) {
        console.error("Error fetching transactions:", error);
        throw error;
      }
      
      console.log("Fetched recent transactions:", data?.length || 0);
      
      // Process data to match the ProcessedTransaction type
      const processedData: ProcessedTransaction[] = (data || []).map(tx => ({
        id: tx.id,
        amount: tx.amount,
        transaction_type: tx.transaction_type,
        created_at: tx.created_at,
        description: tx.description || '',
        commission: tx.commission,
        walletName: tx.wallet?.name || 'غير معروف',
        customerName: tx.customer?.name || '',
        customer_name: tx.customer_name || ''
      }));
      
      setTransactions(processedData);
      console.log("Processed transactions:", processedData.length);
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toast.error("حدث خطأ أثناء جلب المعاملات الأخيرة");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, []);

  // Format date to be more readable
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return "اليوم";
    } else if (diffDays === 1) {
      return "الأمس";
    } else {
      return `منذ ${diffDays} يوم`;
    }
  };

  return (
    <Card className={`dashboard-card dashboard-card-animate overflow-hidden ${isMobile ? 'rtl-mobile-card' : ''}`}>
      <TransactionCardHeader transactionsCount={transactions.length} />
      <TransactionCardContent 
        transactions={transactions} 
        isLoading={isLoading}
        formatDate={formatDate}
      />
    </Card>
  );
}
