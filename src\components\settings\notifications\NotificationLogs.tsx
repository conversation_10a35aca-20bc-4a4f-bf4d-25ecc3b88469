
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Bell, RefreshCw, Trash2 } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";
import { useNotificationLogs } from "@/hooks/useNotificationLogs";
import { NotificationTable, ClearLogsDialog } from "./components";
import { useResponsiveNotifications } from "@/components/layout/notifications/NotificationUtils";
import { NotificationPagination } from "./components/NotificationPagination";
import { ScrollArea } from "@/components/ui/scroll-area";

export function NotificationLogs() {
  const { 
    logs, 
    isLoading, 
    error,
    totalCount,
    totalPages,
    currentPage,
    showDeleteDialog, 
    setShowDeleteDialog, 
    isDeleting,
    clearLogs,
    getStatusColor,
    goToNextPage,
    goToPreviousPage,
    hasNextPage,
    hasPreviousPage
  } = useNotificationLogs();
  
  const queryClient = useQueryClient();
  const { isMobile } = useResponsiveNotifications();

  if (error) {
    return (
      <Card className="overflow-hidden border-b-4 border-b-red-500 shadow-sm hover:shadow-md transition-shadow">
        <CardHeader className="bg-gradient-to-r from-red-50 to-transparent dark:from-red-900/20 dark:to-transparent">
          <CardTitle className="text-red-500">خطأ في تحميل سجل الإشعارات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <p>فشل في تحميل سجل الإشعارات. حاول مرة أخرى لاحقًا.</p>
            <Button 
              variant="outline" 
              onClick={() => queryClient.invalidateQueries({ queryKey: ['notificationLogs'] })}
              className="mt-4"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`overflow-hidden glass-card-hover transition-all duration-300 border-2 light:border-[#ea384c] dark:border-amber-500 mx-auto w-full ${isMobile ? "p-2" : ""}`}>
      <CardHeader className={`bg-gradient-to-r from-green-50 to-transparent dark:from-green-900/20 dark:to-transparent ${isMobile ? "p-4" : ""}`}>
        <div className={`flex ${isMobile ? "flex-col gap-2" : "flex-row items-center justify-between"}`}>
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-green-500" />
            <CardTitle className={isMobile ? "text-xl" : ""}>سجل الإشعارات</CardTitle>
            {totalCount > 0 && (
              <span className="text-sm text-muted-foreground">
                ({totalCount} إشعار)
              </span>
            )}
          </div>
          <div className={`flex items-center ${isMobile ? "w-full justify-end" : "gap-2"}`}>
            <Button
              variant="destructive"
              size={isMobile ? "sm" : "default"}
              onClick={() => setShowDeleteDialog(true)}
              className={`flex items-center gap-1 ${isMobile ? "rtl-mobile-button" : ""}`}
              disabled={!logs?.length || isDeleting || isLoading}
            >
              <Trash2 className="h-4 w-4" />
              {!isMobile && "مسح السجل"}
              {isMobile && "مسح"}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className={`${isMobile ? "p-2" : "p-4"} w-full`}>
        <div className="w-full">
          <NotificationTable 
            logs={logs}
            isLoading={isLoading}
            getStatusColor={getStatusColor}
          />
        </div>

        {totalPages > 1 && (
          <div className="mt-6 w-full">
            <NotificationPagination 
              currentPage={currentPage}
              totalPages={totalPages}
              onNextPage={goToNextPage}
              onPreviousPage={goToPreviousPage}
              hasNextPage={hasNextPage}
              hasPreviousPage={hasPreviousPage}
            />
          </div>
        )}

        <ClearLogsDialog
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          onConfirm={clearLogs}
          isDeleting={isDeleting}
        />
      </CardContent>
    </Card>
  );
}
