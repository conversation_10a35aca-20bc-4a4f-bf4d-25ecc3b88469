
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface Wallet {
  id: string;
  name: string;
  balance: number;
  color_class?: string;
}

export const useWalletDashboard = () => {
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteWalletId, setDeleteWalletId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Fetch wallets data
  const fetchWallets = async () => {
    try {
      setLoading(true);
      // Fetch wallets
      const { data: walletsData, error: walletsError } = await supabase
        .from('wallets')
        .select('*');
      
      if (walletsError) throw walletsError;
      
      // Fetch sim balances for each wallet
      const { data: simsData, error: simsError } = await supabase
        .from('sims')
        .select('wallet_id, balance');
        
      if (simsError) throw simsError;
      
      // Calculate total balance for each wallet
      const walletBalances: Record<string, number> = {};
      
      simsData.forEach(sim => {
        if (!walletBalances[sim.wallet_id]) {
          walletBalances[sim.wallet_id] = 0;
        }
        walletBalances[sim.wallet_id] += sim.balance;
      });
      
      // Format wallet data
      const formattedWallets: Wallet[] = walletsData.map(wallet => {
        const balance = walletBalances[wallet.id] || 0;
        
        // Assign color based on color_class
        let borderColor = '';
        
        if (wallet.color_class) {
          if (wallet.color_class.includes('red')) {
            borderColor = 'border-red-500';
          } else if (wallet.color_class.includes('green')) {
            borderColor = 'border-green-500';
          } else if (wallet.color_class.includes('orange')) {
            borderColor = 'border-orange-500';
          } else if (wallet.color_class.includes('blue')) {
            borderColor = 'border-blue-500';
          }
        }
        
        return {
          id: wallet.id,
          name: wallet.name,
          balance: balance,
          color_class: borderColor
        };
      });
      
      setWallets(formattedWallets);
    } catch (error) {
      console.error("خطأ في جلب بيانات المحافظ:", error);
      toast.error("حدث خطأ أثناء تحميل بيانات المحافظ");
    } finally {
      setLoading(false);
    }
  };

  // Delete wallet handler
  const handleDeleteWallet = async () => {
    if (!deleteWalletId) return;
    
    try {
      // Check if wallet has linked sims
      const { data: sims, error: simsError } = await supabase
        .from('sims')
        .select('id')
        .eq('wallet_id', deleteWalletId);
      
      if (simsError) throw simsError;
      
      if (sims && sims.length > 0) {
        toast.error("لا يمكن حذف المحفظة لأنها تحتوي على شرائح مرتبطة بها");
        setIsDeleteDialogOpen(false);
        return;
      }

      // Get wallet name before deleting it
      const { data: walletData, error: walletError } = await supabase
        .from('wallets')
        .select('name')
        .eq('id', deleteWalletId)
        .single();

      if (walletError) throw walletError;
      
      const walletName = walletData?.name;

      // Delete wallet
      const { error } = await supabase
        .from('wallets')
        .delete()
        .eq('id', deleteWalletId);
        
      if (error) throw error;
      
      // Delete wallet commission if it exists
      if (walletName) {
        const { error: commissionError } = await supabase
          .from('wallet_commissions')
          .delete()
          .eq('wallet_name', walletName);
        
        if (commissionError) {
          console.error("خطأ في حذف عمولة المحفظة:", commissionError);
          // Don't throw here, as we've already deleted the wallet
        }
      }
      
      toast.success("تم حذف المحفظة بنجاح");
      setWallets(wallets.filter(wallet => wallet.id !== deleteWalletId));
    } catch (error) {
      console.error("خطأ في حذف المحفظة:", error);
      toast.error("حدث خطأ أثناء حذف المحفظة");
    } finally {
      setIsDeleteDialogOpen(false);
      setDeleteWalletId(null);
    }
  };
  
  const handleDeleteClick = (id: string) => {
    setDeleteWalletId(id);
    setIsDeleteDialogOpen(true);
  };

  useEffect(() => {
    fetchWallets();
  }, []);

  return {
    wallets,
    loading,
    deleteWalletId,
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    handleDeleteClick,
    handleDeleteWallet,
    fetchWallets
  };
};
