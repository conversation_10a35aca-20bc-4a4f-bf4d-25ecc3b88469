
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Database } from "lucide-react";
import { DownloadSchemaButton, DownloadRLSButton } from "./buttons";

interface DownloadStructureCardProps {
  isGenerating: boolean;
  setIsGenerating: (isGenerating: boolean) => void;
}

export default function DownloadStructureCard({ 
  isGenerating, 
  setIsGenerating 
}: DownloadStructureCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          تحميل هيكل قاعدة البيانات
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p className="mb-4 text-muted-foreground text-sm">
            قم بتحميل ملف SQL شامل يحتوي على كود إنشاء جميع الجداول، الأعمدة، المفاتيح، الفهارس، القيود، وسياسات الأمان (RLS) من قاعدة البيانات الحقيقية.
          </p>
          
          <DownloadSchemaButton 
            isGenerating={isGenerating}
            setIsGenerating={setIsGenerating}
          />
        </div>

        <div className="border-t pt-4">
          <p className="mb-4 text-muted-foreground text-sm">
            قم بتحميل ملف SQL خاص لإعداد سياسات أمان الصفوف (RLS) وتفعيل ميزة Realtime لجميع الجداول.
          </p>
          
          <DownloadRLSButton />
        </div>
      </CardContent>
    </Card>
  );
}
