
import { supabase } from "@/integrations/supabase/client";
import { TransactionType } from "@/utils/transaction/types";

/**
 * Calculate commission based on amount, transaction type and wallet ID
 * @param amount Transaction amount
 * @param type Transaction type (receive or send)
 * @param walletId Wallet ID
 * @returns Calculated commission amount
 */
export const calculateCommission = async (amount: number, type: TransactionType, walletId?: string): Promise<number> => {
  if (!amount || amount <= 0) return 0;
  
  try {
    // Check if this is a Fawry wallet first
    if (walletId) {
      const { data: walletData, error: walletError } = await supabase
        .from("wallets")
        .select("wallet_type")
        .eq("id", walletId)
        .single();
        
      if (!walletError && walletData) {
        console.log("Wallet type check:", walletData.wallet_type);
        
        if (walletData.wallet_type === "fawry") {
          // For Fawry wallets, return 0 because commission will be entered manually
          console.log("Fawry wallet detected - commission will be manual");
          return 0;
        }
      }
    }
    
    // Try to get wallet-specific commission settings
    if (walletId) {
      const { data: walletData, error: walletError } = await supabase
        .from("wallets")
        .select("name")
        .eq("id", walletId)
        .single();
        
      if (!walletError && walletData) {
        const walletName = walletData.name;
        
        // Get wallet-specific commission rate
        const { data: commissionData, error: commissionError } = await supabase
          .from("wallet_commissions")
          .select("*")
          .eq("wallet_name", walletName)
          .maybeSingle();
          
        if (!commissionError && commissionData) {
          const rate = type === 'receive' 
            ? commissionData.deposit_rate / 100 
            : commissionData.withdrawal_rate / 100;
            
          console.log(`Using wallet-specific commission rate ${rate} for ${amount}`);
          return Math.round(amount * rate * 100) / 100;
        }
      }
    }
    
    // Get default commission settings
    const { data, error } = await supabase
      .from("commission_settings")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(1)
      .single();
    
    if (error) {
      console.error("Error fetching commission settings:", error);
      
      // Create default commission settings if they don't exist
      if (error.code === 'PGRST116') {
        const defaultSettings = {
          default_deposit_rate: 0,
          default_withdrawal_rate: 1,
          apply_to_all: false
        };
        
        const { data: newSettings, error: insertError } = await supabase
          .from("commission_settings")
          .insert([defaultSettings])
          .select()
          .single();
          
        if (insertError) {
          console.error("Error creating default commission settings:", insertError);
          return calculateDefaultCommission(amount, type);
        }
        
        const rate = type === 'receive' 
          ? defaultSettings.default_deposit_rate / 100 
          : defaultSettings.default_withdrawal_rate / 100;
          
        return Math.round(amount * rate * 100) / 100;
      }
      
      // Use default values if there's an error
      return calculateDefaultCommission(amount, type);
    }
    
    // Calculate commission based on transaction type
    const rate = type === 'receive' 
      ? data.default_deposit_rate / 100 
      : data.default_withdrawal_rate / 100;
    
    console.log(`Calculating commission with rate ${rate} for ${amount}`);
    return Math.round(amount * rate * 100) / 100;
  } catch (error) {
    console.error("Error calculating commission:", error);
    // Use default values if there's an exception
    return calculateDefaultCommission(amount, type);
  }
};

/**
 * Calculate commission using default rates
 * @param amount Transaction amount
 * @param type Transaction type
 * @returns Default commission amount
 */
const calculateDefaultCommission = (amount: number, type: TransactionType): number => {
  // Default commission rates (0% for receive, 1% for send)
  const commissionRates = {
    receive: 0,
    send: 0.01,
  };
  
  return Math.round(amount * commissionRates[type] * 100) / 100;
};
