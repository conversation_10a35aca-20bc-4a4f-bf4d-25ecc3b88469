
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

export function useUserRoleCheck() {
  const { user } = useAuth();
  const [isAdminOrDeveloper, setIsAdminOrDeveloper] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkUserRole = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        // Check if user is admin or developer
        const { data, error } = await supabase
          .from("user_roles")
          .select("role")
          .eq("user_id", user.id)
          .single();
        
        if (error) {
          console.error("Error fetching user role:", error);
          toast.error("حدث خطأ أثناء التحقق من الصلاحيات");
          return;
        }
        
        // Check role
        if (data && (data.role === 'admin' || data.role === 'developer')) {
          setIsAdminOrDeveloper(true);
        } else {
          setIsAdminOrDeveloper(false);
        }
      } catch (err) {
        console.error("Error checking user role:", err);
      } finally {
        setLoading(false);
      }
    };
    
    checkUserRole();
  }, [user]);

  return { isAdminOrDeveloper, loading };
}
