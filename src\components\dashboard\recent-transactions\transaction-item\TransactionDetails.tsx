
import { Calendar, CreditCard } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { formatCurrency } from "@/utils/formatters";

interface TransactionDetailsProps {
  date: string;
  formatDate: (date: string) => string;
  walletName: string;
  commission: number;
}

export function TransactionDetails({ date, formatDate, walletName, commission }: TransactionDetailsProps) {
  const isMobile = useIsMobile();
  
  return (
    <div className={`flex flex-wrap gap-x-2 gap-y-1 ${isMobile ? 'mt-0.5' : 'mt-1'}`}>
      <div className={`flex items-center ${isMobile ? 'text-[10px]' : 'text-xs'} text-muted-foreground`}>
        <Calendar className={`${isMobile ? 'h-2.5 w-2.5' : 'h-3 w-3'} mr-1`} />
        {formatDate(date)}
      </div>
      <div className={`flex items-center ${isMobile ? 'text-[10px]' : 'text-xs'} text-muted-foreground`}>
        <CreditCard className={`${isMobile ? 'h-2.5 w-2.5' : 'h-3 w-3'} mr-1`} />
        {walletName}
      </div>
      {commission > 0 && (
        <div className={`${isMobile ? 'text-[10px]' : 'text-xs'} text-green-600 dark:text-green-400`}>
          عمولة: {formatCurrency(commission)}
        </div>
      )}
    </div>
  );
}
