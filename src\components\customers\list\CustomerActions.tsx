
import { Button } from "@/components/ui/button";
import { Edit, Trash } from "lucide-react";
import { Customer } from "@/pages/Customers";
import { useIsMobile } from "@/hooks/use-mobile";

interface CustomerActionsProps {
  customer: Customer;
  onEdit: () => void;
  onDelete: () => void;
}

export function CustomerActions({ customer, onEdit, onDelete }: CustomerActionsProps) {
  const isMobile = useIsMobile();
  
  return (
    <div className={isMobile ? "rtl-actions-group-mobile p-2" : "rtl-actions-group px-3 py-2"}>
      <Button
        variant="outline"
        size="sm"
        onClick={onEdit}
        title="تعديل"
        className={`rtl-action-button rtl-action-button-edit ${isMobile ? 'rtl-action-button-mobile' : ''}`}
      >
        <Edit className="h-4 w-4" />
        {!isMobile && <span>تعديل</span>}
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onDelete}
        title="حذف"
        className={`rtl-action-button rtl-action-button-destructive ${isMobile ? 'rtl-action-button-mobile' : ''}`}
      >
        <Trash className="h-4 w-4" />
        {!isMobile && <span>حذف</span>}
      </Button>
    </div>
  );
}
