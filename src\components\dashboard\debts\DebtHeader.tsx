
import { AlertCircle } from "lucide-react";
import { CardTitle } from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";

interface DebtHeaderProps {
  debtCount: number;
  overdueCount: number;
}

export function DebtHeader({ debtCount, overdueCount }: DebtHeaderProps) {
  const isMobile = useIsMobile();
  
  return (
    <div className="flex items-center justify-between">
      <CardTitle className={`text-lg flex items-center gap-2 ${isMobile ? 'text-base' : ''}`}>
        <AlertCircle className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'} text-red-500`} />
        الديون المستحقة والمتأخرة
      </CardTitle>
      {debtCount > 0 && (
        <div className="flex items-center gap-2">
          <span className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 text-xs px-2 py-1 rounded-full">
            {debtCount} ديون
          </span>
          <span className="bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 text-xs px-2 py-1 rounded-full">
            {overdueCount} متأخر
          </span>
        </div>
      )}
    </div>
  );
}
