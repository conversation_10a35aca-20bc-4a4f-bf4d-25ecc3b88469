
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { User } from "@/types/user.types";
import { processEdgeUsersData } from "./userPermissionsService";

/**
 * Fetches all users with their roles and permissions
 */
export const fetchAllUsers = async (): Promise<User[]> => {
  try {
    console.log("Fetching users data from edge function...");
    
    // Call the getUsersData edge function instead of admin.listUsers
    const { data: usersData, error } = await supabase.functions.invoke("getUsersData");
    
    if (error) {
      console.error("Error fetching users:", error);
      throw error;
    }
    
    console.log("Fetched users data from edge function:", usersData);
    
    if (!usersData || usersData.length === 0) {
      return [];
    }
    
    // Process the edge function data to add permissions
    const processedUsers = await processEdgeUsersData(usersData);
    
    console.log("Final processed users data:", processedUsers);
    return processedUsers;
  } catch (error: any) {
    console.error("Error fetching users:", error);
    toast.error("حدث خطأ أثناء جلب بيانات المستخدمين");
    throw error;
  }
};
