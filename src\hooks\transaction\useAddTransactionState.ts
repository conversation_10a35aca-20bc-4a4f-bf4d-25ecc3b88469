
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { transactionFormSchema } from '@/components/transactions/transactionSchema';
import { TransactionType } from '@/utils/transaction/types';

/**
 * Hook for managing the state of the add transaction form
 */
export function useAddTransactionState() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const [isCheckingLimit, setIsCheckingLimit] = useState(false);
  const [monthlyTotal, setMonthlyTotal] = useState(0);
  const [monthlyRemaining, setMonthlyRemaining] = useState(300000);
  const [monthlyLimitExceeded, setMonthlyLimitExceeded] = useState(false);
  const [isFawryWallet, setIsFawryWallet] = useState(false);
  const [selectedSimBalance, setSelectedSimBalance] = useState<number | null>(null);
  const [hasInsufficientBalance, setHasInsufficientBalance] = useState(false);
  const [isMobileSimType, setIsMobileSimType] = useState(false);
  const [exceedsMobileReceiveLimit, setExceedsMobileReceiveLimit] = useState(false);

  const form = useForm({
    resolver: zodResolver(transactionFormSchema),
    defaultValues: {
      transactionType: 'receive' as TransactionType,
      amount: 0,
      commission: 0,
      walletId: '',
      simId: '',
      customerName: '',
      customerPhone: '',
      description: '',
    },
  });

  return {
    form,
    isSubmitting,
    setIsSubmitting,
    isCalculating,
    setIsCalculating,
    isCheckingLimit,
    setIsCheckingLimit,
    monthlyTotal,
    setMonthlyTotal,
    monthlyRemaining,
    setMonthlyRemaining,
    monthlyLimitExceeded,
    setMonthlyLimitExceeded,
    isFawryWallet,
    setIsFawryWallet,
    selectedSimBalance,
    setSelectedSimBalance,
    hasInsufficientBalance,
    setHasInsufficientBalance,
    isMobileSimType,
    setIsMobileSimType,
    exceedsMobileReceiveLimit,
    setExceedsMobileReceiveLimit,
  };
}
