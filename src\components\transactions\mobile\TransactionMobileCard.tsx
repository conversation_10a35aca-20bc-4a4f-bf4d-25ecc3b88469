
import { formatCurrency, formatDate } from "@/utils/formatters";
import { Transaction } from "@/types/transaction.types";
import { TableCard, TableCardRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface TransactionMobileCardProps {
  transaction: Transaction;
}

export function TransactionMobileCard({ transaction }: TransactionMobileCardProps) {
  const transactionType = transaction.transaction_type === "receive" ? "استلام" : "سحب";
  const status = "completed"; // Default status
  const walletName = transaction.wallet?.name || "—";
  
  // Get customer name and phone
  const customerName = transaction.customer_name || transaction.customer?.name || "غير محدد";
  const customerPhone = transaction.customer_phone || transaction.customer?.phone || "—";
  
  // Construct title
  const shortId = transaction.id.substring(transaction.id.length - 4);
  
  return (
    <TableCard 
      title={`${customerName} - ${formatCurrency(transaction.amount)}`}
      className="rtl-mobile-card"
      actions={
        <div className="flex gap-2">
          <Badge 
            variant={transaction.transaction_type === "receive" ? "success" : "destructive"}
            className="rtl-badge text-xs"
          >
            {transactionType}
          </Badge>
        </div>
      }
    >
      <TableCardRow label="رقم المعاملة" value={`#${shortId}`} />
      <TableCardRow label="رقم الهاتف" value={customerPhone} />
      <TableCardRow label="المبلغ" value={formatCurrency(transaction.amount)} />
      {transaction.commission > 0 && (
        <TableCardRow label="العمولة" value={formatCurrency(transaction.commission)} />
      )}
      <TableCardRow label="المحفظة" value={walletName} />
      <TableCardRow label="التاريخ" value={formatDate(transaction.created_at)} />
      {transaction.description && (
        <TableCardRow label="الوصف" value={transaction.description} />
      )}
    </TableCard>
  );
}
