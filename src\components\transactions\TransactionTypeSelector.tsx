
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { ArrowDownSquare, ArrowUpSquare } from "lucide-react";
import { TransactionType } from "@/utils/transaction/types";

interface TransactionTypeSelectorProps {
  value: TransactionType | string;
  onChange: (value: TransactionType | string) => void;
}

export function TransactionTypeSelector({ value, onChange }: TransactionTypeSelectorProps) {
  return (
    <RadioGroup
      className="flex gap-4"
      value={value}
      onValueChange={onChange}
    >
      <div className="flex items-center space-x-2 space-x-reverse">
        <RadioGroupItem value="receive" id="receive" />
        <Label htmlFor="receive" className="flex items-center cursor-pointer gap-2">
          <div className="bg-green-100 dark:bg-green-900/30 p-1 rounded-md">
            <ArrowDownSquare className="h-4 w-4 text-green-500" />
          </div>
          <span>استلام</span>
        </Label>
      </div>
      
      <div className="flex items-center space-x-2 space-x-reverse">
        <RadioGroupItem value="send" id="send" />
        <Label htmlFor="send" className="flex items-center cursor-pointer gap-2">
          <div className="bg-red-100 dark:bg-red-900/30 p-1 rounded-md">
            <ArrowUpSquare className="h-4 w-4 text-red-500" />
          </div>
          <span>ارسال</span>
        </Label>
      </div>
    </RadioGroup>
  );
}
