import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Phone, MessageSquare, MessageCircle, Copy, Check } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
interface ContactMethodsProps {
  openExternalLink: (url: string) => void;
}
export function ContactMethods({
  openExternalLink
}: ContactMethodsProps) {
  const [copiedPhone, setCopiedPhone] = useState(false);
  const {
    toast
  } = useToast();

  // Function to copy phone number to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedPhone(true);
      toast({
        title: "تم نسخ الرقم بنجاح",
        description: `${text} تم نسخه إلى الحافظة`
      });

      // Reset the copied state after 2 seconds
      setTimeout(() => {
        set<PERSON><PERSON><PERSON><PERSON><PERSON>(false);
      }, 2000);
    }).catch(err => {
      toast({
        title: "فشل نسخ الرقم",
        description: "الرجاء المحاولة مرة أخرى",
        variant: "destructive"
      });
    });
  };
  return <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      {/* Phone Card */}
      <Card className="hover:shadow-lg transition-all duration-300 card hover-scale">
        <CardHeader className="text-center pb-2">
          <Phone className="h-8 w-8 mx-auto text-primary" />
          <CardTitle className="mt-2">اتصل بنا</CardTitle>
          <CardDescription>تواصل معنا عبر الهاتف</CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-xl font-semibold text-primary mb-4 text-center ltr:text-left">01029274616</p>
          <div className="flex gap-2">
            <Button variant="default" className="w-full" onClick={() => window.location.href = "tel:+201029274616"}>
              <Phone className="h-4 w-4 ml-2" /> اتصل الآن
            </Button>
            <Button variant="outline" onClick={() => copyToClipboard("01029274616")} className="w-full">
              {copiedPhone ? <>
                  <Check className="h-4 w-4 ml-2" /> تم النسخ
                </> : <>
                  <Copy className="h-4 w-4 ml-2" /> نسخ الرقم
                </>}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Telegram Card */}
      <Card className="hover:shadow-lg transition-all duration-300 card hover-scale">
        <CardHeader className="text-center pb-2">
          <MessageSquare className="h-8 w-8 mx-auto text-primary" />
          <CardTitle className="mt-2">تليجرام</CardTitle>
          <CardDescription>تواصل معنا عبر تليجرام</CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-xl font-semibold text-primary mb-4 text-center ltr:text-left">-------------------</p>
          <Button variant="default" className="w-full" onClick={() => openExternalLink("https://t.me/FoxExploit")}>
            <MessageSquare className="h-4 w-4 ml-2" /> فتح تليجرام
          </Button>
        </CardContent>
      </Card>

      {/* WhatsApp Card */}
      <Card className="hover:shadow-lg transition-all duration-300 card hover-scale">
        <CardHeader className="text-center pb-2">
          <MessageCircle className="h-8 w-8 mx-auto text-primary" />
          <CardTitle className="mt-2">واتساب</CardTitle>
          <CardDescription>تواصل معنا عبر واتساب</CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-xl font-semibold text-primary mb-4 text-center ltr:text-left">01029274616</p>
          <Button variant="default" className="w-full" onClick={() => openExternalLink("https://wa.me/201029274616")}>
            <MessageCircle className="h-4 w-4 ml-2" /> فتح واتساب
          </Button>
        </CardContent>
      </Card>
    </div>;
}