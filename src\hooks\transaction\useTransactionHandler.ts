
import { useState } from 'react';
import { toast } from 'sonner';
import { TransactionFormValues } from '@/components/transactions/transactionSchema';

/**
 * Hook for handling the transaction submission process
 */
export function useTransactionHandler({
  onClose,
}: {
  onClose: () => void;
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  /**
   * Handle the form submission
   */
  const handleTransactionSubmit = async (
    event: React.FormEvent,
    form: any,
    submitTransactionData: (data: any) => Promise<boolean>
  ) => {
    event.preventDefault();
    const isValid = await form.trigger();
    
    if (!isValid) return;
    
    setIsSubmitting(true);

    try {
      const formValues = form.getValues();
      
      const { transactionType, amount, commission, walletId, simId, customerName, customerPhone, description } = formValues;
      
      // تأكد من تضمين اسم العميل ورقم الهاتف في بيانات المعاملة
      const transactionData = {
        amount: Number(amount),
        commission: Number(commission),
        wallet_id: walletId,
        sim_id: simId,
        type: transactionType,
        customer_name: customerName,
        customer_phone: customerPhone || undefined,
        description: description || undefined,
      };
      
      console.log('Transaction data being sent:', transactionData);
      
      const success = await submitTransactionData(transactionData);
      
      if (success) {
        form.reset();
        onClose();
      }
    } catch (error) {
      console.error('Error submitting transaction:', error);
      toast.error('حدث خطأ أثناء إضافة العملية');
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSubmitting,
    setIsSubmitting,
    handleTransactionSubmit
  };
}
