
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from "@/components/ui/dialog";
import { ModernTable } from "@/components/ui/modern-table/ModernTable";
import { supabase } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { ArrowDown, ArrowUp } from "lucide-react";
import { formatCurrency } from "@/utils/formatters";

interface Wallet {
  id: string;
  name: string;
  balance: number;
}

interface Operation {
  id: string;
  wallet_id: string;
  operation_type: string;
  amount: number;
  commission: number;
  created_at: string;
}

interface ViewFawryWalletOperationsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  wallet: Wallet;
}

const ViewFawryWalletOperationsDialog: React.FC<ViewFawryWalletOperationsDialogProps> = ({
  isOpen,
  onOpenChange,
  wallet,
}) => {
  const [operations, setOperations] = useState<Operation[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchOperations = async () => {
    if (!isOpen || !wallet.id) return;
    
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("fawry_wallet_operations")
        .select("*")
        .eq("wallet_id", wallet.id)
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      setOperations(data || []);
    } catch (error) {
      console.error("Error fetching wallet operations:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOperations();
  }, [isOpen, wallet.id]);

  const getOperationIcon = (type: string) => {
    if (type === "استلام") {
      return <ArrowDown className="text-green-500 mr-1" size={18} />;
    }
    return <ArrowUp className="text-red-500 mr-1" size={18} />;
  };

  const columns = [
    {
      header: "نوع العملية",
      accessor: (row: Operation) => (
        <div className="flex items-center">
          {getOperationIcon(row.operation_type)}
          <span>{row.operation_type}</span>
        </div>
      ),
      mobile: true,
    },
    {
      header: "المبلغ",
      accessor: (row: Operation) => {
        // Apply color based on operation type
        const textColor = row.operation_type === "استلام" 
          ? "text-green-600 dark:text-green-400" 
          : "text-red-600 dark:text-red-400";
        
        return (
          <span className={textColor}>
            {formatCurrency(row.amount)}
          </span>
        );
      },
      mobile: true,
    },
    {
      header: "العمولة",
      accessor: (row: Operation) => formatCurrency(row.commission || 0),
    },
    {
      header: "التاريخ",
      accessor: (row: Operation) => format(new Date(row.created_at), 'yyyy-MM-dd HH:mm', { locale: ar }),
      mobile: true,
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>عمليات محفظة {wallet.name}</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <div className="mb-4 flex justify-between items-center">
            <span className="text-muted-foreground">الرصيد الحالي:</span>
            <span className="font-semibold">{formatCurrency(wallet.balance || 0)}</span>
          </div>
          
          <ModernTable
            data={operations}
            columns={columns}
            loading={loading}
            emptyMessage="لا توجد عمليات لهذه المحفظة"
            keyField="id"
            dir="rtl"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewFawryWalletOperationsDialog;
