
@layer components {
  /* Layout containers */
  .page-container {
    @apply container mx-auto p-4 md:p-6;
  }
  
  .flex-center {
    @apply flex items-center justify-center;
  }
  
  /* Grid layouts */
  .grid-auto-fit {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }
  
  /* RTL layout support */
  .rtl-grid {
    @apply grid grid-flow-row-dense;
  }
  
  /* Responsive containers */
  .responsive-container {
    @apply w-full mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }
  
  /* Layout for fixed sidebar and content */
  .layout-with-sidebar {
    @apply flex min-h-screen;
  }
  
  .sidebar-container {
    @apply w-64 flex-shrink-0;
  }
  
  .content-container {
    @apply flex-grow;
  }
  
  /* Stack elements with spacing */
  .stack {
    @apply space-y-4;
  }
  
  .stack-lg {
    @apply space-y-8;
  }
  
  /* Flexbox utilities */
  .flex-between {
    @apply flex justify-between items-center;
  }
  
  .flex-start {
    @apply flex justify-start items-center;
  }
  
  .flex-end {
    @apply flex justify-end items-center;
  }

  /* Dashboard specific responsive styles */
  .dashboard-responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4;
  }
  
  .dashboard-section {
    @apply my-6 space-y-6;
  }
  
  .responsive-card {
    @apply p-4 sm:p-6 overflow-hidden;
  }
  
  .card-header-responsive {
    @apply flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 mb-4;
  }
  
  /* Mobile optimizations */
  .mobile-scroll-container {
    @apply -mx-4 px-4 overflow-x-auto pb-4;
  }
  
  .mobile-full-width {
    @apply w-full min-w-full;
  }
  
  .mobile-card {
    @apply p-3 rounded-md border shadow-sm my-2;
  }
  
  .dashboard-card-animate {
    @apply transition-all duration-300 ease-in-out hover:shadow-md;
  }
  
  /* Theme toggle transition */
  .theme-transition {
    @apply transition-colors duration-300;
  }
  
  /* Modern table styles */
  .modern-table-container {
    @apply rounded-lg overflow-hidden border border-border;
  }
  
  .modern-table-gradient {
    @apply bg-gradient-to-tr from-background to-card;
  }
  
  /* Wallet grid for dashboard */
  .wallet-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4;
  }
}
