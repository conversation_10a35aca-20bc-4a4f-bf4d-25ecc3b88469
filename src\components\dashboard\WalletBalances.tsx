
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { WalletCard } from "./wallet/WalletCard";
import { WalletSkeleton } from "./wallet/WalletSkeleton";
import { useWalletData } from "./wallet/useWalletData";
import { formatCurrency } from "./wallet/formatters";
import { useIsMobile } from "@/hooks/use-mobile";

export function WalletBalances() {
  const { wallets, isLoading, isRefreshing, handleRefresh } = useWalletData();
  const isMobile = useIsMobile();
  
  if (isLoading) {
    return <WalletSkeleton />;
  }
  
  return (
    <>
      <div className={`flex justify-end mb-3 sm:mb-4 ${isMobile ? 'rtl-mobile-margin' : ''}`}>
        <Button 
          variant="outline" 
          size={isMobile ? "sm" : "default"}
          onClick={handleRefresh} 
          disabled={isRefreshing}
          className={`flex items-center gap-2 rtl-button theme-transition
                    bg-[#EF4343] hover:bg-[#EF4343]/90 text-white font-medium
                    dark:bg-[#D0AC19] dark:hover:bg-[#D0AC19]/90 dark:text-black dark:font-bold
                    ${isMobile ? 'text-xs py-1.5 px-2' : ''}`}
        >
          <RefreshCw className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} ${isRefreshing ? 'animate-spin' : ''}`} />
          <span className={`rtl-mobile-text ${isMobile ? 'text-xs' : ''}`}>تحديث البيانات</span>
        </Button>
      </div>
      <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 wallet-grid ${isMobile ? 'rtl-mobile-container' : ''}`}>
        {wallets.map(wallet => (
          <WalletCard 
            key={wallet.id} 
            wallet={wallet} 
            formatCurrency={formatCurrency} 
          />
        ))}
      </div>
    </>
  );
}
