
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageCircle, AlertTriangle, Loader2 } from "lucide-react";
import { useTelegramSettings } from "./useTelegramSettings";
import { TelegramFormInputs } from "./TelegramFormInputs";
import { TelegramFormActions } from "./TelegramFormActions";
import { Button } from "@/components/ui/button";
import { LoadingState } from "./LoadingState";
import { AccessDenied } from "./AccessDenied";

export function TelegramSettings() {
  const {
    isEnabled,
    setIsEnabled,
    botToken,
    setBotToken,
    chatId,
    setChatId,
    userId,
    setUserId,
    isSaving,
    isTesting,
    isCreatingFunction,
    isLoading,
    error,
    currentRole,
    roleLoading,
    handleSaveTelegramSettings,
    handleTestMessage,
    handleCreateEdgeFunction,
    reloadSettings
  } = useTelegramSettings();
  
  // If checking role or settings is still loading
  if (roleLoading || isLoading) {
    return <LoadingState />;
  }

  // If there was an error loading settings
  if (error) {
    return (
      <Card className="overflow-hidden border-b-4 border-b-red-500 shadow-sm">
        <CardHeader className="bg-gradient-to-r from-red-50 to-transparent dark:from-red-900/20 dark:to-transparent">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            خطأ في تحميل الإعدادات
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <p className="text-lg font-medium text-red-500 mb-4">{error}</p>
            <Button onClick={reloadSettings} className="bg-blue-500 hover:bg-blue-600">
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Not a developer
  if (currentRole !== 'developer') {
    return <AccessDenied />;
  }

  // Developer with successful settings load
  return (
    <Card className="overflow-hidden border-b-4 border-b-blue-500 shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-transparent dark:from-blue-900/20 dark:to-transparent">
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-blue-500" />
          إعدادات تليجرام
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <TelegramFormInputs 
          isEnabled={isEnabled}
          setIsEnabled={setIsEnabled}
          botToken={botToken}
          setBotToken={setBotToken}
          chatId={chatId}
          setChatId={setChatId}
          userId={userId}
          setUserId={setUserId}
        />
        
        <TelegramFormActions
          isSaving={isSaving}
          isTesting={isTesting}
          isCreatingFunction={isCreatingFunction}
          handleSave={handleSaveTelegramSettings}
          handleTest={handleTestMessage}
          handleCreateFunction={handleCreateEdgeFunction}
          botToken={botToken}
          chatId={chatId}
        />
      </CardContent>
    </Card>
  );
}
