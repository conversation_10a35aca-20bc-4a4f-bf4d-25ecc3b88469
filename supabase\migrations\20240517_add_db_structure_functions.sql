
-- Function to get all user tables in the database
CREATE OR REPLACE FUNCTION public.get_tables()
RETURNS TABLE(table_schema text, table_name text) 
LANGUAGE SQL SECURITY DEFINER
AS $$
  SELECT table_schema, table_name 
  FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE'
  ORDER BY table_name;
$$;

-- Function to get table definition
CREATE OR REPLACE FUNCTION public.get_table_definition(table_name text)
RETURNS TABLE(
  column_name text, 
  data_type text, 
  is_nullable text, 
  column_default text
) 
LANGUAGE SQL SECURITY DEFINER
AS $$
  SELECT column_name, 
         data_type, 
         is_nullable, 
         column_default
  FROM information_schema.columns 
  WHERE table_schema = 'public' 
  AND table_name = $1
  ORDER BY ordinal_position;
$$;

-- Function to get table constraints
CREATE OR REPLACE FUNCTION public.get_table_constraints(table_name text)
RETURNS TABLE(
  constraint_name text, 
  constraint_type text, 
  constraint_definition text
) 
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT tc.constraint_name, 
         tc.constraint_type,
         pg_get_constraintdef(pgc.oid) as constraint_definition
  FROM information_schema.table_constraints tc
  JOIN pg_constraint pgc ON tc.constraint_name = pgc.conname
  WHERE tc.table_schema = 'public'
  AND tc.table_name = $1;
END;
$$;
