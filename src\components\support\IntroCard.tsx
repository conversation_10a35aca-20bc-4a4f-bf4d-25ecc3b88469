
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export function IntroCard() {
  return (
    <Card className="mb-8 border-2 border-primary/50 shadow-lg theme-card">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-primary">تواصل معنا</CardTitle>
        <CardDescription className="text-lg font-bold">نحن هنا من أجلك دائمًا!</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-6">
          سواء كنت بحاجة إلى الاشتراك في خدماتنا أو تواجه مشكلة فنية، لا تتردد في التواصل معنا. 
          فريقنا متواجد لمساعدتك على مدار الساعة (24/24) وطوال أيام الأسبوع لضمان تجربة سلسة ومريحة.
          لأن راحتك أولويتنا.
        </p>
        <div className="flex flex-wrap gap-2 mt-2">
          <Badge variant="outline" className="bg-primary/10 text-primary">دعم على مدار الساعة</Badge>
          <Badge variant="outline" className="bg-primary/10 text-primary">استجابة سريعة</Badge>
          <Badge variant="outline" className="bg-primary/10 text-primary">خبراء متخصصون</Badge>
        </div>
      </CardContent>
    </Card>
  );
}
