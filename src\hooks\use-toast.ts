
import * as React from "react";
import {
  ToastActionElement,
  ToastProps,
  useToast as useToastFromUI,
} from "@/components/ui/toast";

export type ToastActionType = Omit<React.HTMLProps<HTMLButtonElement>, "size"> & {
  altText?: string;
};

export interface ToastType extends Omit<ToastProps, "action"> {
  id?: string;
  title?: string;
  description?: string;
  action?: ToastActionElement;
}

export const useToast = useToastFromUI;

type ToastFunction = (props: ToastType) => void;

export const toast: Record<"default" | "error" | "success" | "warning" | "info", ToastFunction> & {
  promise: <T>(
    promise: Promise<T>,
    options: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ) => Promise<T>;
} = {
  default: (props) => {
    const { toast } = useToastFromUI();
    toast({ ...props, variant: "default" });
  },
  error: (props) => {
    const { toast } = useToastFromUI();
    toast({ ...props, variant: "destructive" });
  },
  success: (props) => {
    const { toast } = useToastFromUI();
    toast({ ...props, variant: "success" });
  },
  warning: (props) => {
    const { toast } = useToastFromUI();
    toast({ ...props, variant: "warning" });
  },
  info: (props) => {
    const { toast } = useToastFromUI();
    toast({ ...props, variant: "info" });
  }
} as any; // Using 'as any' temporarily to allow us to add the promise method

// Add a promise method to toast
export const addPromiseToast = <T>(
  promise: Promise<T>,
  options: {
    loading: string;
    success: string | ((data: T) => string);
    error: string | ((error: any) => string);
  }
) => {
  const { toast } = useToastFromUI();
  const id = toast({ title: options.loading, variant: "default" });

  promise
    .then((data) => {
      const successMessage = typeof options.success === "function" ? options.success(data) : options.success;
      toast({ id, title: successMessage, variant: "success" });
    })
    .catch((error) => {
      const errorMessage = typeof options.error === "function" ? options.error(error) : options.error;
      toast({ id, title: errorMessage, variant: "destructive" });
    });
  
  return promise;
};

// Add the promise method to the toast object
Object.defineProperty(toast, "promise", {
  value: addPromiseToast,
});
