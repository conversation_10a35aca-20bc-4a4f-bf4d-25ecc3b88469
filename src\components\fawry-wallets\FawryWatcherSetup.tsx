
import { useEffect } from "react";
import { setupFawryWalletWatcher } from "@/hooks/notifications/fawryWalletWatcher";

// Track if watcher is active
let watcherActive = false;

const FawryWatcherSetup = () => {
  // Set up Fawry wallet notification watcher once
  useEffect(() => {
    if (!watcherActive) {
      watcherActive = true;
      const setupWatcher = async () => {
        try {
          await setupFawryWalletWatcher();
        } catch (error) {
          console.error('Error setting up Fawry wallet watcher:', error);
        }
        
        // Clean up when component unmounts
        return () => {
          console.log('🛑 Cleaning up Fawry watcher');
          watcherActive = false;
        };
      };
      
      const cleanup = setupWatcher();
      return () => {
        cleanup.then(cleanupFn => cleanupFn && cleanupFn());
      };
    }
  }, []);

  // This component doesn't render anything
  return null;
};

export default FawryWatcherSetup;
