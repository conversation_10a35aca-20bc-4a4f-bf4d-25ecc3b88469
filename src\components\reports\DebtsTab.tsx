import { useState, useEffect } from "react";
import { DateRange } from "react-day-picker";
import { supabase } from "@/integrations/supabase/client";
import { StatCards } from "@/components/reports/StatCards";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";
import { SimActivityTable } from "./SimActivityTable";

interface DebtsTabProps {
  dateRange: DateRange | undefined;
  branchId: string | null;
}

interface DebtData {
  id: string;
  customer_name: string;
  status: string;
  amount: number;
  due_date: string;
  paid_amount: number;
}

export function DebtsTab({
  dateRange,
  branchId
}: DebtsTabProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalTransactions: 0,
    totalAmount: 0,
    totalCommission: 0,
    growthRate: 0
  });
  const [debtsData, setDebtsData] = useState<DebtData[]>([]);
  const { toast } = useToast();
  const isMobile = useIsMobile();

  useEffect(() => {
    const fetchDebtsData = async () => {
      setIsLoading(true);
      try {
        // Convert dateRange to ISO string format for querying
        const fromDate = dateRange?.from ? dateRange.from.toISOString() : undefined;
        const toDate = dateRange?.to ? dateRange.to.toISOString() : undefined;

        // Build query for debts including customer info
        let query = supabase.from('debts').select('*, customer:customer_id(name)');

        // Apply date filters if they exist
        if (fromDate) {
          query = query.gte('created_at', fromDate);
        }
        if (toDate) {
          // Add a day to toDate to include the entire day
          const nextDay = new Date(dateRange!.to!);
          nextDay.setDate(nextDay.getDate() + 1);
          query = query.lt('created_at', nextDay.toISOString());
        }

        // Branch filter can't be applied directly to debts
        // In a real app, you would have a branch_id on debts or join through transactions

        const { data, error } = await query;

        if (error) {
          throw new Error(error.message);
        }

        if (data && data.length > 0) {
          // Calculate total stats
          const totalAmount = data.reduce((sum, debt) => sum + Number(debt.amount), 0);
          const paidAmount = data.filter(debt => debt.status !== 'pending').reduce((sum, debt) => sum + Number(debt.amount), 0);

          // Format the debts data
          const formattedDebts: DebtData[] = data.map(debt => ({
            id: debt.id,
            customer_name: debt.customer?.name || 'عميل غير معروف',
            status: debt.status,
            amount: Number(debt.amount),
            due_date: debt.due_date || '',
            paid_amount: debt.status === 'paid' ? Number(debt.amount) : debt.status === 'partial' ? Number(debt.amount) / 2 : 0
          }));

          setDebtsData(formattedDebts);
          setStats({
            totalTransactions: data.length,
            totalAmount,
            totalCommission: totalAmount * 0.05, // Estimate commission as 5% of debt amount
            growthRate: -2.5 // Mock value, would need previous period data
          });
        } else {
          setDebtsData([]);
          setStats({
            totalTransactions: 0,
            totalAmount: 0,
            totalCommission: 0,
            growthRate: 0
          });
        }
      } catch (error) {
        console.error('Error fetching debts data:', error);
        toast({
          title: "حدث خطأ",
          description: "لم نتمكن من جلب بيانات الديون. يرجى المحاولة مرة أخرى.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDebtsData();
  }, [dateRange, branchId, toast]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("ar-EG", {
      style: "currency",
      currency: "EGP",
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "غير محدد";
    return new Date(dateString).toLocaleDateString("ar-EG");
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "paid":
        return <Badge variant="success">مدفوع</Badge>;
      case "partial":
        return <Badge variant="warning">مدفوع جزئي</Badge>;
      default:
        return <Badge variant="outline">معلق</Badge>;
    }
  };

  // تعريف أعمدة جدول الديون
  const columns: TableColumn<DebtData>[] = [
    {
      header: "العميل",
      accessor: "customer_name",
      className: "font-medium"
    },
    {
      header: "المبلغ",
      accessor: (debt) => formatCurrency(debt.amount),
      className: "rtl-cell"
    },
    {
      header: "تاريخ الاستحقاق",
      accessor: (debt) => formatDate(debt.due_date),
      className: "rtl-cell"
    },
    {
      header: "المبلغ المدفوع",
      accessor: (debt) => formatCurrency(debt.paid_amount),
      className: "rtl-cell"
    },
    {
      header: "الحالة",
      accessor: (debt) => getStatusBadge(debt.status),
      className: "rtl-cell"
    }
  ];

  return (
    <div className="space-y-6">
      <StatCards stats={stats} isLoading={isLoading} />
      
      <Card>
        <CardHeader>
          <CardTitle>حالة الديون</CardTitle>
        </CardHeader>
        <CardContent>
          <ModernTable
            data={debtsData}
            columns={columns}
            loading={isLoading}
            keyField="id"
            emptyMessage="لا توجد بيانات متاحة للفترة المحددة"
            dir="rtl"
            mobileCardTitle={(debt) => `${debt.customer_name} - ${formatCurrency(debt.amount)}`}
            mobileCardActions={(debt) => getStatusBadge(debt.status)}
          />
        </CardContent>
      </Card>
    </div>
  );
}
