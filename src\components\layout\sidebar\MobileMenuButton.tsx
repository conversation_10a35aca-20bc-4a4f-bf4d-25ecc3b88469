
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";

interface MobileMenuButtonProps {
  toggleMobileSidebar: () => void;
}

export const MobileMenuButton = ({ toggleMobileSidebar }: MobileMenuButtonProps) => {
  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleMobileSidebar}
      className="fixed top-3 right-3 z-50 lg:hidden"
    >
      <Menu size={24} />
    </Button>
  );
};
