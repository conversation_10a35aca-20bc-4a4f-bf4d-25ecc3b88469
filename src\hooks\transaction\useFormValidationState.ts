
import { useEffect, useState } from "react";
import { TransactionType } from "@/utils/transaction/types";

export function useFormValidationState(
  transactionType: TransactionType,
  amount: number,
  simId: string,
  commission: number,
  sims: any[],
  isMobileSimType: boolean,
  walletId: string,
  customerName: string,
  customerPhone: string
) {
  const [insufficientBalance, setInsufficientBalance] = useState(false);
  const [isInactiveSim, setIsInactiveSim] = useState(false);
  const [isZeroBalance, setIsZeroBalance] = useState(false);
  const [exceedsLimit, setExceedsLimit] = useState(false);
  const [isFormComplete, setIsFormComplete] = useState(false);

  // Check for insufficient balance, zero balance, and mobile limit
  useEffect(() => {
    if (simId) {
      const selectedSim = sims.find(sim => sim.id === simId);

      // Check for inactive SIM
      setIsInactiveSim(selectedSim?.active_status === false);

      // Check for zero balance
      setIsZeroBalance(selectedSim?.balance === 0);

      // Check for insufficient balance in send transaction
      if (transactionType === 'send' && selectedSim?.balance !== undefined && amount > 0) {
        const totalAmount = Number(amount) + Number(commission);
        // تعديل هنا: نتحقق إذا كان المبلغ الكلي (المبلغ + العمولة) أكبر من الرصيد المتاح
        setInsufficientBalance(selectedSim.balance < totalAmount && selectedSim.balance > 0);
      } else {
        setInsufficientBalance(false);
      }

      // Check for mobile receive limit
      if (transactionType === 'receive' && isMobileSimType && amount > 300000) {
        setExceedsLimit(true);
      } else {
        setExceedsLimit(false);
      }
    } else {
      setIsInactiveSim(false);
      setIsZeroBalance(false);
      setInsufficientBalance(false);
      setExceedsLimit(false);
    }
  }, [transactionType, amount, simId, sims, isMobileSimType, commission]);

  // Check if all required fields are filled
  useEffect(() => {
    const requiredFieldsFilled = 
      amount > 0 && 
      walletId !== '' && 
      simId !== '' && 
      customerName.trim() !== '' && 
      customerPhone.trim() !== '';
    
    setIsFormComplete(requiredFieldsFilled);
  }, [amount, walletId, simId, customerName, customerPhone]);

  // تحديد ما إذا كان يجب تعطيل زر الإضافة
  const isButtonDisabled = 
    !isFormComplete ||
    insufficientBalance && transactionType === 'send' || 
    isInactiveSim || 
    isZeroBalance && transactionType === 'send' || 
    exceedsLimit && transactionType === 'receive';

  return {
    insufficientBalance,
    isInactiveSim,
    isZeroBalance,
    exceedsLimit,
    isFormComplete,
    isButtonDisabled
  };
}
