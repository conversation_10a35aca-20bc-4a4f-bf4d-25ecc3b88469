
import { Shield } from "lucide-react";
const AuthIllustration = () => {
  return <div className="flex flex-col items-center justify-center text-primary">
      <div className="relative mb-6">
        <img src="https://i.ibb.co/W47dWQ7D/cf6060f5-4b0d-46e3-8db3-cf87937a1185.png" alt="Cash Logo" className="w-32 h-32 object-contain z-10 relative" />
        <div className="absolute inset-0 bg-primary/5 rounded-full w-32 h-32 blur-lg"></div>
      </div>
      <h2 className="text-2xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
        Cash Wallet Pro
      </h2>
      <h3 className="text-lg font-medium text-center">نظام إدارة المحفظة</h3>
      <p className="text-muted-foreground text-center mt-2 max-w-xs">نظام متكامل لإدارة المحافظ الإلكترونية والمعاملات المالية</p>
      <div className="mt-8 flex items-center justify-center">
        <Shield className="h-6 w-6 text-primary/60" />
      </div>
    </div>;
};
export default AuthIllustration;
