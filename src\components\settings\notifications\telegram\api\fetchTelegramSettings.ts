
import { supabase } from "@/integrations/supabase/client";
import { TelegramSettings } from "../types";

export async function fetchTelegramSettings(): Promise<TelegramSettings | null> {
  try {
    const { data, error } = await supabase
      .from("telegram_settings")
      .select("*")
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // Table exists but no rows - this is expected for new installations
        console.log("No telegram settings found, will use defaults");
        return {
          isEnabled: false,
          botToken: "",
          chatId: "",
          userId: "",
        };
      }
      // Any other error is unexpected
      console.error("Error fetching Telegram settings:", error);
      throw error;
    }

    if (data) {
      return {
        isEnabled: data.is_enabled || false,
        botToken: data.bot_token || "",
        chatId: data.chat_id || "",
        userId: data.user_id || "",
      };
    }
    
    // If we get here with no data and no error, return defaults
    return {
      isEnabled: false,
      botToken: "",
      chatId: "",
      userId: "",
    };
  } catch (error) {
    console.error("Error fetching Telegram settings:", error);
    throw error;
  }
}
