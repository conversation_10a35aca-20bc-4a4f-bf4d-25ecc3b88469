
import { Button } from "@/components/ui/button";

interface FormActionsProps {
  loading: boolean;
  onCancel: () => void;
}

export function FormActions({ loading, onCancel }: FormActionsProps) {
  return (
    <div className="flex justify-end gap-2">
      <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
        إلغاء
      </Button>
      <Button type="submit" disabled={loading}>
        {loading ? "جاري الحفظ..." : "حفظ"}
      </Button>
    </div>
  );
}
