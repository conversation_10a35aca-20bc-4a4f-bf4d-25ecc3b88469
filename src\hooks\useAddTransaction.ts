
import { TransactionType } from '@/utils/transaction/types';
import { useTransactionForm } from './useTransactionForm';
import { useAddTransactionState } from './transaction/useAddTransactionState';
import { useMonthlyLimitCheck } from './transaction/useMonthlyLimitCheck';
import { useCommissionCalculation } from './transaction/useCommissionCalculation';
import { useWalletTypeCheck } from './transaction/useWalletTypeCheck';
import { useTransactionSubmission } from './transaction/useTransactionSubmission';
import { useTransactionCalculations } from './transaction/useTransactionCalculations';
import { useTransactionHandler } from './transaction/useTransactionHandler';

export function useAddTransaction({
  onClose,
}: {
  onClose: () => void;
}) {
  // Load form data
  const {
    wallets,
    sims,
    customers,
    isLoading,
  } = useTransactionForm();

  // Manage form state
  const {
    form,
    isSubmitting: formSubmitting,
    setIsSubmitting: setFormSubmitting,
    isCalculating: formCalculating,
    setIsCalculating: setFormCalculating,
    isCheckingLimit: formCheckingLimit,
    setIsCheckingLimit: setFormCheckingLimit,
    monthlyTotal: formMonthlyTotal,
    setMonthlyTotal: setFormMonthlyTotal,
    monthlyRemaining: formMonthlyRemaining,
    setMonthlyRemaining: setFormMonthlyRemaining,
    monthlyLimitExceeded: formMonthlyLimitExceeded,
    setMonthlyLimitExceeded: setFormMonthlyLimitExceeded,
    isFawryWallet: formIsFawryWallet,
    setIsFawryWallet: setFormIsFawryWallet,
    selectedSimBalance: formSelectedSimBalance,
    setSelectedSimBalance: setFormSelectedSimBalance,
    hasInsufficientBalance: formHasInsufficientBalance,
    setHasInsufficientBalance: setFormHasInsufficientBalance,
    isMobileSimType: formIsMobileSimType,
    setIsMobileSimType: setFormIsMobileSimType,
    exceedsMobileReceiveLimit: formExceedsMobileReceiveLimit,
    setExceedsMobileReceiveLimit: setFormExceedsMobileReceiveLimit,
  } = useAddTransactionState();

  // Hook for monthly limit checking
  const { 
    isCheckingLimit, 
    monthlyTotal, 
    monthlyRemaining, 
    monthlyLimitExceeded, 
    checkMonthlyLimits 
  } = useMonthlyLimitCheck();

  // Hook for commission calculation
  const { 
    isCalculating, 
    handleCalculateCommission 
  } = useCommissionCalculation();

  // Hook for wallet type checking
  const { 
    isFawryWallet, 
    checkWalletType 
  } = useWalletTypeCheck(wallets);

  // Hook for transaction submission
  const { 
    isSubmitting, 
    submitTransactionData 
  } = useTransactionSubmission();

  // Hook for transaction submission handling
  const {
    handleTransactionSubmit
  } = useTransactionHandler({
    onClose
  });

  // Watch form values
  const watchAmount = form.watch('amount');
  const watchWalletId = form.watch('walletId');
  const watchTransactionType = form.watch('transactionType') as TransactionType;
  const watchSimId = form.watch('simId');
  const watchCommission = form.watch('commission');

  // Apply transaction calculations and validations
  useTransactionCalculations({
    watchAmount,
    watchTransactionType,
    watchWalletId,
    watchSimId,
    watchCommission,
    formIsFawryWallet,
    formSelectedSimBalance,
    formIsMobileSimType,
    sims,
    wallets,
    form,
    setFormIsFawryWallet,
    setFormSelectedSimBalance, 
    setFormIsMobileSimType,
    setFormCheckingLimit,
    setFormMonthlyTotal,
    setFormMonthlyRemaining,
    setFormMonthlyLimitExceeded,
    setFormHasInsufficientBalance,
    setFormExceedsMobileReceiveLimit,
    checkWalletType,
    checkMonthlyLimits,
    handleCalculateCommission
  });

  // Handle form submission
  const onSubmit = (event: React.FormEvent) => {
    return handleTransactionSubmit(event, form, submitTransactionData);
  };

  return {
    form,
    isSubmitting: formSubmitting || isSubmitting,
    isCalculating: formCalculating || isCalculating,
    isCheckingLimit: formCheckingLimit || isCheckingLimit,
    isLoading,
    wallets,
    sims,
    customers,
    watchWalletId,
    selectedSimBalance: formSelectedSimBalance,
    isMobileSimType: formIsMobileSimType,
    monthlyLimitExceeded: formMonthlyLimitExceeded,
    monthlyTotal: formMonthlyTotal,
    monthlyRemaining: formMonthlyRemaining,
    hasInsufficientBalance: formHasInsufficientBalance,
    exceedsMobileReceiveLimit: formExceedsMobileReceiveLimit,
    isFawryWallet: formIsFawryWallet,
    onSubmit
  };
}
