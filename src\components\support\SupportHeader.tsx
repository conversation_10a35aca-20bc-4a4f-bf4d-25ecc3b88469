
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Moon, Sun } from "lucide-react";
import { User as CustomUser } from "@/types/user.types";
import { User } from "@supabase/supabase-js";
import { ThemeSwitcher } from "@/components/layout/ThemeSwitcher";

interface SupportHeaderProps {
  user: CustomUser | User | null;
}

export function SupportHeader({ user }: SupportHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
      <div>
        <h1 className="text-3xl font-bold mb-2">الدعم والاشتراك</h1>
        <p className="text-muted-foreground">نحن هنا لمساعدتك وتوفير أفضل تجربة لخدماتنا</p>
      </div>
      <div className="flex items-center gap-2">
        <ThemeSwitcher />
        <Link to={user ? '/' : '/auth'}>
          <Button variant="outline" size="sm" className="flex items-center gap-2 hover-scale">
            {user ? 'العودة للتطبيق' : 'تسجيل الدخول'}
            <ArrowRight className="h-4 w-4" />
          </Button>
        </Link>
      </div>
    </div>
  );
}
