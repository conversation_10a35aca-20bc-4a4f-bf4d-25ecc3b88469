
import { Employee } from "@/types/employee.types";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, Edit, Trash2 } from "lucide-react";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";

interface EmployeeTableProps {
  employees: Employee[];
  loading: boolean;
  getDepartmentName: (departmentId: string) => string;
  setViewingEmployee: (employee: Employee) => void;
  setEditingEmployee: (employee: Employee) => void;
  setDeletingEmployeeId: (id: string) => void;
}

export function EmployeeTable({
  employees,
  loading,
  getDepartmentName,
  setViewingEmployee,
  setEditingEmployee,
  setDeletingEmployeeId
}: EmployeeTableProps) {
  const getRoleTranslation = (role: string) => {
    switch (role) {
      case "manager": return "مدير";
      case "supervisor": return "مشرف";
      case "employee": return "موظف";
      case "accountant": return "محاسب";
      default: return role;
    }
  };

  const columns: TableColumn<Employee>[] = [
    {
      header: "الاسم",
      accessor: "name",
      className: "font-medium"
    },
    {
      header: "رقم الهاتف",
      accessor: (employee) => employee.phone || "—"
    },
    {
      header: "المنصب",
      accessor: (employee) => getRoleTranslation(employee.role)
    },
    {
      header: "القسم",
      accessor: (employee) => getDepartmentName(employee.department_id)
    },
    {
      header: "الحالة",
      accessor: (employee) => (
        <Badge
          variant={employee.status === "active" ? "default" : "secondary"}
        >
          {employee.status === "active" ? "نشط" : "غير نشط"}
        </Badge>
      )
    },
    {
      header: "إجراءات",
      accessor: (employee) => (
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setViewingEmployee(employee)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setEditingEmployee(employee)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDeletingEmployeeId(employee.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <ModernTable 
      data={employees}
      columns={columns}
      loading={loading}
      keyField="id"
      emptyMessage="لا توجد بيانات للعرض"
      dir="rtl"
      mobileCardTitle={(employee) => employee.name}
      mobileCardActions={(employee) => (
        <div className="flex gap-2">
          <Button variant="ghost" size="sm" onClick={() => setViewingEmployee(employee)}>
            <Eye className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={() => setEditingEmployee(employee)}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={() => setDeletingEmployeeId(employee.id)}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )}
    />
  );
}
