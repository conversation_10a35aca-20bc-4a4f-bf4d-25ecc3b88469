
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs";
import DatabaseStatus from "@/components/database-tools/DatabaseStatus";
import DatabaseBackup from "@/components/database-tools/DatabaseBackup";
import DatabaseStructure from "@/components/database-tools/DatabaseStructure";
import DangerZone from "@/components/database-tools/DangerZone";
import EdgeFunctions from "@/components/database-tools/EdgeFunctions";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent } from "@/components/ui/card";
import { Lock, Shield, Database, Save, Code, FileCode, AlertTriangle } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

export default function DatabaseTools() {
  const [currentRole, setCurrentRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const isMobile = useIsMobile();
  
  useEffect(() => {
    // Get the current user's role
    async function getUserRole() {
      setLoading(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          const { data: roleData, error } = await supabase
            .from("user_roles")
            .select("role")
            .eq("user_id", user.id)
            .single();
            
          if (error) throw error;
          setCurrentRole(roleData?.role || null);
        }
      } catch (error) {
        console.error("Error fetching user role:", error);
      } finally {
        setLoading(false);
      }
    }
    
    getUserRole();
  }, []);

  // Check if the user is a developer and can access the database tools
  const isDeveloper = currentRole === 'developer';

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </div>
    );
  }

  if (!isDeveloper) {
    return (
      <div className="p-6">
        <Card className="border-red-200 dark:border-red-800">
          <CardContent className="pt-6 pb-6">
            <div className="flex flex-col items-center justify-center text-center space-y-4">
              <div className="bg-red-100 dark:bg-red-900/20 p-3 rounded-full">
                <Lock className="h-8 w-8 text-red-500 dark:text-red-400" />
              </div>
              <h3 className="text-xl font-semibold text-red-600 dark:text-red-400">
                الوصول مقيد
              </h3>
              <p className="text-muted-foreground max-w-md">
                يمكن للمطورين فقط الوصول إلى أدوات قاعدة البيانات. هذا القسم محمي لضمان سلامة وأمان البيانات.
              </p>
              <div className="bg-amber-50 dark:bg-amber-900/10 p-4 rounded-md border border-amber-200 dark:border-amber-800 text-amber-800 dark:text-amber-300 text-sm">
                <div className="flex items-center gap-2 mb-1">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">ملاحظة أمان</span>
                </div>
                <p>
                  إذا كنت بحاجة إلى إجراء تغييرات على قاعدة البيانات، يرجى التواصل مع مطور النظام.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">أدوات قاعدة البيانات</h1>
      
      <Tabs defaultValue="status" dir="rtl" className="w-full">
        <TabsList className="grid grid-cols-5 mb-6">
          <TabsTrigger value="status" className="flex items-center justify-center gap-2 py-2">
            <Database className={isMobile ? "h-5 w-5" : "h-4 w-4 ml-1.5"} />
            {!isMobile && <span>حالة النظام</span>}
            {isMobile && <span className="sr-only">حالة النظام</span>}
          </TabsTrigger>
          
          <TabsTrigger value="backup" className="flex items-center justify-center gap-2 py-2">
            <Save className={isMobile ? "h-5 w-5" : "h-4 w-4 ml-1.5"} />
            {!isMobile && <span>النسخ الاحتياطي</span>}
            {isMobile && <span className="sr-only">النسخ الاحتياطي</span>}
          </TabsTrigger>
          
          <TabsTrigger value="structure" className="flex items-center justify-center gap-2 py-2">
            <FileCode className={isMobile ? "h-5 w-5" : "h-4 w-4 ml-1.5"} />
            {!isMobile && <span>هيكل قاعدة البيانات</span>}
            {isMobile && <span className="sr-only">هيكل قاعدة البيانات</span>}
          </TabsTrigger>
          
          <TabsTrigger value="functions" className="flex items-center justify-center gap-2 py-2">
            <Code className={isMobile ? "h-5 w-5" : "h-4 w-4 ml-1.5"} />
            {!isMobile && <span>وظائف Edge</span>}
            {isMobile && <span className="sr-only">وظائف Edge</span>}
          </TabsTrigger>
          
          <TabsTrigger value="danger" className="flex items-center justify-center gap-2 py-2">
            <AlertTriangle className={isMobile ? "h-5 w-5" : "h-4 w-4 ml-1.5"} />
            {!isMobile && <span>خاص بالمطور</span>}
            {isMobile && <span className="sr-only">خاص بالمطور</span>}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="status">
          <DatabaseStatus />
        </TabsContent>
        
        <TabsContent value="backup">
          <DatabaseBackup />
        </TabsContent>
        
        <TabsContent value="structure">
          <DatabaseStructure />
        </TabsContent>
        
        <TabsContent value="functions">
          <EdgeFunctions />
        </TabsContent>
        
        <TabsContent value="danger">
          <DangerZone />
        </TabsContent>
      </Tabs>
    </div>
  );
}
