
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, FileEdit, Eye, Trash2 } from "lucide-react";
import { Debt } from "@/types/debt.types";
import { useIsMobile } from "@/hooks/use-mobile";

interface DebtActionsProps {
  debt: Debt;
  onEdit: (debt: Debt) => void;
  onView: (debt: Debt) => void;
  onDelete: (debtId: string) => void;
  onMarkPaid: (debtId: string) => void;
}

export function DebtActions({ 
  debt, 
  onEdit, 
  onView, 
  onDelete, 
  onMarkPaid 
}: DebtActionsProps) {
  const isMobile = useIsMobile();

  // On mobile, display buttons in a more compact format
  if (isMobile) {
    return (
      <div className="flex flex-wrap gap-2 justify-center items-center my-1">
        {debt.status !== 'paid' && (
          <Button
            onClick={() => onMarkPaid(debt.id)}
            variant="subtle"
            size="icon-sm"
            className="bg-green-600 text-white hover:bg-green-700 shadow-sm"
            aria-label="تمّ الدفع"
          >
            <CheckCircle className="h-3.5 w-3.5" />
          </Button>
        )}
        <Button
          onClick={() => onView(debt)}
          variant="outline"
          size="icon-sm"
          className="text-blue-600 border-blue-200 hover:bg-blue-50 hover:text-blue-700 dark:text-blue-400 dark:border-blue-500/30 dark:hover:bg-blue-500/20"
          aria-label="عرض"
        >
          <Eye className="h-3.5 w-3.5" />
        </Button>
        <Button
          onClick={() => onEdit(debt)}
          variant="outline"
          size="icon-sm"
          className="text-amber-500 border-amber-200 hover:bg-amber-50 hover:text-amber-600 dark:text-amber-400 dark:border-amber-500/30 dark:hover:bg-amber-500/20"
          aria-label="تعديل"
        >
          <FileEdit className="h-3.5 w-3.5" />
        </Button>
        <Button
          onClick={() => onDelete(debt.id)}
          variant="outline"
          size="icon-sm"
          className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:border-red-500/30 dark:hover:bg-red-500/20"
          aria-label="حذف"
        >
          <Trash2 className="h-3.5 w-3.5" />
        </Button>
      </div>
    );
  }

  // Desktop view
  return (
    <div className="flex items-center gap-2 rtl:space-x-reverse">
      {debt.status !== 'paid' && (
        <Button
          onClick={() => onMarkPaid(debt.id)}
          variant="subtle"
          size="sm"
          className="bg-green-600 text-white hover:bg-green-700 shadow-sm flex items-center gap-1.5 dark:bg-green-700 dark:hover:bg-green-600"
          aria-label="تمّ الدفع"
        >
          <CheckCircle className="h-3.5 w-3.5" />
          <span>تمّ الدفع</span>
        </Button>
      )}
      <Button
        onClick={() => onView(debt)}
        variant="outline"
        size="sm"
        className="text-blue-600 border-blue-200 hover:bg-blue-50 hover:text-blue-700 dark:text-blue-400 dark:border-blue-500/30 dark:hover:bg-blue-500/20 flex items-center gap-1.5"
        aria-label="عرض"
      >
        <Eye className="h-3.5 w-3.5" />
        <span>عرض</span>
      </Button>
      <Button
        onClick={() => onEdit(debt)}
        variant="outline"
        size="sm"
        className="text-amber-500 border-amber-200 hover:bg-amber-50 hover:text-amber-600 dark:text-amber-400 dark:border-amber-500/30 dark:hover:bg-amber-500/20 flex items-center gap-1.5"
        aria-label="تعديل"
      >
        <FileEdit className="h-3.5 w-3.5" />
        <span>تعديل</span>
      </Button>
      <Button
        onClick={() => onDelete(debt.id)}
        variant="outline"
        size="sm"
        className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:border-red-500/30 dark:hover:bg-red-500/20 flex items-center gap-1.5"
        aria-label="حذف"
      >
        <Trash2 className="h-3.5 w-3.5" />
        <span>حذف</span>
      </Button>
    </div>
  );
}
