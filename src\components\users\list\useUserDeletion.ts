
import { useState } from "react";
import { User } from "@/types/user.types";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

// حساب المطور الذي لا يمكن تعديله أو حذفه
const DEVELOPER_EMAIL = "<EMAIL>";

export const useUserDeletion = (onUserUpdate: () => void) => {
  const [deletingUser, setDeletingUser] = useState<User | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const confirmDelete = (user: User) => {
    // التحقق من أن المستخدم ليس حساب المطور
    if (user.email === DEVELOPER_EMAIL) {
      toast.error("لا يمكن حذف حساب المطور");
      return;
    }
    setDeletingUser(user);
  };

  const cancelDelete = () => {
    setDeletingUser(null);
  };

  const deleteUser = async (userId: string) => {
    setIsDeleting(true);
    
    try {
      console.log("Deleting user with ID:", userId);
      
      // Call the edge function using supabase.functions.invoke instead of direct fetch
      const { data, error } = await supabase.functions.invoke('delete-user', {
        body: { userId }
      });
      
      if (error) {
        throw new Error(error.message || "فشل حذف المستخدم");
      }
      
      // Also delete user permissions and roles
      await Promise.all([
        supabase.from('user_permissions').delete().eq('user_id', userId),
        supabase.from('user_roles').delete().eq('user_id', userId)
      ]);
      
      toast.success("تم حذف المستخدم بنجاح");
      onUserUpdate();
    } catch (error: any) {
      console.error("Error deleting user:", error);
      toast.error(error.message || "حدث خطأ أثناء حذف المستخدم");
    } finally {
      setIsDeleting(false);
      setDeletingUser(null);
    }
  };

  return {
    deletingUser,
    isDeleting,
    confirmDelete,
    cancelDelete,
    deleteUser
  };
};
