
import React from "react";
import { formatDate } from "@/utils/formatters";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, CheckCircle, Info } from "lucide-react";

interface NotificationLogProps {
  id: string;
  user_id: string;
  message: string;
  type: string;
  status: string;
  created_at: string;
}

export function NotificationMobileCard({ 
  id, 
  user_id,
  message, 
  type,
  status,
  created_at 
}: NotificationLogProps) {
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case "sent":
        return "bg-green-500 hover:bg-green-600";
      case "failed":
        return "bg-red-500 hover:bg-red-600";
      case "pending":
        return "bg-yellow-500 hover:bg-yellow-600";
      default:
        return "bg-blue-500 hover:bg-blue-600";
    }
  };

  const getIcon = () => {
    if (status === 'failed') return <AlertTriangle className="h-5 w-5 text-destructive" />;
    
    switch (type.toLowerCase()) {
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  return (
    <div className="mobile-card glass-card animate-fade-in mb-3 border-2 border-[#ea384c] dark:border-amber-500">
      <div className="p-4">
        <div className="flex gap-3 items-start">
          <div className="mt-0.5">
            {getIcon()}
          </div>
          <div className="flex-1">
            <div className="flex justify-between items-start">
              <p className="text-sm font-medium break-words">{message}</p>
              <Badge
                className={`${getStatusColor(status)} ml-2 shrink-0`}
                variant="default"
              >
                {status === "sent" && "تم الإرسال"}
                {status === "failed" && "فشل"}
                {status === "pending" && "قيد الإنتظار"}
              </Badge>
            </div>
            
            <div className="mt-2 grid grid-cols-2 gap-2 text-xs text-muted-foreground">
              <div>
                <span className="block font-medium">المستخدم</span>
                <span dir="ltr" className="rtl-mobile-numbers">{user_id}</span>
              </div>
              <div>
                <span className="block font-medium">النوع</span>
                <span>{type === "telegram" ? "تليجرام" : type}</span>
              </div>
              <div>
                <span className="block font-medium">التاريخ</span>
                <span>{formatDate(created_at)}</span>
              </div>
              <div>
                <span className="block font-medium">المعرف</span>
                <span dir="ltr" className="rtl-mobile-numbers">{id.substring(0, 8)}...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
