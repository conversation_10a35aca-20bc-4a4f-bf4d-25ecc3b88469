
import { Transaction } from "@/types/transaction.types";
import { formatCurrency, formatDate } from "@/utils/formatters";
import { Badge } from "@/components/ui/badge";
import { TableCard, TableCardRow } from "@/components/ui/table";
import { ArrowDown, ArrowUp } from "lucide-react";

interface TransactionMobileCardsProps {
  transactions: Transaction[];
  loading: boolean;
}

export function TransactionMobileCards({
  transactions,
  loading
}: TransactionMobileCardsProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        {Array(3).fill(0).map((_, i) => (
          <div key={i} className="animate-pulse bg-muted rounded-lg p-4 space-y-3">
            <div className="h-5 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          </div>
        ))}
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8 border rounded-lg">
        لا توجد معاملات مطابقة للبحث
      </div>
    );
  }

  return (
    <div className="space-y-3 px-1">
      {transactions.map((transaction) => (
        <TableCard 
          key={transaction.id}
          title={`${transaction.customer?.name || 'عميل'} - ${formatCurrency(Number(transaction.amount))}`}
          className="effect-card glass-effect rtl-card rtl-mobile-card w-full mx-auto max-w-[95%] md:max-w-full"
          style={{
            transform: "translateZ(0)",
            overflow: "visible"
          }}
          actions={
            <div className="flex items-center gap-1">
              <Badge 
                variant={transaction.transaction_type === "receive" ? "success" : "destructive"}
                className="rtl-badge text-xs"
              >
                {transaction.transaction_type === "receive" ? "استلام" : "سحب"}
              </Badge>
            </div>
          }
        >
          <div className={`animated-border ${transaction.transaction_type === "receive" ? "effect-green" : "effect-red"}`}></div>
          <TableCardRow 
            label="الحالة" 
            value={
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 text-xs">
                مكتملة
              </Badge>
            } 
          />
          <TableCardRow 
            label="المحفظة" 
            value={transaction.wallet?.name || "غير محدد"} 
          />
          <TableCardRow 
            label="النوع" 
            value={
              <div className="flex items-center gap-1">
                {transaction.transaction_type === "receive" ? (
                  <>
                    <span>استلام</span>
                    <ArrowDown className="h-3 w-3 text-green-500" />
                  </>
                ) : (
                  <>
                    <span>سحب</span>
                    <ArrowUp className="h-3 w-3 text-red-500" />
                  </>
                )}
              </div>
            } 
          />
          <TableCardRow 
            label="المبلغ" 
            value={formatCurrency(Number(transaction.amount))} 
          />
          <TableCardRow 
            label="العمولة" 
            value={formatCurrency(Number(transaction.commission))} 
          />
          <TableCardRow 
            label="التاريخ" 
            value={formatDate(transaction.created_at)} 
          />
          {transaction.description && (
            <TableCardRow 
              label="الوصف" 
              value={transaction.description} 
            />
          )}
        </TableCard>
      ))}
    </div>
  );
}
