
import { Operation, OperationFormValues } from "./operationSchema";
import { calculateBalanceChange, calculateNewBalance, validateSufficientBalance } from "./balanceCalculations";
import { getWalletBalance, updateOperation, updateWalletBalance } from "./fawryOperationDBUtils";
import { ToastType } from "@/hooks/use-toast";

/**
 * Updates an operation with the same wallet
 */
export const updateOperationWithSameWallet = async (
  operation: Operation,
  values: OperationFormValues,
  toast: (props: ToastType) => void
): Promise<boolean> => {
  try {
    // Calculate the change in balance from original operation
    const originalBalanceChange = calculateBalanceChange(operation.operation_type, operation.amount);
    
    // Calculate the change in balance from the new operation
    const newBalanceChange = calculateBalanceChange(values.operation_type, values.amount);
    
    // Get current wallet balance
    const currentBalance = await getWalletBalance(operation.wallet_id);
    
    if (currentBalance === null) {
      toast({
        variant: "destructive",
        title: "خطأ في الحصول على رصيد المحفظة",
        description: "لا يمكن تحديث العملية في هذه اللحظة",
      });
      return false;
    }
    
    // Calculate new balance after update
    const newBalance = calculateNewBalance(currentBalance, originalBalanceChange, newBalanceChange);
    
    // For withdrawals, check if there's enough balance
    if (values.operation_type === "إرسال" && newBalance < 0) {
      toast({
        variant: "destructive",
        title: "رصيد غير كافي",
        description: "لا يمكن إجراء عملية الإرسال لأن الرصيد غير كافي",
      });
      return false;
    }
    
    // Update operation in the database
    const operationUpdated = await updateOperation(operation.id, {
      operation_type: values.operation_type,
      amount: values.amount,
      commission: values.commission || 0,
      wallet_id: values.wallet_id,
    });
    
    if (!operationUpdated) {
      toast({
        variant: "destructive",
        title: "خطأ في تحديث العملية",
        description: "حدث خطأ أثناء تحديث العملية",
      });
      return false;
    }
    
    // Update wallet balance
    const balanceUpdated = await updateWalletBalance(operation.wallet_id, newBalance);
    
    if (!balanceUpdated) {
      toast({
        variant: "destructive",
        title: "خطأ في تحديث الرصيد",
        description: "حدث خطأ أثناء تحديث رصيد المحفظة",
      });
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Error updating operation with same wallet:", error);
    toast({
      variant: "destructive",
      title: "خطأ في التحديث",
      description: "حدث خطأ غير متوقع أثناء تحديث العملية",
    });
    return false;
  }
};

/**
 * Updates an operation with a different wallet
 */
export const updateOperationWithDifferentWallet = async (
  operation: Operation,
  values: OperationFormValues,
  toast: (props: ToastType) => void
): Promise<boolean> => {
  try {
    // Calculate the change in balance from original operation
    const originalBalanceChange = calculateBalanceChange(operation.operation_type, operation.amount);
    
    // Calculate the change in balance from the new operation
    const newBalanceChange = calculateBalanceChange(values.operation_type, values.amount);
    
    // Get current balance for both original and new wallets
    const originalWalletBalance = await getWalletBalance(operation.wallet_id);
    const newWalletBalance = await getWalletBalance(values.wallet_id);
    
    if (originalWalletBalance === null || newWalletBalance === null) {
      toast({
        variant: "destructive",
        title: "خطأ في الحصول على رصيد المحفظة",
        description: "لا يمكن تحديث العملية في هذه اللحظة",
      });
      return false;
    }
    
    // Calculate new balances after update
    const updatedOriginalWalletBalance = originalWalletBalance - originalBalanceChange;
    const updatedNewWalletBalance = newWalletBalance + newBalanceChange;
    
    // For withdrawals, check if there's enough balance in new wallet
    if (values.operation_type === "إرسال" && !validateSufficientBalance(newWalletBalance, values.amount)) {
      toast({
        variant: "destructive",
        title: "رصيد غير كافي",
        description: "المحفظة الجديدة لا تملك رصيد كافي لإجراء عملية الإرسال",
      });
      return false;
    }
    
    // Update operation in the database
    const operationUpdated = await updateOperation(operation.id, {
      operation_type: values.operation_type,
      amount: values.amount,
      commission: values.commission || 0,
      wallet_id: values.wallet_id,
    });
    
    if (!operationUpdated) {
      toast({
        variant: "destructive",
        title: "خطأ في تحديث العملية",
        description: "حدث خطأ أثناء تحديث العملية",
      });
      return false;
    }
    
    // Update original wallet balance
    const originalWalletUpdated = await updateWalletBalance(operation.wallet_id, updatedOriginalWalletBalance);
    
    if (!originalWalletUpdated) {
      toast({
        variant: "destructive",
        title: "خطأ في تحديث الرصيد",
        description: "حدث خطأ أثناء تحديث رصيد المحفظة الأصلية",
      });
      return false;
    }
    
    // Update new wallet balance
    const newWalletUpdated = await updateWalletBalance(values.wallet_id, updatedNewWalletBalance);
    
    if (!newWalletUpdated) {
      toast({
        variant: "destructive",
        title: "خطأ في تحديث الرصيد",
        description: "حدث خطأ أثناء تحديث رصيد المحفظة الجديدة",
      });
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Error updating operation with different wallet:", error);
    toast({
      variant: "destructive",
        title: "خطأ في التحديث",
        description: "حدث خطأ غير متوقع أثناء تحديث العملية",
      });
    return false;
  }
};
