
/**
 * Downloads the provided backup data as a JSON file
 */
export const downloadBackupFile = (backupData: any, type = 'regular') => {
  try {
    // Convert to JSON and create downloadable file
    const data = JSON.stringify(backupData, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // Create filename with current date in format YYYY-MM-DD_HH-MM
    const date = new Date();
    const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}_${String(date.getHours()).padStart(2, '0')}-${String(date.getMinutes()).padStart(2, '0')}`;
    
    // Different filename based on backup type
    const filename = type === 'full_project' 
      ? `full_project_backup_${formattedDate}.json` 
      : `cash_database_backup_${formattedDate}.json`;
    
    link.setAttribute('download', filename);
    
    document.body.appendChild(link);
    link.click();
    link.remove();
    
    return true;
  } catch (error) {
    console.error("Error downloading backup:", error);
    throw error;
  }
};
