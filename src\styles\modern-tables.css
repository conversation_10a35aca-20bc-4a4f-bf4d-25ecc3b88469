
@layer utilities {
  /* تنسيقات عصرية للجداول */
  .modern-table {
    @apply rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm;
  }
  
  /* حواف مميزة للجداول - الحواف اليمنى واليسرى سميكة، العلوية والسفلية أنحف */
  .modern-table th,
  .modern-table td {
    @apply relative;
  }
  
  /* الحدود اليمنى واليسرى السميكة للجدول */
  .modern-table-borders::before,
  .modern-table-borders::after {
    content: "";
    @apply absolute top-0 bottom-0 w-[2px] bg-primary/40 dark:bg-primary/40;
  }
  
  .modern-table-borders::before {
    @apply right-0;
  }
  
  .modern-table-borders::after {
    @apply left-0;
  }
  
  /* الحدود العلوية والسفلية الأنحف للجدول */
  .modern-table-borders {
    @apply border-t border-b border-primary/20 dark:border-primary/20;
  }
  
  /* التدرجات اللونية للجداول */
  .modern-table-gradient {
    background: linear-gradient(to bottom right, var(--table-gradient-start), var(--table-gradient-end));
  }
  
  /* تنسيقات الجداول المتجاوبة */
  .responsive-table {
    @apply w-full overflow-hidden;
  }
  
  /* تنسيقات للخلايا في الوضع المتجاوب */
  .responsive-cell {
    @apply p-3;
  }
  
  /* تنسيقات الخطوط السفلية للصفوف */
  .table-row-border {
    @apply border-b border-primary/10 dark:border-primary/10;
  }
  
  /* تأثير التحويم على صفوف الجدول */
  .modern-table tr:hover {
    @apply bg-primary/5 dark:bg-primary/10 transition-colors duration-200;
  }
  
  /* التباين الألوان بين الصفوف البديلة */
  .modern-table tr:nth-child(even) {
    @apply bg-muted/20 dark:bg-muted/10;
  }
  
  /* تنسيقات للجداول المتجاوبة على الأجهزة الصغيرة */
  @media (max-width: 768px) {
    .card-view-table {
      @apply block border-0 shadow-none;
    }
    
    .card-view-table thead {
      @apply hidden;
    }
    
    .card-view-table tbody {
      @apply block w-full;
    }
    
    .card-view-table tr {
      @apply block rounded-lg border border-gray-200 dark:border-gray-700 mb-4 shadow-sm;
    }
    
    .card-view-table td {
      @apply block text-right p-3 before:content-[attr(data-label)] before:float-right before:font-medium 
                 before:text-muted-foreground before:mr-2;
    }
  }
  
  /* تنسيقات حواف جديدة للجداول */
  .elegant-table {
    @apply rounded-lg overflow-hidden border-[1px];
    border-left: 2px solid theme('colors.red.500');
    border-right: 2px solid theme('colors.red.500');
    border-top: 1px solid theme('colors.red.300');
    border-bottom: 1px solid theme('colors.red.300');
  }
  
  .dark .elegant-table {
    border-left: 2px solid theme('colors.yellow.400');
    border-right: 2px solid theme('colors.yellow.400');
    border-top: 1px solid theme('colors.yellow.600');
    border-bottom: 1px solid theme('colors.yellow.600');
  }
  
  /* تنسيقات الرأس للجدول */
  .elegant-table thead {
    @apply bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-200;
  }
  
  /* تنسيقات العناوين في رأس الجدول */
  .elegant-table th {
    @apply py-3 px-4 font-semibold text-sm border-b-2 border-red-400 dark:border-yellow-500;
  }
  
  /* تنسيقات خلايا الجدول */
  .elegant-table td {
    @apply py-3 px-4 border-b border-gray-100 dark:border-gray-700;
  }
  
  /* تأثير عند التحويم على صفوف الجدول */
  .elegant-table tbody tr:hover {
    @apply bg-red-50 dark:bg-yellow-900/10 transition-colors duration-200;
  }
  
  /* تنسيقات حواف الجدول العصرية */
  .modern-table-container {
    @apply relative rounded-xl overflow-hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  
  .modern-table-container::before {
    content: '';
    @apply absolute top-0 bottom-0 left-0 w-[2px] bg-red-500 dark:bg-yellow-400 z-10;
  }
  
  .modern-table-container::after {
    content: '';
    @apply absolute top-0 bottom-0 right-0 w-[2px] bg-red-500 dark:bg-yellow-400 z-10;
  }
  
  .modern-table-container .rtl-table {
    @apply border-y border-gray-200 dark:border-gray-700;
    border-top-width: 1px;
    border-bottom-width: 1px;
  }
  
  /* تنسيقات الخلايا داخل الجدول المتميز */
  .modern-table-gradient td {
    @apply border-b border-gray-100/50 dark:border-gray-800/50;
  }
  
  /* تصميمات البطاقات للأجهزة المحمولة */
  .mobile-card {
    @apply rounded-xl border p-4 relative overflow-hidden animate-fade-in;
    border-left: 2px solid theme('colors.red.500');
    border-right: 2px solid theme('colors.red.500');
    border-top: 1px solid theme('colors.red.300');
    border-bottom: 1px solid theme('colors.red.300');
  }
  
  .dark .mobile-card {
    border-left: 2px solid theme('colors.yellow.400');
    border-right: 2px solid theme('colors.yellow.400');
    border-top: 1px solid theme('colors.yellow.600');
    border-bottom: 1px solid theme('colors.yellow.600');
  }
  
  /* تنسيقات الصفوف في البطاقات */
  .table-card-row {
    @apply py-2 border-b border-gray-100 dark:border-gray-800 last:border-0;
  }
}
