
@layer utilities {
  /* تنسيقات عامة للثيمات */
  .theme-transition {
    transition: all 0.2s ease-in-out !important;
  }
  
  .dark .rtl-table-card {
    border-color: var(--border) !important;
    background-color: var(--card) !important;
  }
  
  /* تحسينات للبطاقات والجداول بدعم الثيمات */
  .theme-card {
    background-color: var(--card) !important;
    border-color: var(--border) !important;
    color: var(--card-foreground) !important;
    transition: all 0.2s ease-in-out !important;
  }
  
  /* تنسيقات الأزرار مع دعم الثيمات - وضع النهار */
  .rtl-icon-button:hover {
    background-color: var(--accent) !important;
    color: var(--accent-foreground) !important;
    opacity: 0.9;
  }
  
  /* تنسيقات الأزرار مع دعم الثيمات - وضع الليل */
  .dark .rtl-icon-button {
    color: var(--muted-foreground) !important;
  }
  
  .dark .rtl-icon-button:hover {
    background-color: var(--secondary) !important;
    color: var(--secondary-foreground) !important;
  }
  
  /* تحسينات للأزرار الأساسية */
  .theme-button {
    background-color: var(--primary) !important;
    color: var(--primary-foreground) !important;
    transition: all 0.2s ease-in-out !important;
  }
  
  .theme-button:hover {
    opacity: 0.9 !important;
  }
  
  .dark .theme-button {
    background-color: var(--primary) !important;
    color: var(--primary-foreground) !important;
  }
  
  .dark .theme-button:hover {
    opacity: 0.8 !important;
  }
  
  /* تنسيقات إضافية للأزرار المخططة */
  .theme-button-outline {
    border-color: var(--border) !important;
    color: var(--foreground) !important;
    transition: all 0.2s ease-in-out !important;
  }
  
  .theme-button-outline:hover {
    background-color: var(--muted) !important;
  }
  
  .dark .theme-button-outline {
    border-color: var(--border) !important;
    color: var(--foreground) !important;
  }
  
  .dark .theme-button-outline:hover {
    background-color: var(--secondary) !important;
  }
  
  /* تنسيقات البطاقات الأساسية */
  .card {
    border: 2px solid #ff4d6d !important; /* الأحمر للوضع النهاري */
  }
  
  .dark .card {
    border: 2px solid #d4af37 !important; /* الذهبي للوضع الليلي */
  }
}
