
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { TransactionType } from "@/utils/transaction/types";

// Update interfaces to match the actual database schema
export interface Wallet {
  id: string;
  name: string;
  color_class?: string;
  // Add active_status property
  active_status?: boolean;
  // Add computed display property
  displayType?: string;
  // Add wallet_type property to identify Fawry wallets
  wallet_type?: string;
}

export interface Sim {
  id: string;
  number: string;
  wallet_id: string;
  // Add computed display property
  displayProvider?: string;
  // Add balance property
  balance?: number;
  // Add active_status property
  active_status?: boolean;
  // Add receive_limit property to match the database schema
  receive_limit?: number;
  // Add send_limit property for completeness
  send_limit?: number;
}

interface Customer {
  id: string;
  name: string;
  phone: string | null;
}

export function useTransactionForm() {
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [sims, setSims] = useState<Sim[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch wallets from the database with wallet_type included
        const { data: walletsData, error: walletsError } = await supabase
          .from("wallets")
          .select("id, name, color_class, wallet_type");
        
        if (walletsError) {
          console.error("Error fetching wallets:", walletsError);
        }

        // Fetch sims from the database - now including balance and active_status
        const { data: simsData, error: simsError } = await supabase
          .from("sims")
          .select("id, number, wallet_id, balance, active_status");
        
        if (simsError) {
          console.error("Error fetching sims:", simsError);
        }

        // Fetch customers from the database
        const { data: customersData, error: customersError } = await supabase
          .from("customers")
          .select("id, name, phone");
        
        if (customersError) {
          console.error("Error fetching customers:", customersError);
        }

        // Process wallets data
        if (walletsData) {
          const processedWallets = walletsData.map(wallet => ({
            id: wallet.id,
            name: wallet.name,
            color_class: wallet.color_class,
            wallet_type: wallet.wallet_type, // Include wallet_type for Fawry check
            active_status: true, // Set default to true as the column doesn't exist in DB
            // Calculate displayType from name (for UI display only)
            displayType: wallet.name.split(' ')[0] || ''
          }));
          setWallets(processedWallets);
        } else {
          // Fallback data
          setWallets([
            { id: "wallet1", name: "فودافون كاش", displayType: "فودافون", active_status: true, wallet_type: "vodafone_cash" },
            { id: "wallet2", name: "اتصالات كاش", displayType: "اتصالات", active_status: true, wallet_type: "etisalat_cash" },
            { id: "wallet3", name: "أورانج كاش", displayType: "أورانج", active_status: true, wallet_type: "orange_cash" },
            { id: "wallet4", name: "انستاباي", displayType: "انستاباي", active_status: false, wallet_type: "instapay" },
            { id: "wallet5", name: "محفظة فوري", displayType: "فوري", active_status: true, wallet_type: "fawry" },
          ]);
        }
        
        // Process sims data
        if (simsData) {
          const processedSims = simsData.map(sim => {
            // Extract provider from sim number prefix for UI display
            let displayProvider = '';
            if (sim.number.startsWith('010')) displayProvider = 'فودافون';
            else if (sim.number.startsWith('011')) displayProvider = 'اتصالات';
            else if (sim.number.startsWith('012')) displayProvider = 'أورانج';
            
            return {
              id: sim.id,
              number: sim.number,
              wallet_id: sim.wallet_id,
              balance: sim.balance,
              active_status: sim.active_status !== undefined ? sim.active_status : true, // Use true as default if not specified
              displayProvider
            };
          });
          setSims(processedSims);
        } else {
          // Fallback data
          setSims([
            { id: "sim1", number: "01029274545", wallet_id: "wallet1", displayProvider: "فودافون", balance: 1000, active_status: true },
            { id: "sim2", number: "01154896321", wallet_id: "wallet2", displayProvider: "اتصالات", balance: 2000, active_status: true },
            { id: "sim3", number: "01287412563", wallet_id: "wallet3", displayProvider: "أورانج", balance: 1500, active_status: false },
          ]);
        }
        
        if (customersData) {
          setCustomers(customersData);
        } else {
          setCustomers([
            { id: "cust1", name: "أحمد محمد", phone: "01012345678" },
            { id: "cust2", name: "سارة علي", phone: "01112345678" },
            { id: "cust3", name: "محمود خالد", phone: "01212345678" },
          ]);
        }
      } catch (error) {
        console.error("Error loading form data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return {
    wallets,
    sims,
    customers,
    isLoading,
  };
}
