
@layer utilities {
  /* تنسيقات متقدمة للتجاوب مع اتجاه RTL */
  
  /* تحسينات عامة للتجاوب في الأجهزة المحمولة */
  [dir="rtl"] .rtl-mobile-container {
    padding: 1rem !important;
    text-align: right !important;
  }
  
  /* تنسيقات الهوامش المتجاوبة */
  @media (max-width: 768px) {
    [dir="rtl"] .rtl-mobile-margin {
      margin-right: 0.5rem !important;
      margin-left: 0.5rem !important;
    }
    
    [dir="rtl"] .rtl-mobile-text {
      font-size: 0.9rem !important;
      line-height: 1.4 !important;
    }
    
    /* تعديلات التباعد للشاشات الصغيرة في RTL */
    [dir="rtl"] .rtl-mobile-padding {
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
    }
    
    /* تحسين ظهور الأزرار على الشاشات الصغيرة */
    [dir="rtl"] .rtl-mobile-button {
      font-size: 0.85rem !important;
      padding: 0.4rem 0.75rem !important;
    }
    
    /* تحسين عرض البطاقات في الشاشات الصغيرة */
    [dir="rtl"] .rtl-mobile-card-spacing {
      margin-bottom: 1rem !important;
    }
    
    /* تحسين موضع زر القائمة الجانبية */
    [dir="rtl"] .mobile-sidebar-toggle {
      position: fixed !important;
      top: 0.75rem !important;
      right: 0.75rem !important;
      z-index: 50 !important;
    }
  }
  
  /* تحسين توزيع العناصر في الشاشات الصغيرة */
  @media (max-width: 640px) {
    [dir="rtl"] .rtl-stack-vertical {
      display: flex !important;
      flex-direction: column !important;
      gap: 0.75rem !important;
      align-items: flex-start !important;
    }
    
    /* تحسين انسيابية الأزرار في الشاشات الصغيرة */
    [dir="rtl"] .rtl-mobile-actions {
      display: flex !important;
      flex-direction: row !important;
      gap: 0.5rem !important;
      justify-content: space-between !important;
      width: 100% !important;
      margin-top: 0.75rem !important;
    }
    
    /* تنسيقات للجداول في الشاشات الصغيرة جداً */
    [dir="rtl"] .rtl-mobile-table {
      font-size: 0.8rem !important;
    }
    
    /* تعديل حجم وشكل البطاقات على الهواتف */
    [dir="rtl"] .rtl-mobile-card {
      padding: 0.75rem !important;
      margin-bottom: 0.75rem !important;
      border-radius: 0.5rem !important;
    }
    
    /* تحسين قراءة الأرقام على الشاشات الصغيرة */
    [dir="rtl"] .rtl-mobile-numbers {
      font-size: 0.9rem !important;
      direction: ltr !important;
      display: inline-block !important;
    }
  }
  
  /* تحسين تجربة إدخال النصوص في نماذج RTL */
  [dir="rtl"] .rtl-input {
    text-align: right !important;
    padding-right: 1rem !important;
    padding-left: 2rem !important;
  }
  
  /* تنسيق أيقونات الإدخال */
  [dir="rtl"] .rtl-input-icon {
    right: auto !important;
    left: 0.75rem !important;
  }
  
  /* تحسين تنسيقات الأرقام والقيم المالية */
  [dir="rtl"] .rtl-currency {
    direction: ltr !important; /* الأرقام تظهر من اليسار لليمين حتى في RTL */
    display: inline-block !important;
  }
  
  /* تحسين عرض المخططات البيانية في RTL */
  [dir="rtl"] .rtl-chart-container {
    direction: ltr !important; /* المخططات دائماً تعرض من اليسار لليمين */
    text-align: right !important;
  }
  
  /* تنسيقات لتوسيط العناصر في الشاشات الصغيرة */
  @media (max-width: 480px) {
    [dir="rtl"] .rtl-mobile-center {
      display: flex !important;
      justify-content: center !important;
      width: 100% !important;
    }
    
    [dir="rtl"] .rtl-mobile-full-width {
      width: 100% !important;
    }
    
    /* تحسين قابلية قراءة العناوين */
    [dir="rtl"] .rtl-mobile-heading {
      font-size: 1.1rem !important;
      line-height: 1.3 !important;
      margin-bottom: 0.5rem !important;
    }
    
    /* تنسيق خاص للإحصائيات على الشاشات الصغيرة */
    [dir="rtl"] .rtl-mobile-stats {
      display: grid !important;
      grid-template-columns: 1fr !important;
      gap: 0.75rem !important;
    }
    
    /* تحسين موضع زر القائمة الجانبية في الشاشات الصغيرة جداً */
    [dir="rtl"] .mobile-sidebar-toggle-xs {
      top: 0.5rem !important;
      right: 0.5rem !important;
    }
  }
}
