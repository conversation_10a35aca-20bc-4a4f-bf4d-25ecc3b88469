
import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { useFawryOperations } from "./hooks/useFawryOperations";
import { useAuth } from "@/contexts/AuthContext";
import { useUserRoleCheck } from "@/hooks/useUserRoleCheck";
import { useOperationActions } from "./hooks/useOperationActions";
import OperationsActions from "./components/OperationsActions";
import OperationsTable from "./components/OperationsTable";
import AddFawryOperationDialog from "./AddFawryOperationDialog";
import DeleteAllOperationsDialog from "./DeleteAllOperationsDialog";
import EditFawryOperationDialog from "./EditFawryOperationDialog";
import DeleteFawryOperationDialog from "./DeleteFawryOperationDialog";

interface FawryOperationsProps {
  refreshKey?: number;
  onOperationAdded?: () => void;
}

const FawryOperations: React.FC<FawryOperationsProps> = ({ refreshKey = 0, onOperationAdded }) => {
  const { wallets, operations, loading, fetchOperations } = useFawryOperations(refreshKey);
  const { isAdminOrDeveloper } = useUserRoleCheck();
  const { 
    addDialogOpen, 
    deleteAllDialogOpen,
    editDialogOpen,
    deleteDialogOpen,
    currentOperationId,
    setAddDialogOpen, 
    setDeleteAllDialogOpen,
    setEditDialogOpen,
    setDeleteDialogOpen,
    handleAddOperation,
    handleDeleteAll,
  } = useOperationActions();

  const handleOperationAdded = () => {
    fetchOperations();
    if (onOperationAdded) {
      onOperationAdded();
    }
  };

  const handleOperationUpdated = () => {
    fetchOperations();
  };

  // Find the current operation by ID when needed
  const currentOperation = currentOperationId 
    ? operations.find(op => op.id === currentOperationId) 
    : undefined;

  return (
    <>
      <Card>
        <CardHeader className="flex flex-col gap-4">
          {/* Moved the actions to appear above the title */}
          <div className="w-full flex justify-end">
            <OperationsActions
              isAdminOrDeveloper={isAdminOrDeveloper}
              onAddOperation={handleAddOperation}
              onDeleteAllOperations={handleDeleteAll}
            />
          </div>
          <CardTitle className="text-xl">عمليات محافظ فوري</CardTitle>
        </CardHeader>
        <OperationsTable
          operations={operations}
          loading={loading}
        />
      </Card>

      {/* Add operation dialog */}
      <AddFawryOperationDialog
        isOpen={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        wallets={wallets}
        onOperationAdded={handleOperationAdded}
      />

      {/* Edit operation dialog */}
      {currentOperation && (
        <EditFawryOperationDialog
          isOpen={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          operation={currentOperation}
          wallets={wallets}
          onOperationUpdated={handleOperationUpdated}
        />
      )}

      {/* Delete operation dialog */}
      {currentOperation && (
        <DeleteFawryOperationDialog
          isOpen={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          operation={currentOperation}
          onOperationDeleted={handleOperationUpdated}
        />
      )}

      {/* Delete all operations dialog */}
      <DeleteAllOperationsDialog
        isOpen={deleteAllDialogOpen}
        onOpenChange={setDeleteAllDialogOpen}
        onOperationsDeleted={fetchOperations}
      />
    </>
  );
};

export default FawryOperations;
