
import { ArrowDownSquare, ArrowUpSquare } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface TransactionIconProps {
  transactionType: string;
}

export function TransactionIcon({ transactionType }: TransactionIconProps) {
  const isMobile = useIsMobile();
  const isReceive = transactionType === 'receive';
  
  return (
    <div className={`p-1.5 sm:p-2 rounded-md ${
      isReceive
        ? 'bg-green-100 dark:bg-green-900/30' 
        : 'bg-red-100 dark:bg-red-900/30'
    }`}>
      {isReceive ? (
        <ArrowDownSquare className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-green-500 dark:text-green-400`} />
      ) : (
        <ArrowUpSquare className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-red-500 dark:text-red-400`} />
      )}
    </div>
  );
}
