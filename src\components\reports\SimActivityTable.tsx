
import React from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";

export interface SimActivity {
  id: string;
  sim_number: string;
  total_transactions: number;
  total_amount: number;
  total_commission: number;
  total_received: number;
  total_sent: number;
}

interface SimActivityTableProps {
  data: SimActivity[];
  isLoading: boolean;
}

export function SimActivityTable({
  data,
  isLoading
}: SimActivityTableProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("ar-EG", {
      style: "currency",
      currency: "EGP",
      maximumFractionDigits: 0
    }).format(amount);
  };

  const columns: TableColumn<SimActivity>[] = [
    {
      header: "الشريحة",
      accessor: "sim_number",
      className: "font-medium text-right"
    },
    {
      header: "عدد العمليات",
      accessor: "total_transactions",
      className: "text-right"
    },
    {
      header: "إجمالي المبالغ",
      accessor: (item) => (
        <span className="rtl-currency">{formatCurrency(item.total_amount)}</span>
      ),
      className: "text-right"
    },
    {
      header: "إجمالي العمولة",
      accessor: (item) => (
        <span className="rtl-currency">{formatCurrency(item.total_commission)}</span>
      ),
      className: "text-right"
    },
    {
      header: "اجمالي المستلم",
      accessor: (item) => (
        <span className="text-green-600 rtl-currency">{formatCurrency(item.total_received)}</span>
      ),
      className: "text-right"
    },
    {
      header: "اجمالي المرسل",
      accessor: (item) => (
        <span className="text-red-600 rtl-currency">{formatCurrency(item.total_sent)}</span>
      ),
      className: "text-right font-bold"
    }
  ];

  return (
    <ModernTable
      data={data}
      columns={columns}
      loading={isLoading}
      keyField="id"
      emptyMessage="لا توجد بيانات متاحة للفترة المحددة"
      dir="rtl"
      mobileCardTitle={(item) => `الشريحة: ${item.sim_number}`}
    />
  );
}
