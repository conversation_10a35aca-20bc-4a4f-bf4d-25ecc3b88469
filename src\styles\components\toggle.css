
/* Custom Toggle Button Styling */
.toggle-special-customer {
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid transparent;
}

.toggle-special-customer[data-state="on"] {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.toggle-special-customer[data-state="off"] {
  background-color: #e5e7eb;
  color: #4b5563;
}

.dark .toggle-special-customer[data-state="off"] {
  background-color: #374151;
  color: #e5e7eb;
}

.toggle-special-customer:hover {
  transform: translateY(-1px);
}

.toggle-special-customer:active {
  transform: translateY(0);
}
