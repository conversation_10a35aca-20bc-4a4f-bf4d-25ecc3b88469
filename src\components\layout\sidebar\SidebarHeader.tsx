
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, X } from "lucide-react";

interface SidebarHeaderProps {
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  isMobile: boolean;
  setIsMobileOpen: (open: boolean) => void;
  isRTL: boolean;
}

export const SidebarHeader = ({
  isCollapsed,
  setIsCollapsed,
  isMobile,
  setIsMobileOpen,
  isRTL,
}: SidebarHeaderProps) => {
  return (
    <div className="p-4 flex justify-between items-center h-16 border-b">
      {!isCollapsed && (
        <h1 className="font-bold text-lg animate-slide-in">نظام Cash Wallet Pro</h1>
      )}
      {isMobile ? (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsMobileOpen(false)}
          className="ml-auto"
        >
          <X size={18} />
        </Button>
      ) : (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className={!isCollapsed && isRTL ? "mr-auto" : !isCollapsed ? "ml-auto" : ""}
        >
          {isCollapsed ? (
            <ChevronLeft size={18} />
          ) : (
            <ChevronRight size={18} />
          )}
        </Button>
      )}
    </div>
  );
};
