
import { SidebarLink } from "./SidebarLink";
import { navigationItems } from "./navigationData";

interface SidebarContentProps {
  isCollapsed: boolean;
  handleMobileLinkClick: () => void;
}

export const SidebarContent = ({ isCollapsed, handleMobileLinkClick }: SidebarContentProps) => {
  return (
    <nav className="flex-1 py-4 px-2 space-y-1 sidebar-content overflow-y-auto">
      {navigationItems.map((item) => (
        <SidebarLink
          key={item.path}
          to={item.path}
          icon={item.icon}
          label={item.label}
          isCollapsed={isCollapsed}
          onClick={handleMobileLinkClick}
        />
      ))}
    </nav>
  );
};
