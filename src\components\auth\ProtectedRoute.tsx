
import { useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { checkUserPermission } from "@/services/user/userPermissionsService";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
}

const ProtectedRoute = ({ children, requiredPermission }: ProtectedRouteProps) => {
  const { user, loading } = useAuth();
  const location = useLocation();
  const [hasPermission, setHasPermission] = useState<boolean>(true); 
  const [checkingPermission, setCheckingPermission] = useState<boolean>(!!requiredPermission);

  console.log("ProtectedRoute rendered for path:", location.pathname, "requiring permission:", requiredPermission);

  // Check user permission when component mounts or user changes
  useEffect(() => {
    const checkPermission = async () => {
      if (user && requiredPermission) {
        try {
          console.log(`Checking permission "${requiredPermission}" for user ${user.id} on path ${location.pathname}`);
          
          // تحقق أولا إذا كان المستخدم مطورًا أو مديرًا
          const { data: userRole, error: roleError } = await supabase
            .from("user_roles")
            .select("role")
            .eq("user_id", user.id);
            
          if (roleError) {
            console.error("Error checking user role:", roleError);
          }
          
          // منح صلاحيات للمطور والمدير تلقائيًا
          if (userRole && userRole.length > 0) {
            const role = userRole[0].role;
            if (role === 'developer' || role === 'admin') {
              console.log(`User is ${role}, granting all permissions`);
              setHasPermission(true);
              setCheckingPermission(false);
              return;
            }
          }
          
          // استخدم دالة التحقق من الصلاحيات للمستخدمين العاديين
          console.log("User is not admin/developer, checking specific permission");
          const hasAccess = await checkUserPermission(user.id, requiredPermission as any);
          
          console.log(`Permission check result for "${requiredPermission}":`, hasAccess);
          setHasPermission(hasAccess);
          
          // إذا لم يكن لدى المستخدم صلاحيات كافية، اعرض رسالة
          if (!hasAccess) {
            toast.error("لا تملك صلاحيات كافية للوصول إلى هذه الصفحة");
          }
        } catch (err) {
          console.error("Error in checkPermission:", err);
          setHasPermission(false);
        }
        
        setCheckingPermission(false);
      } else if (!requiredPermission) {
        // إذا لم يكن هناك صلاحية مطلوبة، امنح الوصول
        console.log("No specific permission required, granting access");
        setHasPermission(true);
        setCheckingPermission(false);
      } else {
        // إذا لم يكن هناك مستخدم، قم بتعيين حالة التحقق إلى false
        setCheckingPermission(false);
      }
    };

    if (user) {
      checkPermission();
    } else {
      setCheckingPermission(false);
    }
  }, [user, requiredPermission, location.pathname]);

  // If still loading auth state or checking permission, show a loading spinner
  if (loading || checkingPermission) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!user) {
    console.log("User not authenticated, redirecting to auth");
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // إذا كان المستخدم مصادق عليه ولكن ليس لديه الصلاحيات المطلوبة، عرض رسالة خطأ
  if (requiredPermission && !hasPermission) {
    console.log(`User lacks required permission "${requiredPermission}", redirecting to home`);
    return (
      <div className="min-h-screen flex items-center justify-center flex-col">
        <h1 className="text-2xl font-bold mb-4">خطأ في الوصول</h1>
        <p className="text-lg mb-6">لا تملك صلاحيات كافية للوصول إلى هذه الصفحة</p>
        <Navigate to="/" replace />
      </div>
    );
  }

  // إذا كان المستخدم مصادق عليه ولديه الصلاحيات، عرض المحتوى المحمي
  console.log(`Access granted for path ${location.pathname}`);
  return <>{children}</>;
};

export default ProtectedRoute;
