
import { Card, CardContent, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { CheckCircle2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export function SupportAbout() {
  return (
    <Card className="mt-8 border-t-4 border-t-primary">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle2 className="h-5 w-5 text-primary" />
          عن خدمة الدعم
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">
          فريق الدعم الفني متاح للمساعدة في جميع الاستفسارات المتعلقة بالتطبيق. نحن نعمل على مدار الساعة لضمان تجربة مستخدم سلسة وفعالة.
          في حال واجهتك أي مشكلة أو كان لديك أي استفسار، لا تتردد في التواصل معنا عبر أي من قنوات الاتصال المذكورة أعلاه.
        </p>
      </CardContent>
      <CardFooter className="flex justify-center border-t pt-4">
        <Badge variant="outline" className="bg-primary/10 text-primary px-4 py-2">متاح 24/7 لخدمتك</Badge>
      </CardFooter>
    </Card>
  );
}
