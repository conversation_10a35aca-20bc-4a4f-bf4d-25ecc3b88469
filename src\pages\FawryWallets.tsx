
import { useState } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { DashboardStats } from "@/components/dashboard/DashboardStats";
import FawryWalletTabs from "@/components/fawry-wallets/FawryWalletTabs";
import FawryWatcherSetup from "@/components/fawry-wallets/FawryWatcherSetup";
import { useFawryStats } from "@/hooks/useFawryStats";
import { useUserRoleCheck } from "@/hooks/useUserRoleCheck";
import { toast } from "sonner";
import { formatCurrency } from "@/utils/formatters";

const FawryWallets = () => {
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState("operations");
  
  // Use a refresh key to trigger re-fetching of data
  const [refreshKey, setRefreshKey] = useState(0);
  
  // Use our custom hooks
  const { isAdminOrDeveloper, loading: roleLoading } = useUserRoleCheck();
  const { stats, loading: statsLoading } = useFawryStats(refreshKey);

  // Event handlers
  const handleWalletAdded = () => {
    setRefreshKey(prevKey => prevKey + 1);
    // Switch to wallets tab after adding wallet
    setActiveTab("wallets");
  };

  const handleOperationAdded = () => {
    setRefreshKey(prevKey => prevKey + 1);
  };

  const handleRefresh = () => {
    setRefreshKey(prevKey => prevKey + 1);
    toast.success("تم تحديث البيانات بنجاح");
  };

  return (
    <div className={`container mx-auto ${isMobile ? 'p-4' : 'p-6'}`}>
      <h1 className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold mb-4`}>محفظة فوري</h1>
      
      {/* Set up notification watcher */}
      <FawryWatcherSetup />
      
      {/* Stats dashboard with refresh button */}
      <DashboardStats 
        stats={[
          {
            title: "إجمالي المستلم",
            value: formatCurrency(stats.receivedAmount),
            icon: "arrow-down",
            description: "إجمالي المبلغ المستلم",
            valueColor: "green"
          },
          {
            title: "إجمالي المرسل",
            value: formatCurrency(stats.sentAmount),
            icon: "arrow-up",
            description: "إجمالي المبلغ المرسل",
            valueColor: "red"
          },
          {
            title: "صافي الأرباح",
            value: formatCurrency(stats.netProfit),
            icon: "wallet",
            description: "إجمالي العمولات المحصلة",
            valueColor: "green"
          },
          {
            title: "عدد المعاملات",
            value: stats.transactionCount.toString(),
            icon: "file-text",
            description: "إجمالي عمليات الإرسال والاستلام"
          }
        ]} 
        isLoading={statsLoading} 
        onRefresh={handleRefresh}
      />
      
      {/* Tabs for different sections */}
      <FawryWalletTabs
        isAdminOrDeveloper={isAdminOrDeveloper}
        refreshKey={refreshKey}
        onWalletAdded={handleWalletAdded}
        onOperationAdded={handleOperationAdded}
      />
    </div>
  );
};

export default FawryWallets;
