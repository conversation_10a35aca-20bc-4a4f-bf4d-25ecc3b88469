
import { supabase } from "@/integrations/supabase/client";
import { MonthlyLimitResult, MonthlyLimitOptions, TransactionPeriod } from "./types";

/**
 * Get current month period (first day to last day)
 */
export function getCurrentMonthPeriod(): TransactionPeriod {
  const today = new Date();
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1).toISOString();
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString();
  
  return {
    startDate: firstDayOfMonth,
    endDate: lastDayOfMonth
  };
}

/**
 * Check if a receive transaction would exceed the monthly limit for a SIM
 * @param simId The SIM ID to check
 * @param amount The amount of the transaction
 * @param options Optional configuration
 * @returns Object with limit information
 */
export async function checkMonthlyReceiveLimit(
  simId: string, 
  amount: number,
  options?: MonthlyLimitOptions
): Promise<MonthlyLimitResult> {
  try {
    const { startDate, endDate } = getCurrentMonthPeriod();
    
    // First get the SIM's receive_limit
    const { data: simData, error: simError } = await supabase
      .from('sims')
      .select('receive_limit')
      .eq('id', simId)
      .single();
    
    if (simError) {
      throw simError;
    }
    
    // Use the SIM's receive_limit or fallback to options or default
    const monthlyLimit = simData?.receive_limit || options?.monthlyLimit || 300000;
    
    // Get all receive transactions for this SIM in the current month
    const { data, error } = await supabase
      .from('transactions')
      .select('amount')
      .eq('sim_id', simId)
      .eq('transaction_type', 'receive')
      .gte('created_at', startDate)
      .lte('created_at', endDate);
    
    if (error) {
      throw error;
    }
    
    // Calculate total received amount this month
    const total = data?.reduce((sum, tx) => sum + Number(tx.amount), 0) || 0;
    
    // Calculate remaining amount before hitting the limit
    const remaining = monthlyLimit - total;
    
    // Check if current total exceeds the limit or if adding the new amount would exceed it
    const exceeds = total >= monthlyLimit || total + amount > monthlyLimit;
    
    return {
      exceeds,
      total,
      remaining: Math.max(0, remaining)
    };
  } catch (error) {
    console.error('Error checking monthly limit:', error);
    return {
      exceeds: false,
      total: 0,
      remaining: 300000,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
