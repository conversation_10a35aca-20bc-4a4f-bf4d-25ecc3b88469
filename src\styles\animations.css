
@layer utilities {
  /* تنسيقات الحركة والتأثيرات البصرية */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out forwards;
  }

  .animate-slide-in {
    animation: slideIn 0.4s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out forwards;
  }

  /* تعريف التأثيرات الحركية */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* تنسيقات لتأثيرات التحميل */
  .animate-pulse {
    animation: pulse 1.5s infinite ease-in-out;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* تأثيرات للأزرار عند الضغط */
  .animate-press {
    transition: transform 0.1s ease-in-out;
  }

  .animate-press:active {
    transform: scale(0.95);
  }

  /* تأثيرات إضافية */
  .hover-grow {
    transition: transform 0.2s ease;
  }

  .hover-grow:hover {
    transform: scale(1.03);
  }

  .hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-3px);
    @apply shadow-md;
  }

  /* تأثيرات حركية خاصة بالمكونات */
  .card-enter {
    animation: cardEnter 0.5s ease-out forwards;
  }

  @keyframes cardEnter {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* تأثير النبض لإشعارات */
  .pulse-dot {
    position: relative;
  }

  .pulse-dot::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    @apply bg-red-500 rounded-full;
    animation: pulseDot 1.5s infinite;
  }

  @keyframes pulseDot {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.5);
      opacity: 0.6;
    }
  }

  /* تأثير الظهور التدريجي بتأخير */
  .stagger-fade-in > * {
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards;
  }

  .stagger-fade-in > *:nth-child(1) { animation-delay: 0.1s; }
  .stagger-fade-in > *:nth-child(2) { animation-delay: 0.2s; }
  .stagger-fade-in > *:nth-child(3) { animation-delay: 0.3s; }
  .stagger-fade-in > *:nth-child(4) { animation-delay: 0.4s; }
  .stagger-fade-in > *:nth-child(5) { animation-delay: 0.5s; }
}
