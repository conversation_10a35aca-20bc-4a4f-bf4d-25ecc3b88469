import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Wallet } from "../utils/operationSchema";

interface WalletBalanceCardProps {
  wallet: Wallet | null;
}

const WalletBalanceCard: React.FC<WalletBalanceCardProps> = ({ wallet }) => {
  if (!wallet) {
    return null;
  }

  return (
    <Card className="bg-muted/40 border border-amber-200/50 dark:border-amber-700/30">
      <CardContent className="p-3 flex justify-between items-center">
        <span className="text-sm font-medium text-muted-foreground">رصيد المحفظة</span>
        <span className="text-lg font-bold">{wallet.balance?.toLocaleString('ar-EG') || 0} جنيه</span>
      </CardContent>
    </Card>
  );
};

export default WalletBalanceCard;
