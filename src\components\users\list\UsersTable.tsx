import React from "react";
import { User } from "@/types/user.types";
import { StatusBadge } from "./StatusBadge";
import { UserActions } from "./UserActions";
import { formatDate } from "@/utils/formatters";
import { UserStatus } from "./UserStatus";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";

// البريد الإلكتروني للمطور الذي لا يمكن تعديله أو حذفه
const DEVELOPER_EMAIL = "<EMAIL>";

interface UsersTableProps {
  users: User[];
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
}

export const UsersTable = ({ users, onEdit, onDelete }: UsersTableProps) => {
  // ترجمة أدوار المستخدمين إلى العربية
  const getRoleInArabic = (role: string): string => {
    switch (role) {
      case "admin": return "مدير";
      case "employee": return "موظف";
      case "developer": return "مطور";
      default: return role;
    }
  };

  // التحقق مما إذا كان المستخدم هو حساب المطور
  const isDeveloper = (email: string): boolean => {
    return email === DEVELOPER_EMAIL;
  };

  const columns: TableColumn<User>[] = [
    {
      header: "البريد الإلكتروني",
      accessor: (user) => (
        <UserStatus 
          email={user.email} 
          isActive={user.is_active} 
          isDeveloper={isDeveloper(user.email)}
        />
      )
    },
    {
      header: "الدور",
      accessor: (user) => getRoleInArabic(user.role)
    },
    {
      header: "تاريخ الإنشاء",
      accessor: (user) => formatDate(user.created_at),
      mobile: false // إخفاء على الأجهزة المحمولة
    },
    {
      header: "آخر تسجيل دخول",
      accessor: (user) => user.last_sign_in_at
        ? formatDate(user.last_sign_in_at)
        : "لم يسجل الدخول بعد",
      mobile: false // إخفاء على الأجهزة المحمولة
    },
    {
      header: "الحالة",
      accessor: (user) => <StatusBadge isActive={user.is_active} />
    },
    {
      header: "الإجراءات",
      accessor: (user) => (
        <UserActions
          user={user}
          isDeveloper={isDeveloper}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      ),
      mobile: false // Don't show these actions in the mobile card content
    }
  ];

  return (
    <ModernTable
      data={users}
      columns={columns}
      keyField="id"
      emptyMessage="لا يوجد مستخدمين"
      dir="rtl"
      mobileCardTitle={(user) => user.email}
      mobileCardActions={(user) => (
        <UserActions
          user={user}
          isDeveloper={isDeveloper}
          onEdit={onEdit}
          onDelete={onDelete}
          hideLabels={false}
        />
      )}
    />
  );
};
