import { Form } from "@/components/ui/form";
import { useAddTransaction } from "@/hooks/useAddTransaction";
import { TransactionTypeSelector } from "../TransactionTypeSelector";
import { WalletSelector } from "../WalletSelector";
import { SimSelector } from "../SimSelector";
import { CustomerFields } from "../CustomerFields";
import { AmountFields } from "../AmountFields";
import { DescriptionField } from "../DescriptionField";
import { FormActions } from "../FormActions";
import { TransactionWarnings } from "./TransactionWarnings";
import { useFormValidationState } from "@/hooks/transaction/useFormValidationState";
import { TransactionType } from "@/utils/transaction/types";
import { FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";

interface AddTransactionFormProps {
  onClose: () => void;
}

export function AddTransactionForm({
  onClose
}: AddTransactionFormProps) {
  
  const {
    form,
    isSubmitting,
    isCalculating,
    isCheckingLimit,
    isLoading,
    wallets,
    sims,
    customers,
    watchWalletId,
    selectedSimBalance,
    isMobileSimType,
    monthlyLimitExceeded,
    monthlyTotal,
    monthlyRemaining,
    hasInsufficientBalance,
    exceedsMobileReceiveLimit,
    onSubmit
  } = useAddTransaction({
    onClose
  });
  
  const transactionType = form.watch("transactionType") as TransactionType;
  const amount = form.watch("amount");
  const simId = form.watch("simId");
  const commission = form.watch("commission");
  const customerName = form.watch("customerName");
  const customerPhone = form.watch("customerPhone");
  
  // Get the selected SIM to access its receive_limit
  const selectedSim = sims.find(sim => sim.id === simId);
  const receiveLimit = selectedSim?.receive_limit || 300000; // Default to 300,000 if not set
  
  // Use the form validation state hook with more fields
  const { 
    insufficientBalance, 
    isInactiveSim, 
    isZeroBalance, 
    exceedsLimit, 
    isFormComplete,
    isButtonDisabled 
  } = useFormValidationState(
    transactionType, 
    amount, 
    simId, 
    commission, 
    sims, 
    isMobileSimType,
    watchWalletId,
    customerName,
    customerPhone
  );

  if (isLoading) {
    return <div className="p-4 text-center">جاري تحميل البيانات...</div>;
  }

  // Find if selected sim matches the selected wallet
  const isSimMatchingWallet = selectedSim && watchWalletId ? 
    selectedSim.wallet_id === watchWalletId : true;

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="space-y-6 my-4">
        <FormField
          control={form.control}
          name="transactionType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>نوع العملية <span className="text-red-500">*</span></FormLabel>
              <TransactionTypeSelector
                value={field.value}
                onChange={field.onChange}
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <AmountFields form={form} isCalculating={isCalculating} />

        <FormField
          control={form.control}
          name="walletId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>المحفظة <span className="text-red-500">*</span></FormLabel>
              <WalletSelector
                wallets={wallets}
                value={field.value}
                onChange={field.onChange}
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="simId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>الشريحة <span className="text-red-500">*</span></FormLabel>
              <SimSelector
                sims={sims}
                value={field.value}
                onChange={field.onChange}
                walletId={watchWalletId}
                isRequired={true}
              />
              <FormMessage />
            </FormItem>
          )}
        />

        {simId && watchWalletId && !isSimMatchingWallet && (
          <Alert variant="destructive" className="text-right">
            <AlertDescription className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              تنبيه: الشريحة المختارة لا تنتمي إلى المحفظة المحددة!
            </AlertDescription>
          </Alert>
        )}

        <TransactionWarnings
          insufficientBalance={insufficientBalance}
          isZeroBalance={isZeroBalance}
          exceedsLimit={exceedsLimit}
          monthlyLimitExceeded={monthlyLimitExceeded}
          transactionType={transactionType}
          amount={amount}
          commission={commission}
          selectedSimBalance={selectedSimBalance}
          monthlyTotal={monthlyTotal}
          monthlyRemaining={monthlyRemaining}
          isMobileSimType={isMobileSimType}
          receiveLimit={receiveLimit} // Pass the receive_limit from the selected SIM
        />

        <CustomerFields form={form} customers={customers} isPhoneRequired={true} />

        <DescriptionField form={form} />

        <FormActions 
          onClose={onClose} 
          isSubmitting={isSubmitting} 
          isCalculating={isCalculating || isCheckingLimit} 
          isDisabled={isButtonDisabled || !isSimMatchingWallet} 
        />
      </form>
    </Form>
  );
}
