
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { toast } from "sonner";
import { 
  createDatabaseBackup, 
  downloadBackupFile,
  saveBackupToHistory 
} from "../utils";

export function CreateBackupButton() {
  const [isBackupLoading, setIsBackupLoading] = useState(false);

  const handleCreateBackup = async () => {
    setIsBackupLoading(true);
    
    try {
      const backupData = await createDatabaseBackup();
      await downloadBackupFile(backupData);
      
      // Save backup info to history
      saveBackupToHistory(backupData);
      
      toast.success("تم إنشاء النسخة الاحتياطية بنجاح");
    } catch (error) {
      console.error("Error creating backup:", error);
      toast.error("حدث خطأ أثناء إنشاء النسخة الاحتياطية");
    } finally {
      setIsBackupLoading(false);
    }
  };

  return (
    <Button 
      onClick={handleCreateBackup} 
      disabled={isBackupLoading}
      className="w-full"
    >
      {isBackupLoading ? "جاري إنشاء النسخة الاحتياطية..." : "إنشاء وتحميل نسخة احتياطية"}
    </Button>
  );
}
