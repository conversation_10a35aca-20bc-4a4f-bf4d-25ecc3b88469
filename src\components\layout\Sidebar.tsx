
import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  SidebarContent,
  UserProfile,
  SidebarHeader,
  MobileMenuButton,
  MobileOverlay
} from "./sidebar";

export function Sidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const isMobile = useIsMobile();
  const isRTL = document.dir === "rtl";
  
  // Handle screen resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 768 && isMobileOpen) {
        setIsMobileOpen(false);
      }
    };
    
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [isMobileOpen]);
  
  // Close mobile sidebar when route changes
  const location = useLocation();
  useEffect(() => {
    if (isMobileOpen) {
      setIsMobileOpen(false);
    }
  }, [location.pathname]);
  
  // Mobile sidebar toggle
  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen);
  };
  
  // Handle click on mobile links
  const handleMobileLinkClick = () => {
    if (isMobile) {
      setIsMobileOpen(false);
    }
  };

  return (
    <>
      {/* Mobile menu button - visible only on small screens */}
      {isMobile && (
        <MobileMenuButton toggleMobileSidebar={toggleMobileSidebar} />
      )}
      
      {/* Mobile overlay */}
      <MobileOverlay 
        isMobileOpen={isMobile && isMobileOpen} 
        closeMobileSidebar={() => setIsMobileOpen(false)} 
      />
      
      <div
        className={cn(
          "h-screen border-l transition-all duration-300 sticky top-0 bg-background flex flex-col z-50",
          isCollapsed ? "w-16" : "w-60",
          isMobile && (isMobileOpen 
            ? "fixed inset-y-0 right-0 translate-x-0 shadow-xl" 
            : "fixed inset-y-0 right-0 translate-x-full")
        )}
      >
        <SidebarHeader 
          isCollapsed={isCollapsed}
          setIsCollapsed={setIsCollapsed}
          isMobile={isMobile}
          setIsMobileOpen={setIsMobileOpen}
          isRTL={isRTL}
        />

        <SidebarContent 
          isCollapsed={isCollapsed}
          handleMobileLinkClick={handleMobileLinkClick}
        />

        <UserProfile isCollapsed={isCollapsed} />
      </div>
    </>
  );
}
