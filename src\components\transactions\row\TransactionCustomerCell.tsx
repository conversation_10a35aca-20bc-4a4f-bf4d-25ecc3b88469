
import { TableCell } from "@/components/ui/table";
import { Transaction } from "@/types/transaction.types";

interface TransactionCustomerCellProps {
  customer?: { name: string; phone?: string } | null;
  customerName?: string;
  customerPhone?: string;
}

export function TransactionCustomerCell({ customer, customerName, customerPhone }: TransactionCustomerCellProps) {
  // استخدام اسم العميل من حقل customer_name إذا كان متوفر
  // وإلا استخدم اسم العميل من كائن customer المرتبط
  const displayName = customerName || (customer?.name) || "—";
  
  // استخدام رقم الهاتف من حقل customer_phone إذا كان متوفر
  // وإلا استخدم رقم الهاتف من كائن customer المرتبط
  const displayPhone = customerPhone || (customer?.phone) || "—";
  
  return (
    <TableCell className="font-medium rtl-cell">
      <div className="cell-content flex flex-col gap-1">
        <div>{displayName}</div>
        <div className="text-xs text-muted-foreground">{displayPhone}</div>
      </div>
    </TableCell>
  );
}
