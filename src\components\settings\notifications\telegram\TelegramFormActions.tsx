
import { Button } from "@/components/ui/button";
import { Save, SendIcon } from "lucide-react";
import { toast } from "sonner";

interface TelegramFormActionsProps {
  isSaving: boolean;
  isTesting: boolean;
  isCreatingFunction: boolean;
  handleSave: () => void;
  handleTest: () => void;
  handleCreateFunction: () => void;
  botToken: string;
  chatId: string;
}

export function TelegramFormActions({
  isSaving,
  isTesting,
  handleSave,
  handleTest,
  botToken,
  chatId
}: TelegramFormActionsProps) {
  const handleTestWithFeedback = () => {
    if (!botToken || !chatId) {
      toast.warning("تنبيه", {
        description: "يرجى إدخال توكن البوت ومعرف الدردشة أولاً"
      });
      return;
    }
    
    toast.info("جاري الإرسال", {
      description: "يتم الآن إرسال رسالة اختبارية..."
    });
    
    handleTest();
  };

  return (
    <div className="flex flex-col sm:flex-row gap-2 pt-6">
      <Button onClick={handleSave} className="flex gap-2" disabled={isSaving}>
        <Save className="h-4 w-4" />
        {isSaving ? "جاري الحفظ..." : "حفظ الإعدادات"}
      </Button>
      
      <Button 
        variant="outline" 
        onClick={handleTestWithFeedback} 
        className="flex gap-2" 
        disabled={isTesting || !botToken || !chatId}
      >
        <SendIcon className="h-4 w-4" />
        {isTesting ? "جاري الإرسال..." : "إرسال اختباري"}
      </Button>
    </div>
  );
}
