
import React from "react";
import { ArrowDown, ArrowUp } from "lucide-react";
import { FormControl, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { UseFormReturn } from "react-hook-form";
import { OperationFormValues } from "../hooks/useFawryOperationForm";

interface OperationTypeSelectorProps {
  form: UseFormReturn<OperationFormValues>;
}

const OperationTypeSelector: React.FC<OperationTypeSelectorProps> = ({ form }) => {
  return (
    <FormItem className="space-y-3">
      <FormLabel>نوع العملية</FormLabel>
      <FormControl>
        <RadioGroup
          onValueChange={(value) => form.setValue("operation_type", value)}
          defaultValue={form.watch("operation_type")}
          className="flex space-x-2 space-x-reverse items-center justify-around"
        >
          <div className={`
            flex flex-col items-center p-3 rounded-lg cursor-pointer border-2 transition-all w-[48%]
            ${form.watch('operation_type') === 'استلام' 
              ? 'border-green-500 bg-green-50 dark:bg-green-900/20' 
              : 'border-muted bg-background'}
          `}
            onClick={() => form.setValue("operation_type", "استلام")}
          >
            <div className="mb-2 rounded-full bg-green-100 p-2 dark:bg-green-800/30">
              <ArrowDown className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <FormLabel className="cursor-pointer mt-0 mb-1 font-medium">استلام</FormLabel>
            <RadioGroupItem value="استلام" id="receive" className="sr-only" />
          </div>

          <div className={`
            flex flex-col items-center p-3 rounded-lg cursor-pointer border-2 transition-all w-[48%]
            ${form.watch('operation_type') === 'إرسال' 
              ? 'border-red-500 bg-red-50 dark:bg-red-900/20' 
              : 'border-muted bg-background'}
          `}
            onClick={() => form.setValue("operation_type", "إرسال")}
          >
            <div className="mb-2 rounded-full bg-red-100 p-2 dark:bg-red-800/30">
              <ArrowUp className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
            <FormLabel className="cursor-pointer mt-0 mb-1 font-medium">إرسال</FormLabel>
            <RadioGroupItem value="إرسال" id="send" className="sr-only" />
          </div>
        </RadioGroup>
      </FormControl>
      <FormMessage />
    </FormItem>
  );
};

export default OperationTypeSelector;
