import React from "react";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { useIsMobile } from "@/hooks/use-mobile";
import { TableCard } from "@/components/ui/table";
export interface TableColumn<T> {
  header: string;
  accessor: keyof T | ((row: T) => React.ReactNode);
  className?: string;
  mobile?: boolean; // ما إذا كان سيتم عرض هذا العمود على الأجهزة المحمولة
}
interface ModernTableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  emptyMessage?: string;
  keyField: keyof T;
  rowClassName?: string | ((row: T) => string);
  mobileCardTitle?: (row: T) => React.ReactNode;
  mobileCardActions?: (row: T) => React.ReactNode;
  dir?: "rtl" | "ltr";
}
export function ModernTable<T extends object>({
  data,
  columns,
  loading = false,
  emptyMessage = "لا توجد بيانات متاحة",
  keyField,
  rowClassName,
  mobileCardTitle,
  mobileCardActions,
  dir = "rtl"
}: ModernTableProps<T>) {
  const isMobile = useIsMobile();
  const getCellValue = (row: T, column: TableColumn<T>): React.ReactNode => {
    const accessor = column.accessor;
    if (typeof accessor === "function") {
      return accessor(row);
    }
    // تحويل آمن إلى نص لضمان أنه ReactNode
    return String(row[accessor as keyof T] ?? '');
  };
  const getRowClassName = (row: T) => {
    if (typeof rowClassName === "function") {
      return rowClassName(row);
    }
    return rowClassName || "hover:bg-amber-50/30 dark:hover:bg-amber-900/10 transition-all duration-200";
  };

  // عرض الكروت للأجهزة المحمولة
  if (isMobile) {
    return <div className="space-y-3 animate-fade-in">
        {loading ? Array(3).fill(0).map((_, i) => <div key={i} className="animate-pulse bg-muted rounded-lg p-3 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>) : data.length === 0 ? <div className="text-center py-4 text-sm rtl-mobile-text">
            {emptyMessage}
          </div> : data.map(row => {
        const title = mobileCardTitle ? mobileCardTitle(row) : String(row[keyField] ?? '');
        const actions = mobileCardActions ? mobileCardActions(row) : undefined;

        // تصفية الأعمدة التي يجب عرضها على الأجهزة المحمولة
        const mobileColumns = columns.filter(col => col.mobile !== false);
        return <TableCard key={String(row[keyField])} title={title} className="mobile-card text-sm shadow-sm border-amber-100/50 dark:border-amber-800/30" actions={actions}>
                {mobileColumns.map((column, idx) => <div key={idx} className="table-card-row py-1 flex justify-between items-center">
                    <span className="text-muted-foreground text-xs font-medium">{column.header}</span>
                    <span className="font-medium text-sm">{getCellValue(row, column)}</span>
                  </div>)}
              </TableCard>;
      })}
      </div>;
  }

  // عرض الجدول للأجهزة المكتبية
  return <div className="modern-table-container animate-fade-in transition-all duration-300 elegant-table overflow-hidden rounded-md border border-amber-100/50 dark:border-amber-800/30 shadow-sm">
      <Table dir={dir} className={`rtl-table modern-table-gradient text-sm`}>
        <TableHeader>
          <TableRow className="bg-muted/20 border-b border-amber-500/20 dark:border-amber-400/30">
            {columns.map((column, index) => <TableHead key={index} className={`rtl-header whitespace-nowrap py-1.5 font-bold text-right pr-3 text-xs ${column.className || ''}`}>
                {column.header}
              </TableHead>)}
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? Array(3).fill(0).map((_, rowIndex) => <TableRow key={`loading-${rowIndex}`}>
                {columns.map((_, colIndex) => <TableCell key={`loading-cell-${rowIndex}-${colIndex}`} className="p-1">
                    <div className="h-3 bg-gray-200 animate-pulse rounded"></div>
                  </TableCell>)}
              </TableRow>) : data.length === 0 ? <TableRow>
              <TableCell colSpan={columns.length} className="text-center py-3 text-sm">
                {emptyMessage}
              </TableCell>
            </TableRow> : data.map((row, index) => <TableRow key={String(row[keyField])} className={`${getRowClassName(row)} animate-slide-in debt-card-hover text-sm`} style={{
          animationDelay: `${index * 50}ms`
        }}>
                {columns.map((column, colIndex) => <TableCell key={colIndex} className={`rtl-cell py-1 px-3 ${column.className || ''}`} label={column.header}>
                    <div className="cell-content text-sm py-[-10px]">
                      {getCellValue(row, column)}
                    </div>
                  </TableCell>)}
              </TableRow>)}
        </TableBody>
      </Table>
    </div>;
}