
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { SpecialCustomerForm } from "./SpecialCustomerForm";

interface AddSpecialCustomerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function AddSpecialCustomerDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: AddSpecialCustomerDialogProps) {
  const handleSuccess = () => {
    onSuccess();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            إضافة عميل مميز جديد
          </DialogTitle>
          <DialogDescription>
            أدخل بيانات العميل المميز في النموذج أدناه
          </DialogDescription>
        </DialogHeader>
        <SpecialCustomerForm
          onSuccess={handleSuccess}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
