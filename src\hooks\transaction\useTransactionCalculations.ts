
import { useEffect } from 'react';
import { TransactionType } from '@/utils/transaction/types';

/**
 * Hook for handling all transaction calculations and validations
 */
export function useTransactionCalculations({
  watchAmount,
  watchTransactionType,
  watchWalletId,
  watchSimId,
  watchCommission,
  formIsFawryWallet,
  formSelectedSimBalance,
  formIsMobileSimType,
  sims,
  wallets,
  form,
  setFormIsFawryWallet,
  setFormSelectedSimBalance, 
  setFormIsMobileSimType,
  setFormCheckingLimit,
  setFormMonthlyTotal,
  setFormMonthlyRemaining,
  setFormMonthlyLimitExceeded,
  setFormHasInsufficientBalance,
  setFormExceedsMobileReceiveLimit,
  checkWalletType,
  checkMonthlyLimits,
  handleCalculateCommission
}) {
  // Check if the selected wallet is a Fawry wallet
  useEffect(() => {
    const checkWallet = async () => {
      if (!watchWalletId) {
        setFormIsFawryWallet(false);
        return;
      }

      const selectedWallet = wallets.find(wallet => wallet.id === watchWalletId);
      const isFawry = selectedWallet?.wallet_type === 'fawry';
      setFormIsFawryWallet(isFawry);
      
      console.log('Wallet changed:', watchWalletId, 'isFawry:', isFawry, 'wallet type:', selectedWallet?.wallet_type);
      
      // If it's not a Fawry wallet and amount is > 0, calculate commission automatically
      if (!isFawry && watchAmount > 0) {
        handleCalculateCommission(
          Number(watchAmount),
          watchTransactionType,
          watchWalletId,
          isFawry,
          (value) => form.setValue('commission', value)
        );
      } else if (isFawry) {
        // For Fawry wallet, reset commission to 0 so user can enter manually
        form.setValue('commission', 0);
      }
    };

    checkWallet();
  }, [watchWalletId, wallets]);

  // Update sim balance when sim changes
  useEffect(() => {
    if (watchSimId) {
      const sim = sims.find(sim => sim.id === watchSimId);
      if (sim) {
        setFormSelectedSimBalance(sim.balance);
        
        // Check if this is a mobile SIM by looking at the number format
        const isMobile = sim.number.startsWith('010') || 
                         sim.number.startsWith('011') || 
                         sim.number.startsWith('012') || 
                         sim.number.startsWith('015');
        setFormIsMobileSimType(isMobile);
        
        // Check monthly limits for mobile SIMs
        if (isMobile && watchTransactionType === 'receive') {
          const checkLimit = async () => {
            setFormCheckingLimit(true);
            const { total, remaining, exceeds } = await checkMonthlyLimits(sim.id, Number(watchAmount));
            setFormMonthlyTotal(total);
            setFormMonthlyRemaining(remaining);
            setFormMonthlyLimitExceeded(exceeds);
            setFormCheckingLimit(false);
          };
          
          checkLimit();
        }
      }
    } else {
      setFormSelectedSimBalance(null);
      setFormIsMobileSimType(false);
    }
  }, [watchSimId, sims, watchTransactionType, watchAmount]);

  // Update commission when amount or transaction type changes
  useEffect(() => {
    // Only auto-calculate commission for non-Fawry wallets
    if (watchAmount > 0 && watchWalletId && !formIsFawryWallet) {
      console.log('Amount/type changed - auto calculating commission for non-Fawry wallet');
      handleCalculateCommission(
        Number(watchAmount),
        watchTransactionType,
        watchWalletId,
        formIsFawryWallet,
        (value) => form.setValue('commission', value)
      );
    }
    
    // Always check mobile limits for receive transactions
    if (watchAmount > 0 && formIsMobileSimType && watchTransactionType === 'receive') {
      const exceeds = watchAmount > 300000;
      setFormExceedsMobileReceiveLimit(exceeds);
    } else {
      setFormExceedsMobileReceiveLimit(false);
    }
  }, [watchAmount, watchTransactionType, watchWalletId, formIsFawryWallet]);

  // Check if transaction would cause insufficient balance
  useEffect(() => {
    if (watchTransactionType === 'send' && formSelectedSimBalance !== null && watchAmount > 0) {
      const totalAmount = Number(watchAmount) + Number(watchCommission);
      // تعديل هنا: نتحقق إذا كان المبلغ الكلي (المبلغ + العمولة) أكبر من الرصيد المتاح
      const insufficient = formSelectedSimBalance < totalAmount;
      setFormHasInsufficientBalance(insufficient);
    } else {
      setFormHasInsufficientBalance(false);
    }
  }, [watchAmount, watchTransactionType, formSelectedSimBalance, watchCommission, form]);
}
