
import { useState } from "react";
import { Customer } from "@/pages/Customers";
import { CustomerDesktopView } from "./CustomerDesktopView";
import { CustomerMobileView } from "./CustomerMobileView";
import { useIsMobile } from "@/hooks/use-mobile";
import { SimplePagination } from "@/components/ui/pagination-simple";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { CustomerForm } from "../CustomerForm";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface CustomerListProps {
  customers: Customer[];
  loading: boolean;
  onCustomerUpdated: () => void;
}

export function CustomerListContainer({
  customers,
  loading,
  onCustomerUpdated,
}: CustomerListProps) {
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [deletingCustomerId, setDeletingCustomerId] = useState<string | null>(null);
  const isMobile = useIsMobile();
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const totalPages = Math.ceil(customers.length / itemsPerPage);
  
  // Get current items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentCustomers = customers.slice(indexOfFirstItem, indexOfLastItem);

  const handleEditClick = (customer: Customer) => {
    setEditingCustomer(customer);
  };

  const handleDeleteClick = (customerId: string) => {
    setDeletingCustomerId(customerId);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingCustomerId) return;

    try {
      const { error } = await supabase
        .from("customers")
        .delete()
        .eq("id", deletingCustomerId);

      if (error) throw error;
      
      toast.success("تم حذف العميل بنجاح");
      onCustomerUpdated();
    } catch (error) {
      console.error("Error deleting customer:", error);
      toast.error("حدث خطأ أثناء حذف العميل");
    } finally {
      setDeletingCustomerId(null);
    }
  };

  const handleEditSuccess = () => {
    setEditingCustomer(null);
    onCustomerUpdated();
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  return (
    <>
      {isMobile ? (
        <CustomerMobileView 
          customers={currentCustomers}
          loading={loading}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
        />
      ) : (
        <CustomerDesktopView 
          customers={currentCustomers}
          loading={loading}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
        />
      )}

      {/* Pagination */}
      {!loading && customers.length > 0 && (
        <div className="mt-4 mb-6">
          <SimplePagination
            currentPage={currentPage}
            totalPages={totalPages}
            onNextPage={handleNextPage}
            onPreviousPage={handlePreviousPage}
            hasNextPage={currentPage < totalPages}
            hasPreviousPage={currentPage > 1}
          />
        </div>
      )}

      {/* Edit Customer Dialog */}
      <Dialog
        open={!!editingCustomer}
        onOpenChange={(open) => !open && setEditingCustomer(null)}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>تعديل بيانات العميل</DialogTitle>
          </DialogHeader>
          {editingCustomer && (
            <CustomerForm
              customer={editingCustomer}
              onSuccess={handleEditSuccess}
              onCancel={() => setEditingCustomer(null)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!deletingCustomerId}
        onOpenChange={(open) => !open && setDeletingCustomerId(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
            <AlertDialogDescription>
              هل أنت متأكد من رغبتك في حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm}>
              حذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
