
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

// إضافة عمولة لمحفظة جديدة
export const addWalletCommission = async (walletName: string) => {
  try {
    // التحقق من وجود عمولة للمحفظة بالفعل
    const { data: existingCommission, error: checkError } = await supabase
      .from('wallet_commissions')
      .select('id')
      .eq('wallet_name', walletName)
      .single();
      
    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 يعني "لا يوجد سجلات"
      console.error("خطأ في التحقق من وجود عمولة للمحفظة:", checkError);
      return false;
    }
    
    // إذا كانت العمولة موجودة بالفعل، نعود بنجاح
    if (existingCommission) {
      return true;
    }
    
    // الحصول على الإعدادات الافتراضية للعمولات
    const { data: defaultSettings, error: settingsError } = await supabase
      .from('commission_settings')
      .select('default_deposit_rate, default_withdrawal_rate')
      .limit(1)
      .single();
      
    if (settingsError) {
      console.error("خطأ في الحصول على إعدادات العمولة الافتراضية:", settingsError);
      return false;
    }
    
    // استخدام القيم الافتراضية إذا لم تكن موجودة
    const depositRate = defaultSettings?.default_deposit_rate || 0;
    const withdrawalRate = defaultSettings?.default_withdrawal_rate || 1;
    
    // إضافة عمولة جديدة للمحفظة
    const { error: insertError } = await supabase
      .from('wallet_commissions')
      .insert({
        wallet_name: walletName,
        deposit_rate: depositRate,
        withdrawal_rate: withdrawalRate,
        min_commission: 5
      });
      
    if (insertError) {
      console.error("خطأ في إضافة عمولة المحفظة:", insertError);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("خطأ غير متوقع في إضافة عمولة المحفظة:", error);
    return false;
  }
};
