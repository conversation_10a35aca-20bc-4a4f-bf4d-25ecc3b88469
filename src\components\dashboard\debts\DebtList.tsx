
import { DebtItem } from "./DebtItem";
import { DebtEmptyState } from "./DebtEmptyState";
import { DebtsSkeleton } from "./DebtsSkeleton";
import type { Debt } from "@/hooks/useOutstandingDebts";

interface DebtListProps {
  debts: Debt[];
  isLoading: boolean;
  formatCurrency: (amount: number) => string;
  navigateToDebts?: () => void;
}

export function DebtList({ debts, isLoading, formatCurrency, navigateToDebts }: DebtListProps) {
  if (isLoading) {
    return <DebtsSkeleton />;
  }
  
  if (debts.length === 0) {
    return <DebtEmptyState />;
  }
  
  return (
    <div>
      {debts.map((debt) => (
        <DebtItem 
          key={debt.id}
          debt={debt}
          formatCurrency={formatCurrency}
          onClick={navigateToDebts}
        />
      ))}
    </div>
  );
}
