
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface FawryStats {
  receivedAmount: number;
  sentAmount: number;
  netProfit: number;
  transactionCount: number;
}

export function useFawryStats(refreshKey: number = 0) {
  const [stats, setStats] = useState<FawryStats>({
    receivedAmount: 0,
    sentAmount: 0,
    netProfit: 0,
    transactionCount: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFawryStats = async () => {
      try {
        setLoading(true);
        
        // Fetch all operations to calculate accurate statistics
        const { data: operationsData, error: operationsError } = await supabase
          .from('fawry_wallet_operations')
          .select('amount, commission, operation_type');
        
        if (operationsError) throw operationsError;
        
        if (operationsData && operationsData.length > 0) {
          console.log("Fetched fawry operations:", operationsData.length);
          
          // Filter operations by type
          const receivedOperations = operationsData.filter(op => op.operation_type === 'استلام');
          const sentOperations = operationsData.filter(op => op.operation_type === 'إرسال');
          
          // Calculate received amount (total amount from "استلام" operations)
          const receivedAmount = receivedOperations.reduce(
            (sum, op) => sum + Number(op.amount || 0), 
            0
          );
          
          // Calculate sent amount (total amount from "إرسال" operations)
          const sentAmount = sentOperations.reduce(
            (sum, op) => sum + Number(op.amount || 0), 
            0
          );
          
          // Calculate total commissions across all operations
          const totalCommission = operationsData.reduce(
            (sum, op) => sum + Number(op.commission || 0), 
            0
          );
          
          // Update stats with correct values
          setStats({
            receivedAmount,
            sentAmount,
            netProfit: totalCommission,
            transactionCount: operationsData.length
          });
          
          console.log("Calculated stats:", {
            receivedAmount,
            sentAmount,
            netProfit: totalCommission,
            transactionCount: operationsData.length
          });
        } else {
          console.log("No fawry operations found or data is empty");
        }
      } catch (err) {
        console.error("Error fetching Fawry stats:", err);
        toast.error("حدث خطأ أثناء تحميل إحصائيات فوري");
      } finally {
        setLoading(false);
      }
    };

    fetchFawryStats();
  }, [refreshKey]);

  return { stats, loading };
}
