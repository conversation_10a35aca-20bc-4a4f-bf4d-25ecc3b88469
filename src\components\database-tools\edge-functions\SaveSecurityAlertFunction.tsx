
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Save, Shield } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useIsMobile } from "@/hooks/use-mobile";

export function SaveSecurityAlertFunction() {
  const [isSaving, setIsSaving] = useState(false);
  const isMobile = useIsMobile();
  
  const securityAlertCode = `import { serve } from "https://deno.land/std@0.177.0/http/server.ts";

interface SecurityAlertData {
  status: "success" | "failed";
  username: string;
  userId: string;
  ip: string;
  timestamp: string;
  role: string;
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }
  
  try {
    // Parse request body
    const { alertData, chatId, botToken } = await req.json();
    
    if (!chatId || !botToken || !alertData) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Missing required parameters: chatId, botToken, or alertData"
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
      );
    }
    
    const data = alertData as SecurityAlertData;
    
    // Format Telegram message with appropriate emojis
    const statusEmoji = data.status === "success" ? "✅" : "❌";
    const alertEmoji = data.status === "success" ? "⚠️" : "🚨";
    
    const message = \`
\${alertEmoji} <b>تنبيه أمني: محاولة دخول للوحة المطور</b> \${alertEmoji}

<b>الحالة:</b> \${statusEmoji} \${data.status === "success" ? "ناجحة" : "فاشلة"}
<b>المستخدم:</b> \${data.username}
<b>معرف المستخدم:</b> \${data.userId}
<b>الصلاحية:</b> \${data.role}
<b>عنوان IP:</b> \${data.ip}
<b>الوقت:</b> \${data.timestamp}

\${data.status === "success" ? "🔐" : "🔓"} <b>إذا لم تكن أنت من قام بهذه المحاولة، يرجى تغيير كلمة المرور فوراً!</b>
\`;

    // Process chat ID format properly
    let formattedChatId = chatId;
    
    // Send message using Telegram Bot API
    const apiUrl = \`https://api.telegram.org/bot\${botToken}/sendMessage\`;
    
    console.log(\`Sending security alert to Telegram chat ID: \${formattedChatId}\`);
    
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        chat_id: formattedChatId,
        text: message,
        parse_mode: "HTML", // Support HTML formatting in messages
      }),
    });
    
    // Parse Telegram response
    const telegramResponse = await response.json();
    
    if (telegramResponse.ok) {
      return new Response(
        JSON.stringify({ success: true, data: telegramResponse }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    } else {
      let errorMessage = "فشل في إرسال التنبيه الأمني";
      
      if (telegramResponse.description) {
        errorMessage = telegramResponse.description;
      }
      
      console.error("Telegram API error:", telegramResponse);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: errorMessage, 
          details: telegramResponse 
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
      );
    }
  } catch (error) {
    console.error("Error in securityAlert function:", error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || "An unknown error occurred"
      }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
    );
  }
});`;
  
  const saveToDatabase = async () => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('supabase_functions_content')
        .upsert({
          name: 'securityAlert',
          content: securityAlertCode,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'name'
        });
        
      if (error) {
        throw error;
      }
      
      toast.success("تم حفظ وظيفة securityAlert في قاعدة البيانات بنجاح");
    } catch (error: any) {
      console.error("خطأ في حفظ الوظيفة:", error);
      toast.error(`فشل حفظ وظيفة securityAlert: ${error.message}`);
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <Button
      variant="outline"
      size={isMobile ? "icon-sm" : "sm"}
      onClick={saveToDatabase}
      disabled={isSaving}
      className={`flex items-center ${isMobile ? "px-2" : "gap-2"} border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-700`}
    >
      {isMobile ? (
        <Shield className="h-4 w-4" />
      ) : (
        <>
          <Shield className="h-4 w-4" />
          <span className="whitespace-nowrap">
            {isSaving ? "جاري الحفظ..." : "حفظ وظيفة securityAlert"}
          </span>
        </>
      )}
    </Button>
  );
}
