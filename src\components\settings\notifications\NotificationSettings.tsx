
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { SecuritySettings } from "./security/SecuritySettings";
import { TelegramSettings } from "./telegram/TelegramSettings";
import { NotificationRules } from "./NotificationRules";
import { Bell, MessageCircle, Shield } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";

export function NotificationSettings() {
  const [activeTab, setActiveTab] = useState("rules");
  const isMobile = useIsMobile();
  
  return (
    <Card className="overflow-hidden">
      <CardHeader className="bg-muted/50">
        <CardTitle>إعدادات الإشعارات</CardTitle>
      </CardHeader>
      <CardContent className="p-0 sm:p-6">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className={`${isMobile ? "grid grid-cols-3 h-auto" : "grid grid-cols-3"} mb-6 w-full`}>
            <TabsTrigger 
              value="rules" 
              className="flex items-center justify-center gap-2 py-3 px-4 w-full border-b-2 border-transparent"
              title="قواعد الإشعارات"
            >
              <Bell className="h-5 w-5" />
              {!isMobile && <span className="text-sm">قواعد الإشعارات</span>}
            </TabsTrigger>
            <TabsTrigger 
              value="telegram" 
              className="flex items-center justify-center gap-2 py-3 px-4 w-full border-b-2 border-transparent"
              title="إعدادات تليجرام"
            >
              <MessageCircle className="h-5 w-5" />
              {!isMobile && <span className="text-sm">إعدادات تليجرام</span>}
            </TabsTrigger>
            <TabsTrigger 
              value="security" 
              className="flex items-center justify-center gap-2 py-3 px-4 w-full border-b-2 border-transparent"
              title="الأمان والتنبيهات"
            >
              <Shield className="h-5 w-5" />
              {!isMobile && <span className="text-sm">الأمان والتنبيهات</span>}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="rules" className="animate-fade-in px-6 pb-6">
            <NotificationRules />
          </TabsContent>
          
          <TabsContent value="telegram" className="animate-fade-in px-6 pb-6">
            <TelegramSettings />
          </TabsContent>
          
          <TabsContent value="security" className="animate-fade-in px-6 pb-6">
            <SecuritySettings />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
