
@layer utilities {
  /* تنسيقات خاصة بالجداول بالعربية */
  [dir="rtl"] .rtl-table-cell {
    text-align: right !important;
  }
  
  /* تنسيقات الجدول في النظام العربي */
  [dir="rtl"] .rtl-table {
    text-align: right !important;
    direction: rtl !important;
  }
  
  /* تنسيقات الجدول والخلايا */
  [dir="rtl"] .rtl-table th,
  [dir="rtl"] .rtl-table td {
    text-align: right !important;
  }
  
  /* تحسينات للرؤوس والخلايا */
  [dir="rtl"] .rtl-header {
    text-align: right !important;
    font-weight: 600 !important;
    padding-right: 1rem !important;
    padding-left: 0 !important;
  }
  
  [dir="rtl"] .rtl-cell {
    text-align: right !important;
    padding-right: 1rem !important;
    padding-left: 0 !important;
  }
  
  /* تنسيقات خاصة بمحاذاة محتوى الخلايا */
  [dir="rtl"] .cell-content {
    display: flex !important;
    justify-content: flex-start !important;
    text-align: right !important;
    width: 100% !important;
    padding-right: 1rem !important;
  }
  
  /* تحسينات للمسافات بين الأعمدة */
  [dir="rtl"] .column-spacing {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  /* تدرجات لونية للجداول */
  .modern-table-gradient {
    @apply relative overflow-hidden;
  }

  /* تدرج للوضع النهاري - تحسين للألوان أكثر إشراقاً ورقة */
  .modern-table-gradient tbody tr {
    @apply relative transition-all duration-300 hover:shadow-md;
    background: linear-gradient(90deg, rgba(255,246,240,0.2) 0%, rgba(255,250,245,0.3) 100%);
  }

  .modern-table-gradient tbody tr:hover {
    background: linear-gradient(90deg, rgba(254,215,188,0.15) 0%, rgba(255,228,206,0.2) 100%);
  }

  /* تدرج للوضع الليلي - تم الحفاظ عليه كما هو */
  .dark .modern-table-gradient tbody tr {
    background: linear-gradient(90deg, rgba(249,212,35,0.05) 0%, rgba(255,78,0,0.08) 100%);
  }

  .dark .modern-table-gradient tbody tr:hover {
    background: linear-gradient(90deg, rgba(249,212,35,0.1) 0%, rgba(255,78,0,0.15) 100%);
  }

  /* ظلال وحدود للجدول - تحسينات للهوامش */
  .modern-table-container {
    @apply rounded-xl overflow-hidden border border-amber-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-300 my-2;
  }
  
  /* تحسين تصميم رؤوس الجدول */
  .modern-table-gradient thead tr {
    @apply bg-amber-50/50 dark:bg-gray-800/90;
  }
  
  .modern-table-gradient th {
    @apply py-3 font-semibold text-gray-700 dark:text-gray-200;
  }
  
  /* تخصيص لون النص في الصفوف بشكل متناوب للقراءة الأفضل */
  .modern-table-gradient tbody tr:nth-child(even) {
    @apply bg-amber-50/30 dark:bg-opacity-50;
  }
  
  /* تحسينات للخلايا */
  .modern-table-gradient td {
    @apply border-b border-amber-100/50 dark:border-gray-700/50 py-2.5;
  }
  
  /* تأثيرات إضافية عند التفاعل */
  .modern-table-gradient tbody tr:hover td {
    @apply text-amber-900 dark:text-white;
  }
}
