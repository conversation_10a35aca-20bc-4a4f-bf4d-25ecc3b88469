
import { z } from "zod";

/**
 * Schema for validating fawry operations
 */
export const operationSchema = z.object({
  operation_type: z.string().min(1, "نوع العملية مطلوب"),
  amount: z.coerce.number().min(0.01, "المبلغ يجب أن يكون أكبر من صفر"),
  commission: z.coerce.number().min(0, "العمولة يجب أن تكون صفر أو أكثر").optional(),
  wallet_id: z.string().min(1, "المحفظة مطلوبة"),
});

export type OperationFormValues = z.infer<typeof operationSchema>;

export interface Operation {
  id: string;
  wallet_id: string;
  operation_type: string;
  amount: number;
  commission: number;
  created_at: string;
}

export interface Wallet {
  id: string;
  name: string;
  phone_number: string;
  balance: number;
}
