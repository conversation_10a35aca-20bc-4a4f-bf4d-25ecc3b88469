
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { AddTransactionForm } from "./form/AddTransactionForm";

interface AddTransactionDialogProps {
  open?: boolean;
  onOpenChange: (open: boolean) => void;
  onTransactionAdded?: () => void;
}

export function AddTransactionDialog({ 
  open, 
  onOpenChange,
  onTransactionAdded 
}: AddTransactionDialogProps) {
  const handleClose = () => {
    onOpenChange(false);
    // Call the onTransactionAdded callback if provided
    if (onTransactionAdded) {
      onTransactionAdded();
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold">إضافة عملية جديدة</DialogTitle>
          <DialogDescription className="text-center">
            قم بإدخال بيانات العملية الجديدة في النموذج أدناه
          </DialogDescription>
        </DialogHeader>
        <AddTransactionForm onClose={handleClose} />
      </DialogContent>
    </Dialog>
  );
}
