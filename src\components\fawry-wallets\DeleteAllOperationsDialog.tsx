
import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface DeleteAllOperationsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onOperationsDeleted: () => void;
}

const DeleteAllOperationsDialog: React.FC<DeleteAllOperationsDialogProps> = ({
  isOpen,
  onOpenChange,
  onOperationsDeleted,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  const handleDeleteAll = async () => {
    setIsDeleting(true);
    try {
      // 1. استرجاع جميع العمليات لتحديث أرصدة المحافظ
      const { data: operations, error: fetchError } = await supabase
        .from("fawry_wallet_operations")
        .select("*");

      if (fetchError) {
        throw fetchError;
      }

      // 2. استرجاع جميع المحافظ
      const { data: wallets, error: walletsError } = await supabase
        .from("fawry_wallets")
        .select("id, balance");

      if (walletsError) {
        throw walletsError;
      }

      // 3. إعادة ضبط رصيد كل محفظة إلى صفر
      for (const wallet of wallets) {
        const { error: resetError } = await supabase
          .from("fawry_wallets")
          .update({ balance: 0 })
          .eq("id", wallet.id);

        if (resetError) {
          throw resetError;
        }
      }

      // 4. حذف جميع العمليات
      const { error: deleteError } = await supabase
        .from("fawry_wallet_operations")
        .delete()
        .gte("id", "00000000-0000-0000-0000-000000000000"); // شرط يحقق حذف جميع السجلات

      if (deleteError) {
        throw deleteError;
      }

      toast({
        title: "تم الحذف بنجاح",
        description: "تم حذف جميع العمليات وإعادة ضبط أرصدة المحافظ",
      });

      onOpenChange(false);
      onOperationsDeleted();
    } catch (error) {
      console.error("Error deleting operations:", error);
      toast({
        variant: "destructive",
        title: "خطأ في الحذف",
        description: "حدث خطأ أثناء حذف العمليات. الرجاء المحاولة مرة أخرى.",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>هل أنت متأكد من حذف جميع العمليات؟</AlertDialogTitle>
          <AlertDialogDescription>
            هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع العمليات وإعادة ضبط أرصدة المحافظ إلى صفر.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>إلغاء</AlertDialogCancel>
          <AlertDialogAction onClick={handleDeleteAll} disabled={isDeleting} className="bg-red-600 hover:bg-red-700">
            {isDeleting ? "جاري الحذف..." : "حذف جميع العمليات"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export default DeleteAllOperationsDialog;
