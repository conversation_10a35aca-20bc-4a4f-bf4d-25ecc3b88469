
-- Function to check if <PERSON><PERSON> is enabled for a table
CREATE OR REPLACE FUNCTION public.is_rls_enabled(table_name_param text)
RETURNS TABLE(is_rls_enabled boolean) 
LANGUAGE SQL SECURITY DEFINER
AS $$
  SELECT relrowsecurity AS is_rls_enabled
  FROM pg_class
  WHERE oid = ('public.' || table_name_param)::regclass;
$$;

-- View to get information about RLS policies
CREATE OR REPLACE VIEW public._rls_policies_view AS
SELECT
  n.nspname AS schema_name,
  c.relname AS table_name,
  pol.polname AS policy_name,
  CASE
    WHEN pol.polpermissive THEN 'PERMISSIVE'
    ELSE 'RESTRICTIVE'
  END AS policy_type,
  CASE
    WHEN pol.polcmd = 'r' THEN 'SELECT'
    WHEN pol.polcmd = 'a' THEN 'INSERT'
    WHEN pol.polcmd = 'w' THEN 'UPDATE'
    WHEN pol.polcmd = 'd' THEN 'DELETE'
    WHEN pol.polcmd = '*' THEN 'ALL'
  END AS operation,
  pg_get_expr(pol.polqual, pol.polrelid) AS using_expression,
  pg_get_expr(pol.polwithcheck, pol.polrelid) AS check_expression,
  CASE
    WHEN pol.polroles = '{0}' THEN 'PUBLIC'
    ELSE ARRAY_TO_STRING(ARRAY(
      SELECT rolname FROM pg_authid
      WHERE oid = ANY(pol.polroles)
      ORDER BY rolname
    ), ', ')
  END AS roles
FROM
  pg_policy pol
  JOIN pg_class c ON c.oid = pol.polrelid
  JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE
  n.nspname = 'public'
ORDER BY
  n.nspname, c.relname, pol.polname;

-- View to get information about database functions
CREATE OR REPLACE VIEW public._functions_view AS
SELECT
  n.nspname AS schema_name,
  p.proname AS function_name,
  pg_get_functiondef(p.oid) AS definition,
  CASE
    WHEN p.prosecdef THEN 'SECURITY DEFINER'
    ELSE 'SECURITY INVOKER'
  END AS security,
  l.lanname AS language,
  p.provolatile AS volatility
FROM
  pg_proc p
  JOIN pg_namespace n ON n.oid = p.pronamespace
  JOIN pg_language l ON l.oid = p.prolang
WHERE
  n.nspname = 'public'
ORDER BY
  n.nspname, p.proname;
