
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardDes<PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, Star } from "lucide-react";

interface TestimonialsSectionProps {
  scrollToContact?: () => void;
}

export function TestimonialsSection({ scrollToContact }: TestimonialsSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-bold flex items-center gap-2">
          <Users className="h-5 w-5 text-primary" />
          آراء العملاء
        </CardTitle>
        <CardDescription>تعرف على تجارب عملائنا مع خدماتنا</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* New Testimonial */}
          <Card className="hover:shadow-lg transition-all duration-300 bg-muted/30">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">محمد عبد الله</h3>
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map(star => <Star key={star} className="w-4 h-4 fill-primary text-primary" />)}
                </div>
              </div>
              <CardDescription>القاهرة</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                "النظام سهل الاستخدام ويتيح تنبيهات تفصيلية لمدير الفرع بشأن جميع العمليات. ما يعجبني أكثر هو القدرة على متابعة كل شيء بشكل لحظي، مما يسهل اتخاذ القرارات بسرعة ودقة."
              </p>
            </CardContent>
          </Card>

          {/* Testimonial 1 */}
          <Card className="hover:shadow-lg transition-all duration-300 bg-muted/30">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">أحمد محمد</h3>
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map(star => <Star key={star} className="w-4 h-4 fill-primary text-primary" />)}
                </div>
              </div>
              <CardDescription> القاهرة</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                "النظام ساعدنا بشكل كبير في تنظيم عملياتنا المالية وإدارة المعاملات. الدعم الفني ممتاز والاستجابة سريعة جداً عند الحاجة."
              </p>
            </CardContent>
          </Card>

          {/* Testimonial 2 */}
          <Card className="hover:shadow-lg transition-all duration-300 bg-muted/30">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">سارة علي</h3>
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map(star => <Star key={star} className="w-4 h-4 fill-primary text-primary" />)}
                </div>
              </div>
              <CardDescription>الإسكندرية</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                "الواجهة سهلة الاستخدام والميزات شاملة. ما أعجبني أكثر هو خدمة العملاء الممتازة والاستجابة السريعة لأي استفسارات."
              </p>
            </CardContent>
          </Card>

          {/* Testimonial 3 */}
          <Card className="hover:shadow-lg transition-all duration-300 bg-muted/30">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">محمود عبدالله</h3>
                <div className="flex items-center">
                  {[1, 2, 3, 4].map(star => <Star key={star} className="w-4 h-4 fill-primary text-primary" />)}
                  {[5].map(star => <Star key={star} className="w-4 h-4 text-muted" />)}
                </div>
              </div>
              <CardDescription> المنصورة</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                "تجربة رائعة مع النظام والدعم الفني. أحتاج فقط لبعض التحسينات في تقارير المبيعات، ولكن بشكل عام الخدمة ممتازة."
              </p>
            </CardContent>
          </Card>

          {/* Testimonial 4 */}
          <Card className="hover:shadow-lg transition-all duration-300 bg-muted/30">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">فاطمة حسن</h3>
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map(star => <Star key={star} className="w-4 h-4 fill-primary text-primary" />)}
                </div>
              </div>
              <CardDescription>طنطا</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                "أفضل نظام استخدمته على الإطلاق. سهل الاستخدام، موثوق، وفريق الدعم متعاون جداً. أنصح به بشدة لجميع الشركات."
              </p>
            </CardContent>
          </Card>

          {/* Testimonial 5 */}
          <Card className="hover:shadow-lg transition-all duration-300 bg-muted/30">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">وليد خالد</h3>
                <div className="flex items-center">
                  {[1, 2, 3, 4].map(star => <Star key={star} className="w-4 h-4 fill-primary text-primary" />)}
                  {[5].map(star => <Star key={star} className="w-4 h-4 text-muted" />)}
                </div>
              </div>
              <CardDescription> القاهرة</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-sm">
                "سعدت بالتعامل مع هذه الشركة، خدمة الدعم ممتازة والنظام سهل الاستخدام. بعض التحديثات المستقبلية ستجعله مثالياً."
              </p>
            </CardContent>
          </Card>
        </div>
      </CardContent>
      <CardFooter className="flex justify-center">
        <Button 
          variant="outline" 
          className="hover-scale"
          onClick={scrollToContact}
        >
          <Users className="h-4 w-4 mr-2" />
          انضم إلى قائمة عملائنا السعداء
        </Button>
      </CardFooter>
    </Card>
  );
}
