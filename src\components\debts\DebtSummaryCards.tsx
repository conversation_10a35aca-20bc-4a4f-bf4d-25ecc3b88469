
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { CreditCard, Clock, Calendar, Check } from "lucide-react";
import { useState } from "react";

interface DebtSummary {
  totalAmount: number;
  count: number;
}

interface DebtSummaryCardsProps {
  summaries: {
    total: DebtSummary;
    pending: DebtSummary;
    overdue: DebtSummary;
    paid: DebtSummary;
  };
  loading: boolean;
}

export function DebtSummaryCards({ summaries, loading }: DebtSummaryCardsProps) {
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} ج.م`;
  };

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map(i => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-300 bg-gray-300 rounded w-24">&nbsp;</CardTitle>
              <div className="p-2 bg-gray-200 rounded-full">&nbsp;</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold bg-gray-200 rounded w-32 h-8">&nbsp;</div>
              <p className="text-xs text-muted-foreground mt-1 bg-gray-200 rounded w-20 h-4">&nbsp;</p>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <SummaryCard 
        title="إجمالي الديون"
        value={formatCurrency(summaries.total.totalAmount)}
        description={`عدد الديون: ${summaries.total.count}`}
        icon={<CreditCard className="h-4 w-4 text-primary" />}
        iconClass="bg-primary/10"
      />
      
      <SummaryCard 
        title="الديون المستحقة"
        value={formatCurrency(summaries.pending.totalAmount)}
        description={`عدد الديون: ${summaries.pending.count}`}
        icon={<Calendar className="h-4 w-4 text-blue-500" />}
        iconClass="bg-blue-100"
      />
      
      <SummaryCard 
        title="الديون المتأخرة"
        value={formatCurrency(summaries.overdue.totalAmount)}
        description={`عدد الديون: ${summaries.overdue.count}`}
        icon={<Clock className="h-4 w-4 text-red-500" />}
        iconClass="bg-red-100"
      />
      
      <SummaryCard 
        title="الديون المدفوعة"
        value={formatCurrency(summaries.paid.totalAmount)}
        description={`عدد الديون: ${summaries.paid.count}`}
        icon={<Check className="h-4 w-4 text-green-500" />}
        iconClass="bg-green-100"
      />
    </div>
  );
}

interface SummaryCardProps {
  title: string;
  value: string;
  description?: string;
  icon: React.ReactNode;
  iconClass?: string;
}

function SummaryCard({ title, value, description, icon, iconClass }: SummaryCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div 
      className={`glass-card relative ${isHovered ? "debt-card-hover" : ""}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Animated border effect on hover */}
      {isHovered && (
        <div className="glass-border-animation"></div>
      )}
      
      <Card className="relative z-10 glass-effect border-0 overflow-hidden rounded-[0.7rem] transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <div className={`p-2 rounded-full ${iconClass}`}>{icon}</div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{value}</div>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
