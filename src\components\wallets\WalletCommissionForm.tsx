
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { saveWalletCommissionSettings } from "./commissionsUtils";
import { WalletCommissionFormValues } from "./commissionsTypes";
import { supabase } from "@/integrations/supabase/client";

interface WalletCommissionFormProps {
  defaultValues: WalletCommissionFormValues;
}

interface WalletCommission {
  id: string;
  wallet_name: string;
  deposit_rate: number;
  withdrawal_rate: number;
  min_commission: number;
}

const WalletCommissionForm = ({ defaultValues }: WalletCommissionFormProps) => {
  const [formValues, setFormValues] = useState<WalletCommissionFormValues>({
    vodafoneDeposit: defaultValues.vodafoneDeposit,
    vodafoneWithdrawal: defaultValues.vodafoneWithdrawal,
    vodafoneMinCommission: defaultValues.vodafoneMinCommission,
    etisalatDeposit: defaultValues.etisalatDeposit,
    etisalatWithdrawal: defaultValues.etisalatWithdrawal,
    etisalatMinCommission: defaultValues.etisalatMinCommission,
    orangeDeposit: defaultValues.orangeDeposit,
    orangeWithdrawal: defaultValues.orangeWithdrawal,
    orangeMinCommission: defaultValues.orangeMinCommission,
    instaPayDeposit: defaultValues.instaPayDeposit,
    instaPayWithdrawal: defaultValues.instaPayWithdrawal,
    instaPayMinCommission: defaultValues.instaPayMinCommission,
  });
  const [walletCommissions, setWalletCommissions] = useState<WalletCommission[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchWalletCommissions();
  }, []);

  const fetchWalletCommissions = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('wallet_commissions')
        .select('*')
        .order('wallet_name');
        
      if (error) {
        throw error;
      }
      
      setWalletCommissions(data || []);
    } catch (error) {
      console.error("Error fetching wallet commissions:", error);
      toast.error("حدث خطأ أثناء جلب بيانات عمولات المحافظ");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormValues((prev) => ({
      ...prev,
      [name]: parseFloat(value) || 0
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Save to database
      await saveWalletCommissionSettings(formValues);
      // Refresh wallet commissions list
      fetchWalletCommissions();
    } catch (error) {
      console.error("Error saving wallet commission settings:", error);
      toast.error("حدث خطأ أثناء حفظ إعدادات العمولة");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">جاري تحميل بيانات العمولات...</div>
        </CardContent>
      </Card>
    );
  }

  if (walletCommissions.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">لا توجد محافظ مضافة بعد. قم بإضافة محافظ من قسم إدارة المحافظ.</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardContent className="pt-6">
          {walletCommissions.map((commission) => (
            <div key={commission.id} className="mb-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">{commission.wallet_name}</h3>
                <div className="grid gap-4 sm:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor={`${commission.wallet_name}Deposit`}>إيداع (%)</Label>
                    <Input
                      id={`${commission.wallet_name}Deposit`}
                      name={`${commission.wallet_name}Deposit`}
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={formValues[`${commission.wallet_name}Deposit` as keyof WalletCommissionFormValues] || commission.deposit_rate}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`${commission.wallet_name}Withdrawal`}>سحب (%)</Label>
                    <Input
                      id={`${commission.wallet_name}Withdrawal`}
                      name={`${commission.wallet_name}Withdrawal`}
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={formValues[`${commission.wallet_name}Withdrawal` as keyof WalletCommissionFormValues] || commission.withdrawal_rate}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`${commission.wallet_name}MinCommission`}>الحد الأدنى للعمولة</Label>
                    <Input
                      id={`${commission.wallet_name}MinCommission`}
                      name={`${commission.wallet_name}MinCommission`}
                      type="number"
                      step="1"
                      min="0"
                      value={formValues[`${commission.wallet_name}MinCommission` as keyof WalletCommissionFormValues] || commission.min_commission}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              </div>
              {commission !== walletCommissions[walletCommissions.length - 1] && (
                <Separator className="my-6" />
              )}
            </div>
          ))}

          <div className="mt-6 flex justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "جاري الحفظ..." : "حفظ عمولات المحافظ"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
};

export default WalletCommissionForm;
