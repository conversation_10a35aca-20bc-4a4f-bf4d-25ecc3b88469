
import { WifiOff, Wifi, Check } from "lucide-react";
import { ThemeSwitcher } from "./ThemeSwitcher";
import { Button } from "@/components/ui/button";
import { UserMenu } from "@/components/auth/UserMenu";
import { NotificationsDropdown } from "./NotificationsDropdown";
import { useState, useEffect } from "react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export function Header() {
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | null>(null);

  const testConnection = async () => {
    setIsLoading(true);
    setConnectionStatus(null);
    
    try {
      const { data, error } = await supabase
        .from('wallets')
        .select('name')
        .limit(1);
      
      if (error) throw error;
      
      setConnectionStatus('connected');
      
      // Show a small toast notification instead of opening the drawer
      toast.success("تم الاتصال بقاعدة البيانات بنجاح", {
        position: "bottom-right",
        duration: 3000,
      });
      
      console.log("Connection test successful:", data);
    } catch (error) {
      console.error("Connection test failed:", error);
      setConnectionStatus('disconnected');
      toast.error("فشل الاتصال بقاعدة البيانات", {
        position: "bottom-right",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Automatically check connection status when component mounts
  useEffect(() => {
    testConnection();
    
    // Set up an interval to check connection status every 60 minutes (3600000 ms)
    const interval = setInterval(() => {
      testConnection();
    }, 3600000); // 60 minutes
    
    // Clean up interval on component unmount
    return () => clearInterval(interval);
  }, []);

  return <header className="h-16 border-b flex items-center justify-between px-4">
      <div className="flex items-center gap-3 flex-1">
        <img src="https://i.ibb.co/W47dWQ7D/cf6060f5-4b0d-46e3-8db3-cf87937a1185.png" alt="Cash Logo" className="h-9 w-9 hidden sm:block" />
        
      </div>
      
      <div className="flex items-center gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon"
                onClick={testConnection}
                disabled={isLoading}
                className={connectionStatus === 'connected' ? 'text-green-500' : connectionStatus === 'disconnected' ? 'text-red-500' : ''}
              >
                {connectionStatus === 'connected' ? (
                  <Wifi size={20} className="text-green-500 transition-colors" />
                ) : connectionStatus === 'disconnected' ? (
                  <WifiOff size={20} className="text-red-500 transition-colors" />
                ) : (
                  <Wifi size={20} className="text-muted-foreground animate-pulse transition-colors" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {isLoading 
                ? 'جاري اختبار الاتصال...' 
                : connectionStatus === 'connected'
                ? 'متصل بقاعدة البيانات'
                : connectionStatus === 'disconnected'
                ? 'غير متصل بقاعدة البيانات'
                : 'اختبار اتصال قاعدة البيانات'}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <NotificationsDropdown />
        <ThemeSwitcher />
        <UserMenu />
      </div>
    </header>;
}
