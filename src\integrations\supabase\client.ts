// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jmpynrnnnyoiywfrpybr.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImptcHlucm5ubnlvaXl3ZnJweWJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4Njg4ODUsImV4cCI6MjA2MTQ0NDg4NX0.CHY7JlpRKp681_6WKhcAHw6wNxNc-hdt_YiOwAgRTFw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});