
import { Receipt } from "lucide-react";
import { Card<PERSON>ead<PERSON>, CardTitle } from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";

interface TransactionCardHeaderProps {
  transactionsCount: number;
}

export function TransactionCardHeader({ transactionsCount }: TransactionCardHeaderProps) {
  const isMobile = useIsMobile();
  
  return (
    <CardHeader className={`bg-gradient-to-r from-blue-50 to-white dark:from-gray-800 dark:to-gray-800/70 border-b border-gray-100 dark:border-gray-700 pb-3 ${isMobile ? 'p-3' : ''}`}>
      <div className="flex items-center justify-between">
        <CardTitle className={`text-lg flex items-center gap-2 ${isMobile ? 'text-base' : ''}`}>
          <Receipt className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'} text-blue-500`} />
          العمليات الأخيرة
        </CardTitle>
        {transactionsCount > 0 && (
          <span className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 text-xs px-2 py-1 rounded-full">
            {transactionsCount} عمليات
          </span>
        )}
      </div>
    </CardHeader>
  );
}
