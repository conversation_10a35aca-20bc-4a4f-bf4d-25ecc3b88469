
-- Function to get primary keys for a table
CREATE OR REPLACE FUNCTION public.get_primary_keys_for_table(table_name_param text)
RETURNS TABLE(column_name text, constraint_name text) 
LANGUAGE SQL SECURITY DEFINER
AS $$
  SELECT
    kcu.column_name,
    tc.constraint_name
  FROM
    information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
  WHERE
    tc.table_schema = 'public'
    AND tc.table_name = table_name_param
    AND tc.constraint_type = 'PRIMARY KEY'
  ORDER BY
    kcu.ordinal_position;
$$;

-- Function to get foreign keys for a table
CREATE OR REPLACE FUNCTION public.get_foreign_keys_for_table(table_name_param text)
RETURNS TABLE(
  column_name text,
  foreign_table_name text,
  foreign_column_name text,
  constraint_name text
) 
LANGUAGE SQL SECURITY DEFINER
AS $$
  SELECT
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    tc.constraint_name
  FROM
    information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
  WHERE
    tc.table_schema = 'public'
    AND tc.table_name = table_name_param
    AND tc.constraint_type = 'FOREIGN KEY'
  ORDER BY
    kcu.ordinal_position;
$$;

-- Function to get indexes for a table
CREATE OR REPLACE FUNCTION public.get_indexes_for_table(table_name_param text)
RETURNS TABLE(
  indexname text,
  indexdef text
) 
LANGUAGE SQL SECURITY DEFINER
AS $$
  SELECT
    indexname,
    indexdef
  FROM
    pg_indexes
  WHERE
    schemaname = 'public'
    AND tablename = table_name_param
  ORDER BY
    indexname;
$$;
