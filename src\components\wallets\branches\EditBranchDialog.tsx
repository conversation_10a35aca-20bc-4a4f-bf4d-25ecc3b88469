
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Branch, updateBranch } from "@/services/branchService";

interface EditBranchDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  branch: Branch | null;
  onBranchUpdated: () => void;
  setBranch: (branch: Branch | null) => void;
}

export function EditBranchDialog({
  isOpen,
  onOpenChange,
  branch,
  onBranchUpdated,
  setBranch,
}: EditBranchDialogProps) {
  const handleEdit = async () => {
    if (!branch) return;
    
    const success = await updateBranch(branch.id, {
      name: branch.name,
      address: branch.address,
      manager: branch.manager
    });
    
    if (success) {
      onOpenChange(false);
      onBranchUpdated();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>تعديل الفرع</DialogTitle>
          <DialogDescription>
            قم بتعديل بيانات الفرع في النموذج أدناه
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="edit-branch-name">اسم الفرع</Label>
            <Input
              id="edit-branch-name"
              value={branch?.name || ""}
              onChange={(e) =>
                setBranch(
                  branch
                    ? { ...branch, name: e.target.value }
                    : null
                )
              }
              dir="rtl"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="edit-branch-address">العنوان</Label>
            <Input
              id="edit-branch-address"
              value={branch?.address || ""}
              onChange={(e) =>
                setBranch(
                  branch
                    ? { ...branch, address: e.target.value }
                    : null
                )
              }
              dir="rtl"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="edit-branch-manager">مدير الفرع</Label>
            <Input
              id="edit-branch-manager"
              value={branch?.manager || ""}
              onChange={(e) =>
                setBranch(
                  branch
                    ? { ...branch, manager: e.target.value }
                    : null
                )
              }
              dir="rtl"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleEdit}>
            حفظ التغييرات
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
