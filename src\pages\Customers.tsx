import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SpecialCustomersTab } from "@/components/customers/special/SpecialCustomersTab";
export interface Customer {
  id: string;
  name: string;
  phone: string;
  notes: string | null;
  created_at: string;
}
const Customers = () => {
  const [activeTab, setActiveTab] = useState("special");
  return <div className="p-4 md:p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">العملاء</h1>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="w-full max-w-md mx-auto mb-2">
          <TabsTrigger value="special" className="flex-1">قائمة العملاء 
        </TabsTrigger>
        </TabsList>
        
        <TabsContent value="special">
          <SpecialCustomersTab />
        </TabsContent>
      </Tabs>
    </div>;
};
export default Customers;