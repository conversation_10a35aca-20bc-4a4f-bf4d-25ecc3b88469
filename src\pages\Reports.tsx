import { useState, useRef, useEffect } from "react";
import { DateRange } from "react-day-picker";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ReportFilters } from "@/components/reports/ReportFilters";
import { TransactionTab } from "@/components/reports/TransactionTab";
import { WalletsTab } from "@/components/reports/WalletsTab";
import { DebtsTab } from "@/components/reports/DebtsTab";
import { BarChart3, CreditCard, ReceiptText } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useIsMobile } from "@/hooks/use-mobile";
import { ScrollArea } from "@/components/ui/scroll-area";
export default function Reports() {
  const [selectedTab, setSelectedTab] = useState("transactions");
  const [filters, setFilters] = useState<{
    dateRange: DateRange | undefined;
    branchId: string | null;
  }>({
    dateRange: {
      from: new Date(),
      to: new Date()
    },
    branchId: null
  });
  const isMobile = useIsMobile();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Effect to scroll active tab into view
  useEffect(() => {
    if (scrollContainerRef.current) {
      const activeTab = scrollContainerRef.current.querySelector('[data-state="active"]');
      if (activeTab) {
        activeTab.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  }, [selectedTab]);
  const handleFiltersChange = (newFilters: {
    dateRange: DateRange | undefined;
    branchId: string | null;
  }) => {
    setFilters(newFilters);
  };
  return <div className="p-6">
      <div className="flex flex-col">
        <h1 className="text-3xl font-bold mb-6">التقارير</h1>
        
        <ReportFilters onFiltersChange={handleFiltersChange} />
        
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
          <div className="relative w-full overflow-hidden">
            <ScrollArea className="w-full" orientation="horizontal">
              <div ref={scrollContainerRef} className="min-w-full my-[11px]">
                <TabsList className="inline-flex w-auto md:w-auto justify-start border-b pb-0 rounded-full">
                  <TabsTrigger value="transactions" title="العمليات" className="gap-2 flex-shrink-0 h-12 sm:h-10 px-4 min-w-[100px] transition-all duration-200 relative rounded-full">
                    <BarChart3 className="h-5 w-5 sm:h-4 sm:w-4" />
                    {!isMobile && <span className="whitespace-nowrap">العمليات</span>}
                  </TabsTrigger>
                  <TabsTrigger value="wallets" title="المحافظ" className="gap-2 flex-shrink-0 h-12 sm:h-10 px-4 min-w-[100px] transition-all duration-200 relative rounded-full">
                    <CreditCard className="h-5 w-5 sm:h-4 sm:w-4" />
                    {!isMobile && <span className="whitespace-nowrap">المحافظ</span>}
                  </TabsTrigger>
                  <TabsTrigger value="debts" title="الديون" className="gap-2 flex-shrink-0 h-12 sm:h-10 px-4 min-w-[100px] transition-all duration-200 relative rounded-full">
                    <ReceiptText className="h-5 w-5 sm:h-4 sm:w-4" />
                    {!isMobile && <span className="whitespace-nowrap">الديون</span>}
                  </TabsTrigger>
                </TabsList>
              </div>
            </ScrollArea>
          </div>
          
          <TabsContent value="transactions" className="space-y-4 animate-fade-in">
            <TransactionTab dateRange={filters.dateRange} branchId={filters.branchId} />
          </TabsContent>
          
          <TabsContent value="wallets" className="space-y-4 animate-fade-in">
            <WalletsTab dateRange={filters.dateRange} branchId={filters.branchId} />
          </TabsContent>
          
          <TabsContent value="debts" className="space-y-4 animate-fade-in">
            <DebtsTab dateRange={filters.dateRange} branchId={filters.branchId} />
          </TabsContent>
        </Tabs>
      </div>
    </div>;
}