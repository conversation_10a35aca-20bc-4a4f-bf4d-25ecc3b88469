
/* تأثيرات للبطاقات الشفافة */
.glass-effect {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.3));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dark .glass-effect {
  background: linear-gradient(135deg, rgba(25, 25, 35, 0.5), rgba(25, 25, 35, 0.3));
}

.glass-card {
  position: relative;
  border-radius: 0.75rem;
  overflow: visible;
  transform: translateZ(0);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.glass-border-animation {
  position: absolute;
  inset: -2px;
  border-radius: 0.75rem;
  padding: 2px;
  background: linear-gradient(
    -45deg, 
    rgba(239, 68, 68, 0), 
    rgba(239, 68, 68, 0.3), 
    rgba(239, 68, 68, 0.6), 
    rgba(239, 68, 68, 0.3), 
    rgba(239, 68, 68, 0)
  );
  background-size: 400% 400%;
  filter: blur(4px);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
  animation: none;
}

.glass-card:hover .glass-border-animation {
  opacity: 1;
  animation: animateGlassBorder 3s ease infinite;
}

@keyframes animateGlassBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.dark .glass-border-animation {
  background: linear-gradient(
    -45deg, 
    rgba(255, 215, 0, 0), 
    rgba(255, 215, 0, 0.15), 
    rgba(255, 215, 0, 0.3), 
    rgba(255, 215, 0, 0.15), 
    rgba(255, 215, 0, 0)
  );
  filter: blur(3px);
}

.glass-card-hover {
  box-shadow: 0 0 15px 1px rgba(239, 68, 68, 0.25);
}

.dark .glass-card-hover {
  box-shadow: 0 0 20px 2px rgba(255, 215, 0, 0.25);
}

@media (prefers-color-scheme: light) {
  .glass-card-hover {
    box-shadow: 0 0 15px 3px rgba(239, 68, 68, 0.4);
  }
}

/* ألوان متنوعة للبطاقات المختلفة */
.glass-card-green {
  border-right: 4px solid #10b981;
}

.glass-card-amber {
  border-right: 4px solid #f59e0b;
}

.glass-card-blue {
  border-right: 4px solid #3b82f6;
}

.glass-card-purple {
  border-right: 4px solid #8b5cf6;
}
