
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { useState } from "react";

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
  isLoading?: boolean;
  valueColor?: "green" | "red" | "default";
  effectColor?: "red" | "green" | "amber" | "blue";
}

export function StatCard({
  title,
  value,
  icon,
  description,
  trend,
  className,
  isLoading = false,
  valueColor = "default",
  effectColor = "red",
}: StatCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // تحديد لون النص للقيمة
  const getValueColorClasses = () => {
    switch (valueColor) {
      case "green":
        return "bg-gradient-to-r from-green-500 to-green-600 dark:from-green-400 dark:to-green-500";
      case "red":
        return "bg-gradient-to-r from-red-500 to-red-600 dark:from-red-400 dark:to-red-500";
      default:
        return "bg-gradient-to-r from-primary to-primary/70 dark:from-amber-400 dark:to-amber-300";
    }
  };

  // تحديد لون خلفية الأيقونة حسب لون القيمة
  const getIconBgColor = () => {
    switch (valueColor) {
      case "green":
        return "bg-green-500/10";
      case "red":
        return "bg-red-500/10";
      default:
        return "bg-primary/10";
    }
  };

  // تحديد فئة التأثير حسب اللون المحدد
  const getEffectClass = () => {
    return `effect-${effectColor}`;
  };

  return (
    <div 
      className={cn(
        "effect-card glass-effect",
        isHovered ? "scale-[1.01]" : "",
        getEffectClass(),
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* تأثير حدود متحركة عند التحويم */}
      {isHovered && (
        <div className="animated-border"></div>
      )}
      
      <Card className="relative z-10 border-0 backdrop-blur-sm bg-white/80 dark:bg-gray-800/80 overflow-hidden rounded-[0.7rem] transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium card-title">{title}</CardTitle>
          <div className={cn("p-2 rounded-full card-icon", getIconBgColor())}>{icon}</div>
        </CardHeader>
        <CardContent className="card-content">
          {isLoading ? (
            <>
              <Skeleton className="h-8 w-20 mb-2" />
              <Skeleton className="h-4 w-24" />
            </>
          ) : (
            <>
              <div className={`text-2xl font-bold card-value ${getValueColorClasses()} bg-clip-text text-transparent`}>{value}</div>
              {(description || trend) && (
                <p className="text-xs text-muted-foreground mt-1 card-description">
                  {trend && (
                    <span
                      className={cn(
                        "font-medium mr-1",
                        trend.isPositive ? "text-green-500" : "text-red-500"
                      )}
                    >
                      {trend.isPositive ? "+" : "-"}
                      {trend.value}%
                    </span>
                  )}
                  {description}
                </p>
              )}
            </>
          )}
        </CardContent>
      </Card>
      
      {/* حدود خفيفة */}
      <div className="absolute inset-0 rounded-xl border-2 border-[#EF4343]/30 dark:border-amber-500/20 pointer-events-none"></div>
    </div>
  );
}
