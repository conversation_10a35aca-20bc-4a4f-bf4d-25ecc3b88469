
import { SimplePagination } from "@/components/ui/pagination-simple";

interface TransactionPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function TransactionPagination({ 
  currentPage, 
  totalPages, 
  onPageChange 
}: TransactionPaginationProps) {
  if (totalPages <= 1) return null;

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  return (
    <SimplePagination
      currentPage={currentPage}
      totalPages={totalPages}
      onNextPage={handleNextPage}
      onPreviousPage={handlePreviousPage}
      hasNextPage={currentPage < totalPages}
      hasPreviousPage={currentPage > 1}
    />
  );
}
