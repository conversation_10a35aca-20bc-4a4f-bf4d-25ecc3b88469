
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { NotificationRuleFormValues } from "./types";
import { fetchNotificationRules, saveNotificationRules } from "./notificationRulesApi";

export function useNotificationRules() {
  const [balanceAlertEnabled, setBalanceAlertEnabled] = useState<boolean>(false);
  const [minBalance, setMinBalance] = useState<string>("50");
  const [transactionAlertEnabled, setTransactionAlertEnabled] = useState<boolean>(false);
  const [transactionTypes, setTransactionTypes] = useState<string[]>([]);
  const [transactionAmountOption, setTransactionAmountOption] = useState<'all' | 'threshold'>('all');
  const [minTransactionAmount, setMinTransactionAmount] = useState<string>("1000");
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadNotificationRules();
  }, []);

  const loadNotificationRules = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const rules = await fetchNotificationRules();
      if (rules) {
        setBalanceAlertEnabled(rules.balanceAlertEnabled);
        setMinBalance(rules.minBalance);
        setTransactionAlertEnabled(rules.transactionAlertEnabled);
        setTransactionTypes(rules.transactionTypes);
        setTransactionAmountOption(rules.transactionAmountOption);
        setMinTransactionAmount(rules.minTransactionAmount);
      }
    } catch (err) {
      console.error("Error loading notification rules:", err);
      setError("فشل في تحميل قواعد الإشعارات");
    } finally {
      setIsLoading(false);
    }
  };

  const handleTransactionTypeChange = (type: string) => {
    setTransactionTypes((prev) => {
      if (prev.includes(type)) {
        return prev.filter((t) => t !== type);
      } else {
        return [...prev, type];
      }
    });
  };

  const validateRules = (): boolean => {
    if (balanceAlertEnabled && (!minBalance || isNaN(Number(minBalance)))) {
      toast.error("يرجى إدخال قيمة صحيحة للحد الأدنى للرصيد");
      return false;
    }

    if (
      transactionAlertEnabled &&
      transactionAmountOption === "threshold" &&
      (!minTransactionAmount || isNaN(Number(minTransactionAmount)))
    ) {
      toast.error("يرجى إدخال قيمة صحيحة للحد الأدنى للمعاملة");
      return false;
    }

    if (transactionAlertEnabled && transactionTypes.length === 0) {
      toast.error("يرجى اختيار نوع معاملة واحد على الأقل");
      return false;
    }

    return true;
  };

  const handleSaveRules = async () => {
    if (!validateRules()) return;

    setIsSaving(true);
    
    try {
      const values: NotificationRuleFormValues = {
        balanceAlertEnabled,
        minBalance,
        transactionAlertEnabled,
        transactionTypes,
        transactionAmountOption,
        minTransactionAmount,
      };

      const success = await saveNotificationRules(values);

      if (success) {
        toast.success("تم حفظ قواعد الإشعارات بنجاح");
      } else {
        toast.error("حدث خطأ أثناء حفظ قواعد الإشعارات");
      }
    } catch (err) {
      console.error("Error saving notification rules:", err);
      toast.error("حدث خطأ أثناء حفظ قواعد الإشعارات");
    } finally {
      setIsSaving(false);
    }
  };

  return {
    balanceAlertEnabled,
    setBalanceAlertEnabled,
    minBalance,
    setMinBalance,
    transactionAlertEnabled,
    setTransactionAlertEnabled,
    transactionTypes,
    transactionAmountOption,
    setTransactionAmountOption,
    minTransactionAmount,
    setMinTransactionAmount,
    isSaving,
    isLoading,
    error,
    handleTransactionTypeChange,
    handleSaveRules
  };
}
