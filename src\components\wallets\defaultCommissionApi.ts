
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { DefaultCommissionFormValues } from "./commissionsTypes";
import { applyDefaultSettingsToAllWallets } from "./walletCommissionApi";

// Load default commission settings from database
export const loadDefaultCommissionSettings = async () => {
  try {
    // First try to get existing settings
    const { data, error } = await supabase
      .from("commission_settings")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(1)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        // No data exists, create default settings
        console.log("No commission settings found, creating default settings");
        
        const defaultSettings = {
          default_deposit_rate: 0,
          default_withdrawal_rate: 1,
          apply_to_all: false
        };
        
        const { data: newSettings, error: createError } = await supabase
          .from("commission_settings")
          .insert([defaultSettings])
          .select()
          .single();
          
        if (createError) {
          console.error("Error creating default commission settings:", createError);
          toast.error("حدث خطأ أثناء إنشاء إعدادات العمولة");
          return defaultSettings;
        }
        
        return newSettings;
      } else {
        console.error("Error loading default commission settings:", error);
        toast.error("حدث خطأ أثناء تحميل إعدادات العمولة");
        return null;
      }
    }

    return data;
  } catch (error) {
    console.error("Error loading default commission settings:", error);
    toast.error("حدث خطأ أثناء تحميل إعدادات العمولة");
    return null;
  }
};

// Save commission settings to database
export const saveDefaultCommissionSettings = async (data: DefaultCommissionFormValues) => {
  try {
    // Check if settings exist
    const existingSettings = await loadDefaultCommissionSettings();
    
    // Check if existingSettings is not null and has an id property
    if (existingSettings && 'id' in existingSettings) {
      // Update existing settings
      const { error } = await supabase
        .from("commission_settings")
        .update({
          default_deposit_rate: data.defaultDepositRate,
          default_withdrawal_rate: data.defaultWithdrawalRate,
          apply_to_all: data.applyToAll,
          updated_at: new Date().toISOString()
        })
        .eq("id", existingSettings.id as string);
      
      if (error) {
        console.error("Error saving default commission settings:", error);
        toast.error("حدث خطأ أثناء حفظ إعدادات العمولة");
        return null;
      }
    } else {
      // Create new settings
      const { error } = await supabase
        .from("commission_settings")
        .insert([{
          default_deposit_rate: data.defaultDepositRate,
          default_withdrawal_rate: data.defaultWithdrawalRate,
          apply_to_all: data.applyToAll,
        }]);
      
      if (error) {
        console.error("Error creating default commission settings:", error);
        toast.error("حدث خطأ أثناء إنشاء إعدادات العمولة");
        return null;
      }
    }

    // If apply to all is true
    if (data.applyToAll) {
      await applyDefaultSettingsToAllWallets(data.defaultDepositRate, data.defaultWithdrawalRate);
    }

    toast.success("تم حفظ إعدادات العمولة بنجاح");
    return data;
  } catch (error) {
    console.error("Error saving default commission settings:", error);
    toast.error("حدث خطأ أثناء حفظ إعدادات العمولة");
    return null;
  }
};
