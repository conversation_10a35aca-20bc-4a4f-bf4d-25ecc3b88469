
/* Custom Switch Styling */
.custom-switch {
  --switch-height: 22px;
  --switch-width: 42px;
  --thumb-size: 18px;
  --thumb-color: #ffffff;
  --thumb-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  --track-light: linear-gradient(to right, #ff4d6d, #ff6b6b);
  --track-dark: linear-gradient(to right, #ffd700, #ffb700);
  
  position: relative;
  display: inline-flex;
  height: var(--switch-height);
  width: var(--switch-width);
  flex-shrink: 0;
  cursor: pointer;
  border-radius: 9999px;
  border: 2px solid transparent;
  padding: 0;
  transition: all 0.2s ease-in-out;
}

.custom-switch[data-state="checked"] {
  background: var(--track-light);
}

.dark .custom-switch[data-state="checked"] {
  background: var(--track-dark);
}

.custom-switch[data-state="unchecked"] {
  background: #e2e8f0;
}

.dark .custom-switch[data-state="unchecked"] {
  background: #1e293b;
}

.custom-switch:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(66, 153, 225, 0.6);
}

.custom-switch-thumb {
  display: block;
  width: var(--thumb-size);
  height: var(--thumb-size);
  background-color: var(--thumb-color);
  border-radius: 9999px;
  box-shadow: var(--thumb-shadow);
  transition: transform 0.2s ease-in-out;
  transform: translateX(0);
}

.custom-switch[data-state="checked"] .custom-switch-thumb {
  transform: translateX(calc(var(--switch-width) - var(--thumb-size) - 4px));
}
