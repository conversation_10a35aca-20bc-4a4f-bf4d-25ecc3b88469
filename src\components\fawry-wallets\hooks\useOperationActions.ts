
import { useState } from "react";

/**
 * Interface for the operation action states and handlers
 */
interface OperationDialogState {
  addDialogOpen: boolean;
  deleteAllDialogOpen: boolean;
  editDialogOpen: boolean;
  deleteDialogOpen: boolean;
  currentOperationId: string | null;
}

/**
 * Hook to manage operation action dialogs and operations
 * @returns Object with dialog states and handler functions
 */
export const useOperationActions = () => {
  // Initialize all dialog states
  const [dialogState, setDialogState] = useState<OperationDialogState>({
    addDialogOpen: false,
    deleteAllDialogOpen: false,
    editDialogOpen: false,
    deleteDialogOpen: false,
    currentOperationId: null,
  });

  /**
   * Updates a specific dialog's open state
   * @param dialogName Name of the dialog to update
   * @param isOpen New open state for the dialog
   */
  const updateDialogState = (
    dialogName: keyof Omit<OperationDialogState, "currentOperationId">, 
    isOpen: boolean
  ) => {
    setDialogState(prev => ({
      ...prev,
      [dialogName]: isOpen,
    }));
  };

  /**
   * Sets the current operation for edit/delete operations
   * @param operationId ID of the selected operation
   */
  const setCurrentOperation = (operationId: string | null) => {
    setDialogState(prev => ({
      ...prev,
      currentOperationId: operationId,
    }));
  };

  // Handler functions for each dialog
  const handleAddOperation = () => {
    updateDialogState("addDialogOpen", true);
  };

  const handleDeleteAll = () => {
    updateDialogState("deleteAllDialogOpen", true);
  };

  const handleEditOperation = (operationId: string) => {
    setCurrentOperation(operationId);
    updateDialogState("editDialogOpen", true);
  };

  const handleDeleteOperation = (operationId: string) => {
    setCurrentOperation(operationId);
    updateDialogState("deleteDialogOpen", true);
  };

  return {
    // Dialog states
    addDialogOpen: dialogState.addDialogOpen,
    deleteAllDialogOpen: dialogState.deleteAllDialogOpen,
    editDialogOpen: dialogState.editDialogOpen,
    deleteDialogOpen: dialogState.deleteDialogOpen,
    currentOperationId: dialogState.currentOperationId,
    
    // Dialog state setters
    setAddDialogOpen: (open: boolean) => updateDialogState("addDialogOpen", open),
    setDeleteAllDialogOpen: (open: boolean) => updateDialogState("deleteAllDialogOpen", open),
    setEditDialogOpen: (open: boolean) => updateDialogState("editDialogOpen", open),
    setDeleteDialogOpen: (open: boolean) => updateDialogState("deleteDialogOpen", open),
    
    // Handler functions
    handleAddOperation,
    handleDeleteAll,
    handleEditOperation,
    handleDeleteOperation,
    setCurrentOperation
  };
};
