
/**
 * Types for transaction limits functionality
 */

export interface MonthlyLimitResult {
  /** Whether the transaction exceeds the monthly limit */
  exceeds: boolean;
  /** The current total amount for the month */
  total: number;
  /** The remaining amount that can be received this month */
  remaining: number;
  /** Error message if any */
  error?: string;
}

export interface MonthlyLimitOptions {
  /** The monthly limit amount in currency units */
  monthlyLimit: number;
}

export interface TransactionPeriod {
  /** Start date of the period */
  startDate: string;
  /** End date of the period */
  endDate: string;
}

// Add the TransactionType type definition
export type TransactionType = "receive" | "send";
