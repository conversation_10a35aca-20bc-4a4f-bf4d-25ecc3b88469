
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Sim } from "@/hooks/useTransactionForm";
import { useState, useEffect } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";

interface SimSelectorProps {
  sims: Sim[];
  value: string;
  onChange: (value: string) => void;
  walletId: string;
  isRequired?: boolean;
}

export function SimSelector({
  sims,
  value,
  onChange,
  walletId,
  isRequired = false
}: SimSelectorProps) {
  // Filter sims based on selected wallet
  const filteredSims = walletId ? sims.filter(sim => sim.wallet_id === walletId) : [];

  // Find the selected SIM to display its balance if selected
  const selectedSim = value ? sims.find(sim => sim.id === value) : null;

  // State for showing inactive SIM alert
  const [showInactiveAlert, setShowInactiveAlert] = useState(false);
  
  // If wallet changed and current sim doesn't belong to it, reset sim selection
  useEffect(() => {
    if (walletId && value) {
      const simBelongsToWallet = sims.some(sim => sim.id === value && sim.wallet_id === walletId);
      if (!simBelongsToWallet) {
        onChange("");
      }
    }
  }, [walletId, sims, value, onChange]);

  // Check if the selected SIM is a mobile number
  const isMobileSim = selectedSim && /^(010|011|012|015)/.test(selectedSim.number);

  // Handle sim change with inactive check
  const handleSimChange = (simId: string) => {
    if (simId === "none") {
      setShowInactiveAlert(false);
      onChange("");
      return;
    }
    
    const selectedSim = sims.find(sim => sim.id === simId);

    // Check if the selected SIM is inactive
    if (selectedSim && selectedSim.active_status === false) {
      setShowInactiveAlert(true);
    } else {
      setShowInactiveAlert(false);
    }

    // Call original onChange handler
    onChange(simId);
  };
  
  const hasZeroBalance = selectedSim && selectedSim.balance === 0;
  
  return <div>
      <Select value={value} onValueChange={handleSimChange} required={isRequired}>
        <SelectTrigger className={!value && isRequired ? "border-red-500" : ""}>
          <SelectValue placeholder={`اختر الشريحة ${isRequired ? '(إجباري)' : '(اختياري)'}`} />
        </SelectTrigger>
        <SelectContent>
          {filteredSims.length === 0 ? (
            walletId ? (
              <SelectItem value="no-sims-available" disabled>
                لا توجد شرائح متاحة لهذه المحفظة
              </SelectItem>
            ) : (
              <SelectItem value="select-wallet-first" disabled>
                الرجاء اختيار المحفظة أولاً
              </SelectItem>
            )
          ) : (
            filteredSims.map(sim => (
              <SelectItem key={sim.id} value={sim.id}>
                {sim.number} {sim.displayProvider ? `(${sim.displayProvider})` : ""}
                {sim.active_status === false && " - غير نشطة"}
                {sim.balance !== undefined && ` - الرصيد: ${sim.balance} ج.م`}
                {/^(010|011|012|015)/.test(sim.number) && " - موبايل"}
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      
      {showInactiveAlert && <Alert variant="destructive" className="mt-2 text-right">
          <AlertDescription>
            تنبيه : هذه الشريحة غير نشطة تواصل مع مدير الفرع لتفعيل الشريحة
          </AlertDescription>
        </Alert>}
      
      {selectedSim && selectedSim.balance !== undefined && <div className="mt-1 text-sm text-muted-foreground text-right">
          الرصيد: <span className="font-medium">{selectedSim.balance} ج.م</span>
          
          {isMobileSim && <div className="mt-1 flex items-center gap-1 text-gray-600 dark:text-gray-400 text-sm">
              <Info className="h-4 w-4" />
              <span>نوع الشريحة: موبايل</span>
            </div>}
        </div>}
    </div>;
}
