
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { TransactionType } from "@/utils/transaction/types";

// أنواع العمليات المالية
// وظيفة لحساب العمولة تلقائيًا بناء على المبلغ ونوع العملية
export const calculateCommission = async (amount: number, type: TransactionType, walletId?: string): Promise<number> => {
  if (!amount || amount <= 0) return 0;
  
  try {
    // التحقق أولاً مما إذا كانت المحفظة هي محفظة فوري
    if (walletId) {
      const { data: walletData, error: walletError } = await supabase
        .from("wallets")
        .select("wallet_type")
        .eq("id", walletId)
        .single();
        
      if (!walletError && walletData && walletData.wallet_type === "fawry") {
        // لمحفظة فوري، نعيد صفر لأن العمولة ستُدخل يدوياً
        return 0;
      }
    }
    
    // الحصول على إعدادات العمولة من قاعدة البيانات
    const { data, error } = await supabase
      .from("commission_settings")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(1)
      .single();
    
    if (error) {
      console.error("Error fetching commission settings:", error);
      // استخدام القيم الافتراضية في حالة حدوث خطأ
      return calculateDefaultCommission(amount, type);
    }
    
    // حساب العمولة بناءً على نوع العملية
    const rate = type === 'receive' 
      ? data.default_deposit_rate / 100 
      : data.default_withdrawal_rate / 100;
    
    return Math.round(amount * rate * 100) / 100;
  } catch (error) {
    console.error("Error calculating commission:", error);
    // استخدام القيم الافتراضية في حالة حدوث استثناء
    return calculateDefaultCommission(amount, type);
  }
};

// وظيفة لحساب العمولة باستخدام القيم الافتراضية
const calculateDefaultCommission = (amount: number, type: TransactionType): number => {
  // معدلات العمولة الافتراضية الجديدة (0% للإيداع و1% للسحب)
  const commissionRates = {
    receive: 0, // 0% للإيداع
    send: 0.01, // 1% للسحب
  };
  
  return Math.round(amount * commissionRates[type] * 100) / 100;
};

// وظيفة إرسال العملية إلى قاعدة البيانات
export const submitTransaction = async (transactionData: any) => {
  try {
    // تحضير البيانات للمعاملة مع التعامل مع معلومات العميل
    const { status, customer_name, customer_phone, type, ...dataToSubmit } = transactionData;
    
    // تنسيق البيانات لتتوافق مع بنية قاعدة البيانات
    const formattedData = {
      ...dataToSubmit,
      // استخدم نوع المعاملة المناسب
      transaction_type: type,
      // إضافة اسم العميل ورقم الهاتف مباشرة إلى المعاملة
      customer_name,
      customer_phone
    };

    console.log("Submitting transaction data:", formattedData);
    
    const { data: transactionResult, error } = await supabase
      .from("transactions")
      .insert([formattedData])
      .select()
      .single();
      
    if (error) throw error;
    
    // تحديث رصيد الشريحة بناءً على نوع العملية
    if (formattedData.sim_id) {
      // الحصول على الرصيد الحالي للشريحة
      const { data: simData, error: simError } = await supabase
        .from("sims")
        .select("balance")
        .eq("id", formattedData.sim_id)
        .single();
        
      if (simError) {
        console.error("Error fetching SIM balance:", simError);
      } else if (simData) {
        // حساب الرصيد الجديد
        let newBalance = simData.balance;
        
        if (type === "receive") {
          // عملية استلام: إضافة المبلغ إلى الرصيد
          newBalance += Number(formattedData.amount);
        } else if (type === "send") {
          // عملية إرسال: خصم المبلغ من الرصيد
          newBalance -= Number(formattedData.amount);
        }
        
        // تحديث رصيد الشريحة
        const { error: updateError } = await supabase
          .from("sims")
          .update({ balance: newBalance })
          .eq("id", formattedData.sim_id);
          
        if (updateError) {
          console.error("Error updating SIM balance:", updateError);
        }
      }
    }
    
    toast.success("تم إضافة العملية بنجاح");
    // إعادة معرف المعاملة مع نتيجة العملية
    return {
      success: true,
      transactionId: transactionResult?.id
    };
  } catch (error) {
    console.error("Error submitting transaction:", error);
    toast.error("حدث خطأ أثناء إضافة العملية");
    return {
      success: false
    };
  }
};
