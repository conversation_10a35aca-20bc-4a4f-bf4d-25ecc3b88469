
import { ArrowDownSquare, ArrowUpSquare } from "lucide-react";
import { TableCell } from "@/components/ui/table";

interface TransactionTypeCellProps {
  transactionType: string;
}

export function TransactionTypeCell({ transactionType }: TransactionTypeCellProps) {
  return (
    <TableCell className="rtl-cell">
      <div className="cell-content">
        {transactionType === "receive" ? (
          <div className="flex items-center gap-1">
            <span>استلام</span>
            <div className="bg-green-100 dark:bg-green-900/30 p-0.5 rounded-md">
              <ArrowDownSquare className="h-4 w-4 text-green-500" />
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-1">
            <span>سحب</span>
            <div className="bg-red-100 dark:bg-red-900/30 p-0.5 rounded-md">
              <ArrowUpSquare className="h-4 w-4 text-red-500" />
            </div>
          </div>
        )}
      </div>
    </TableCell>
  );
}
