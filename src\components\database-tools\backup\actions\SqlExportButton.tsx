
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { FileDown } from "lucide-react";
import { toast } from "sonner";
import { downloadAsFile } from "@/utils/database/downloadUtils";
import { supabase } from "@/integrations/supabase/client";

export function SqlExportButton() {
  const [isSqlBackupLoading, setIsSqlBackupLoading] = useState(false);

  // وظيفة تنزيل البيانات بتنسيق SQL
  const handleSqlExport = async () => {
    setIsSqlBackupLoading(true);
    
    try {
      toast.info("جاري استخراج بيانات SQL من قاعدة البيانات...", { duration: 3000 });
      
      // تاريخ وتوقيت التنزيل للاسم
      const now = new Date();
      const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}-${String(now.getMinutes()).padStart(2, '0')}`;
      
      try {
        // استخدام Edge Function لجلب بنية قاعدة البيانات
        const response = await fetch(`https://jmpynrnnnyoiywfrpybr.supabase.co/functions/v1/get-database-structure`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImptcHlucm5ubnlvaXl3ZnJweWJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4Njg4ODUsImV4cCI6MjA2MTQ0NDg4NX0.CHY7JlpRKp681_6WKhcAHw6wNxNc-hdt_YiOwAgRTFw`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ complete: true })
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (!result.data || !result.data.tables || !Array.isArray(result.data.tables)) {
          throw new Error("لم يتم استلام بيانات صالحة من الخادم");
        }
        
        // بدء تكوين ملف SQL
        let sqlContent = `-- نسخة احتياطية من قاعدة بيانات بتاريخ ${now.toLocaleString('ar-SA')}\n`;
        sqlContent += `-- تم إنشاؤها تلقائيًا من نظام النسخ الاحتياطي\n\n`;
        
        // لكل جدول، نقوم باستخراج البيانات وتحويلها إلى تنسيق SQL INSERT
        for (const tableInfo of result.data.tables) {
          const tableName = tableInfo.table_name;
          
          // تخطي بعض الجداول الخاصة بالنظام
          if (tableName.startsWith('_') || tableName === 'schema_migrations') {
            continue;
          }
          
          // إضافة تعليق للجدول
          sqlContent += `\n-- بيانات الجدول ${tableName}\n`;
          
          try {
            // جلب بيانات الجدول مباشرة من قاعدة البيانات
            const { data: rowsData, error: rowsError } = await supabase
              .from(tableName)
              .select('*');
              
            if (rowsError) {
              console.error(`خطأ في جلب بيانات الجدول ${tableName}:`, rowsError);
              continue;
            }
            
            if (rowsData && rowsData.length > 0) {
              // استخراج أسماء الأعمدة من أول صف
              const columns = Object.keys(rowsData[0]);
              
              for (const row of rowsData) {
                // بناء عبارة INSERT لكل صف
                sqlContent += `INSERT INTO public.${tableName} (${columns.join(', ')}) VALUES (`;
                
                // إضافة قيم كل عمود
                sqlContent += columns.map(col => {
                  const value = row[col];
                  
                  if (value === null) {
                    return 'NULL';
                  } else if (typeof value === 'string') {
                    // إصلاح الاقتباسات داخل النصوص
                    return `'${value.replace(/'/g, "''")}'`;
                  } else if (typeof value === 'object') {
                    // تحويل الكائنات إلى JSON
                    return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
                  } else {
                    return value;
                  }
                }).join(', ');
                
                sqlContent += `);\n`;
              }
            }
          } catch (tableError) {
            console.error(`خطأ في معالجة الجدول ${tableName}:`, tableError);
          }
        }
        
        // تنزيل ملف SQL
        downloadAsFile(sqlContent, `database_data_${dateStr}.sql`, 'application/sql');
        
        toast.success("تم استخراج بيانات SQL بنجاح");
      } catch (error) {
        console.error("حدث خطأ أثناء الاتصال بـ Edge Function:", error);
        toast.error("حدث خطأ أثناء استخراج بيانات SQL من قاعدة البيانات");
      }
    } catch (error) {
      console.error("حدث خطأ أثناء استخراج بيانات SQL:", error);
      toast.error("حدث خطأ أثناء استخراج بيانات SQL من قاعدة البيانات");
    } finally {
      setIsSqlBackupLoading(false);
    }
  };

  return (
    <Button 
      onClick={handleSqlExport} 
      disabled={isSqlBackupLoading}
      className="w-full bg-blue-600 hover:bg-blue-700 mt-2"
      variant="default"
    >
      <FileDown className="h-4 w-4 ml-2" />
      {isSqlBackupLoading ? "جاري استخراج بيانات SQL..." : "تنزيل بيانات SQL للرفع إلى محرر SQL"}
    </Button>
  );
}
