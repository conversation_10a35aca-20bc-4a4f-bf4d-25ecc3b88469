
import { useIsMobile } from "@/hooks/use-mobile";
import { WalletActivity } from "@/services/walletActivityService";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";

interface WalletActivityTableProps {
  data: WalletActivity[];
  isLoading: boolean;
}

export function WalletActivityTable({ data, isLoading }: WalletActivityTableProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("ar-EG", {
      style: "currency",
      currency: "EGP",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const columns: TableColumn<WalletActivity>[] = [
    {
      header: "المحفظة",
      accessor: "name",
      className: "font-medium rtl-cell"
    },
    {
      header: "الرصيد الحالي",
      accessor: (wallet) => (
        <span className="rtl-currency">{formatCurrency(wallet.balance)}</span>
      ),
      className: "rtl-cell"
    },
    {
      header: "عدد العمليات",
      accessor: "transactions_count",
      className: "rtl-cell"
    },
    {
      header: "إجمالي المبالغ",
      accessor: (wallet) => (
        <span className="rtl-currency">{formatCurrency(wallet.total_amount)}</span>
      ),
      className: "rtl-cell"
    },
    {
      header: "إجمالي العمولة",
      accessor: (wallet) => (
        <span className="rtl-currency">{formatCurrency(wallet.total_commission)}</span>
      ),
      className: "rtl-cell"
    }
  ];

  return (
    <ModernTable
      data={data}
      columns={columns}
      loading={isLoading}
      keyField="id"
      emptyMessage="لا توجد بيانات متاحة للفترة المحددة"
      dir="rtl"
      mobileCardTitle={(wallet) => wallet.name}
    />
  );
}
