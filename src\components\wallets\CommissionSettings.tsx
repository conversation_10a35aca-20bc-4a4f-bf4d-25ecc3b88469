
import { useState, useEffect } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import DefaultCommissionForm from "./DefaultCommissionForm";
import WalletCommissionForm from "./WalletCommissionForm";
import { 
  loadDefaultCommissionSettings, 
  loadWalletCommissionSettings, 
  mapWalletSettingsToFormValues 
} from "./commissionsUtils";
import { DefaultCommissionFormValues, WalletCommissionFormValues } from "./commissionsTypes";

const CommissionSettings = () => {
  const [defaultValues, setDefaultValues] = useState<DefaultCommissionFormValues>({
    defaultDepositRate: 0,
    defaultWithdrawalRate: 1,
    applyToAll: false
  });
  
  const [walletCommissionValues, setWalletCommissionValues] = useState<WalletCommissionFormValues>({
    vodafoneDeposit: 0,
    vodafoneWithdrawal: 1,
    vodafoneMinCommission: 5,
    etisalatDeposit: 0,
    etisalatWithdrawal: 1,
    etisalatMinCommission: 5,
    orangeDeposit: 0,
    orangeWithdrawal: 1,
    orangeMinCommission: 5,
    instaPayDeposit: 0,
    instaPayWithdrawal: 1,
    instaPayMinCommission: 5,
  });
  
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true);
      
      // Load default commission settings
      const defaultSettings = await loadDefaultCommissionSettings();
      if (defaultSettings) {
        setDefaultValues({
          defaultDepositRate: defaultSettings.default_deposit_rate,
          defaultWithdrawalRate: defaultSettings.default_withdrawal_rate,
          applyToAll: defaultSettings.apply_to_all
        });
      }
      
      // Load wallet-specific commission settings
      const walletSettings = await loadWalletCommissionSettings();
      if (walletSettings) {
        const formValues = mapWalletSettingsToFormValues(walletSettings);
        setWalletCommissionValues(formValues);
      }
      
      setIsLoading(false);
    };
    
    loadSettings();
  }, []);
  
  // Handler for the onSave prop that's required by DefaultCommissionForm
  const handleDefaultCommissionSave = (data: DefaultCommissionFormValues) => {
    // Update the local state with saved values
    setDefaultValues(data);
    
    // If applyToAll is true, update walletCommissionValues as well
    if (data.applyToAll) {
      // Create new wallet commission values based on default rates
      const updatedWalletValues = { ...walletCommissionValues };
      
      // Apply default rates to all wallets
      Object.keys(updatedWalletValues).forEach(key => {
        if (key.includes('Deposit')) {
          updatedWalletValues[key as keyof WalletCommissionFormValues] = data.defaultDepositRate;
        } else if (key.includes('Withdrawal')) {
          updatedWalletValues[key as keyof WalletCommissionFormValues] = data.defaultWithdrawalRate;
        }
      });
      
      setWalletCommissionValues(updatedWalletValues);
    }
  };
  
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">جاري تحميل إعدادات العمولة...</div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <Alert variant="destructive" className="bg-amber-50 dark:bg-amber-900/20 border-amber-300 dark:border-amber-700">
        <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
        <AlertDescription className="text-amber-800 dark:text-amber-300">
          تنبيه: سيتم تطبيق العمولات على جميع المعاملات الجديدة. لا يمكن تغيير العمولات للمعاملات السابقة.
        </AlertDescription>
      </Alert>
      
      <Tabs defaultValue="default">
        <TabsList className="mb-4">
          <TabsTrigger value="default">الإعدادات الافتراضية</TabsTrigger>
          <TabsTrigger value="wallet-specific">إعدادات المحافظ</TabsTrigger>
        </TabsList>
        
        <TabsContent value="default">
          <Card>
            <CardHeader>
              <CardTitle>إعدادات العمولة الافتراضية</CardTitle>
            </CardHeader>
            <CardContent>
              <DefaultCommissionForm 
                defaultValues={defaultValues} 
                onSave={handleDefaultCommissionSave} 
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="wallet-specific">
          <Card>
            <CardHeader>
              <CardTitle>إعدادات عمولات المحافظ</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <WalletCommissionForm defaultValues={walletCommissionValues} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CommissionSettings;
