
import {
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog";
import { DebtForm } from "./DebtForm";
import { Debt } from "@/types/debt.types";

interface DebtEditDialogProps {
  debt: Debt | null;
  onClose: () => void;
  onSuccess: () => void;
}

export function DebtEditDialog({ debt, onClose, onSuccess }: DebtEditDialogProps) {
  if (!debt) return null;
  
  return (
    <DialogContent className="sm:max-w-[500px]">
      <DialogHeader>
        <DialogTitle>تعديل بيانات الدين</DialogTitle>
        <DialogDescription>
          قم بتعديل بيانات الدين حسب الحاجة
        </DialogDescription>
      </DialogHeader>
      <DebtForm
        debt={debt}
        onSuccess={onSuccess}
        onCancel={onClose}
      />
    </DialogContent>
  );
}
