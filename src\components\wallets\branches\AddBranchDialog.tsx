
import { useState } from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { addBranch } from "@/services/branchService";

interface AddBranchDialogProps {
  onBranchAdded: () => void;
}

export function AddBranchDialog({ onBranchAdded }: AddBranchDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [newBranch, setNewBranch] = useState({
    name: "",
    address: "",
    manager: "",
  });

  const handleAdd = async () => {
    if (!newBranch.name || !newBranch.address || !newBranch.manager) {
      return;
    }
    
    const result = await addBranch({
      name: newBranch.name,
      address: newBranch.address,
      manager: newBranch.manager,
      active_status: true
    });
    
    if (result) {
      setNewBranch({ name: "", address: "", manager: "" });
      setIsOpen(false);
      onBranchAdded();
    }
  };

  return (
    <>
      <Button onClick={() => setIsOpen(true)} className="gap-2">
        <Plus className="h-4 w-4" />
        <span>إضافة فرع جديد</span>
      </Button>
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>إضافة فرع جديد</DialogTitle>
            <DialogDescription>
              أدخل بيانات الفرع الجديد في النموذج أدناه
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="branch-name">اسم الفرع</Label>
              <Input
                id="branch-name"
                placeholder="أدخل اسم الفرع"
                value={newBranch.name}
                onChange={(e) => setNewBranch({ ...newBranch, name: e.target.value })}
                dir="rtl"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="branch-address">العنوان</Label>
              <Input
                id="branch-address"
                placeholder="أدخل عنوان الفرع"
                value={newBranch.address}
                onChange={(e) => setNewBranch({ ...newBranch, address: e.target.value })}
                dir="rtl"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="branch-manager">مدير الفرع</Label>
              <Input
                id="branch-manager"
                placeholder="أدخل اسم مدير الفرع"
                value={newBranch.manager}
                onChange={(e) => setNewBranch({ ...newBranch, manager: e.target.value })}
                dir="rtl"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={handleAdd}>
              إضافة فرع
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
