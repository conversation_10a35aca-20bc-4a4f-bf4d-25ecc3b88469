
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Customer, Debt, DebtFormValues, debtFormSchema } from "@/types/debt.types";

interface UseDebtFormProps {
  debt?: Debt;
  onSuccess: () => void;
}

export function useDebtForm({ debt, onSuccess }: UseDebtFormProps) {
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(true);

  // Initialize form with values
  const form = useForm<DebtFormValues>({
    resolver: zodResolver(debtFormSchema),
    defaultValues: {
      customer_id: debt?.customer_id || "",
      amount: debt ? Number(debt.amount) : 0,
      due_date: debt?.due_date ? new Date(debt.due_date) : null,
      notes: debt?.notes || "",
      status: debt?.status || "pending",
    },
  });

  // Fetch customers
  useEffect(() => {
    async function fetchCustomers() {
      try {
        // Fetch regular customers
        const { data, error } = await supabase
          .from("customers")
          .select("id, name, phone")
          .order("name");
          
        if (error) {
          throw error;
        }
        
        setCustomers(data || []);
      } catch (error) {
        console.error("Error fetching customers:", error);
        toast.error("حدث خطأ أثناء جلب بيانات العملاء");
      } finally {
        setLoadingCustomers(false);
      }
    }
    
    fetchCustomers();
  }, []);

  // Submit form
  const onSubmit = async (values: DebtFormValues) => {
    setLoading(true);
    try {
      // First, check if the customer exists in the customers table - using maybeSingle instead of single
      const { data: existingCustomer, error: customerError } = await supabase
        .from("customers")
        .select("id")
        .eq("id", values.customer_id)
        .maybeSingle();
      
      if (customerError) {
        console.error("Error checking customer:", customerError);
      }
      
      if (!existingCustomer) {
        // Customer doesn't exist - check if it's a special customer
        const { data: specialCustomer, error: specialCustomerError } = await supabase
          .from("special_customers")
          .select("name, phone")
          .eq("id", values.customer_id)
          .single();
          
        if (specialCustomerError) {
          toast.error("العميل غير موجود");
          setLoading(false);
          return;
        }
        
        // Insert the special customer into the regular customers table
        const { data: newCustomer, error: insertError } = await supabase
          .from("customers")
          .insert({
            id: values.customer_id, // Use the same ID as the special customer
            name: specialCustomer.name,
            phone: specialCustomer.phone,
            notes: "تم إنشاؤه تلقائيًا من العملاء المميزين"
          })
          .select()
          .single();
          
        if (insertError) {
          console.error("Error creating customer:", insertError);
          toast.error("حدث خطأ أثناء إنشاء العميل");
          setLoading(false);
          return;
        }
      }
      
      const debtData = {
        customer_id: values.customer_id,
        amount: values.amount,
        due_date: values.due_date ? values.due_date.toISOString() : null,
        notes: values.notes,
        status: values.status,
      };
      
      let error;
      
      if (debt) {
        // Update existing debt
        const result = await supabase
          .from("debts")
          .update(debtData)
          .eq("id", debt.id);
          
        error = result.error;
      } else {
        // Insert new debt
        const result = await supabase
          .from("debts")
          .insert([debtData]);
          
        error = result.error;
      }
      
      if (error) {
        console.error("Error saving debt:", error);
        throw error;
      }
      
      toast.success(debt ? "تم تحديث الدين بنجاح" : "تم إضافة الدين بنجاح");
      onSuccess();
    } catch (error) {
      console.error("Error saving debt:", error);
      toast.error(debt ? "حدث خطأ أثناء تحديث الدين" : "حدث خطأ أثناء إضافة الدين");
    } finally {
      setLoading(false);
    }
  };

  return {
    form,
    loading,
    customers,
    loadingCustomers,
    onSubmit: form.handleSubmit(onSubmit),
  };
}
