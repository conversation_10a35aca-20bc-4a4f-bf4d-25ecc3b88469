
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { Operation, Wallet, operationSchema, OperationFormValues } from "../utils/operationSchema";
import { updateOperationWithDifferentWallet, updateOperationWithSameWallet } from "../utils/walletOperationUtils";

interface UseEditFawryOperationFormProps {
  operation: Operation;
  wallets: Wallet[];
  onClose: () => void;
  onOperationUpdated: () => void;
}

/**
 * Hook for handling the edit fawry operation form
 */
export const useEditFawryOperationForm = ({
  operation,
  wallets,
  onClose,
  onOperationUpdated,
}: UseEditFawryOperationFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [originalOperation, setOriginalOperation] = useState<Operation | null>(null);
  const { toast } = useToast();

  // Store the original operation for comparison later
  useEffect(() => {
    if (operation) {
      setOriginalOperation({...operation});
    }
  }, [operation]);

  // Initialize the form with operation values
  const form = useForm<OperationFormValues>({
    resolver: zodResolver(operationSchema),
    defaultValues: {
      operation_type: operation.operation_type,
      amount: operation.amount,
      commission: operation.commission,
      wallet_id: operation.wallet_id,
    },
    // Reset values when operation changes
    values: {
      operation_type: operation.operation_type,
      amount: operation.amount,
      commission: operation.commission,
      wallet_id: operation.wallet_id,
    },
  });

  const handleClose = () => {
    onClose();
  };

  const onSubmit = async (values: OperationFormValues) => {
    setIsSubmitting(true);

    try {
      let success: boolean;

      if (values.wallet_id !== operation.wallet_id) {
        // Update with a different wallet
        success = await updateOperationWithDifferentWallet(operation, values, toast);
      } else {
        // Update with the same wallet
        success = await updateOperationWithSameWallet(operation, values, toast);
      }

      if (success) {
        toast({
          title: "تم التحديث بنجاح",
          description: "تم تحديث العملية بنجاح",
        });

        handleClose();
        onOperationUpdated();
      }
    } catch (error) {
      console.error("Error updating operation:", error);
      toast({
        variant: "destructive",
        title: "خطأ في التحديث",
        description: "حدث خطأ أثناء تحديث العملية. الرجاء المحاولة مرة أخرى.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    form,
    isSubmitting,
    onSubmit,
    handleClose
  };
};
