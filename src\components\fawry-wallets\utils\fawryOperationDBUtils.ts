
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

/**
 * Fetches wallet balance from database
 */
export const getWalletBalance = async (walletId: string): Promise<number | null> => {
  try {
    const { data, error } = await supabase
      .from("fawry_wallets")
      .select("balance")
      .eq("id", walletId)
      .single();

    if (error) {
      console.error("Error fetching wallet balance:", error);
      return null;
    }
    
    return data?.balance || 0;
  } catch (error) {
    console.error("Exception fetching wallet balance:", error);
    return null;
  }
};

/**
 * Updates operation data in database
 */
export const updateOperation = async (
  operationId: string, 
  operationData: {
    operation_type: string;
    amount: number;
    commission: number;
    wallet_id: string;
  }
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("fawry_wallet_operations")
      .update({
        operation_type: operationData.operation_type,
        amount: operationData.amount,
        commission: operationData.commission || 0,
        wallet_id: operationData.wallet_id,
      })
      .eq("id", operationId);

    if (error) {
      console.error("Error updating operation:", error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Exception updating operation:", error);
    return false;
  }
};

/**
 * Updates wallet balance in database
 */
export const updateWalletBalance = async (walletId: string, newBalance: number): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("fawry_wallets")
      .update({ balance: newBalance })
      .eq("id", walletId);

    if (error) {
      console.error("Error updating wallet balance:", error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Exception updating wallet balance:", error);
    return false;
  }
};
