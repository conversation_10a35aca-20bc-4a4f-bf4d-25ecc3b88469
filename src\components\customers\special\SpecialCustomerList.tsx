
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription // <-- FIX: add this import!
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";
import { Edit, Trash } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { SpecialCustomerForm } from "./SpecialCustomerForm";
import { useIsMobile } from "@/hooks/use-mobile";
import { TableCard, TableCardRow } from "@/components/ui/table";

export interface SpecialCustomer {
  id: string;
  name: string;
  phone: string;
  notes: string | null;
  created_at: string;
}

export function SpecialCustomerList() {
  const [specialCustomers, setSpecialCustomers] = useState<SpecialCustomer[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingCustomer, setEditingCustomer] = useState<SpecialCustomer | null>(null);
  const [deletingCustomerId, setDeletingCustomerId] = useState<string | null>(null);
  const isMobile = useIsMobile();

  const fetchSpecialCustomers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("special_customers")
        .select("*")
        .order("name", { ascending: true });
      
      if (error) throw error;
      setSpecialCustomers(data || []);
    } catch (error) {
      console.error("Error fetching special customers:", error);
      toast.error("حدث خطأ أثناء جلب بيانات العملاء المميزين");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSpecialCustomers();
  }, []);

  const handleEdit = (customer: SpecialCustomer) => {
    setEditingCustomer(customer);
  };

  const handleDelete = (customerId: string) => {
    setDeletingCustomerId(customerId);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingCustomerId) return;

    try {
      const { error } = await supabase
        .from("special_customers")
        .delete()
        .eq("id", deletingCustomerId);

      if (error) throw error;
      
      toast.success("تم حذف العميل المميز بنجاح");
      fetchSpecialCustomers();
    } catch (error) {
      console.error("Error deleting special customer:", error);
      toast.error("حدث خطأ أثناء حذف العميل المميز");
    } finally {
      setDeletingCustomerId(null);
    }
  };

  if (loading) {
    return <div className="text-center py-6">جاري تحميل البيانات...</div>;
  }

  if (specialCustomers.length === 0) {
    return <div className="text-center py-6">لا يوجد عملاء مميزين</div>;
  }

  // Define columns for the table
  const columns: TableColumn<SpecialCustomer>[] = [
    {
      header: "اسم العميل",
      accessor: "name",
      className: "font-medium"
    },
    {
      header: "رقم الهاتف",
      accessor: (customer) => customer.phone || "—"
    },
    {
      header: "ملاحظات",
      accessor: (customer) => customer.notes || "—"
    },
    {
      header: "إجراءات",
      accessor: (customer) => (
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => handleEdit(customer)}
            className="rtl-action-button rtl-action-button-edit"
          >
            <Edit className="h-4 w-4" />
            <span>تعديل</span>
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => handleDelete(customer.id)}
            className="rtl-action-button rtl-action-button-destructive"
          >
            <Trash className="h-4 w-4" />
            <span>حذف</span>
          </Button>
        </div>
      ),
      className: "text-right"
    }
  ];

  return (
    <>
      {isMobile ? (
        <div className="space-y-4 animate-fadeIn">
          {specialCustomers.map((customer) => (
            <TableCard 
              key={customer.id} 
              title={customer.name}
              className="mobile-card hover:shadow-md transition-all"
              actions={
                <div className="rtl-actions-group-mobile p-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(customer)}
                    title="تعديل"
                    className="rtl-action-button rtl-action-button-edit rtl-action-button-mobile"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(customer.id)}
                    title="حذف"
                    className="rtl-action-button rtl-action-button-destructive rtl-action-button-mobile"
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              }
            >
              <TableCardRow label="رقم الهاتف" value={customer.phone || "—"} className="table-card-row" />
              {customer.notes && <TableCardRow label="ملاحظات" value={customer.notes} className="table-card-row" />}
            </TableCard>
          ))}
        </div>
      ) : (
        <ModernTable
          data={specialCustomers}
          columns={columns}
          loading={false}
          keyField="id"
          emptyMessage="لا يوجد عملاء مميزين"
          dir="rtl"
        />
      )}

      {/* Edit Customer Dialog */}
      <Dialog
        open={!!editingCustomer}
        onOpenChange={(open) => !open && setEditingCustomer(null)}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>تعديل بيانات العميل المميز</DialogTitle>
            <DialogDescription>
              يرجى تعديل حقول العميل المميز ثم الضغط على حفظ لإتمام التغييرات.
            </DialogDescription>
          </DialogHeader>
          {editingCustomer && (
            <SpecialCustomerForm
              customer={editingCustomer}
              onSuccess={() => {
                setEditingCustomer(null);
                fetchSpecialCustomers();
              }}
              onCancel={() => setEditingCustomer(null)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!deletingCustomerId}
        onOpenChange={(open) => !open && setDeletingCustomerId(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
            <AlertDialogDescription>
              هل أنت متأكد من رغبتك في حذف هذا العميل المميز؟ لا يمكن التراجع عن هذا الإجراء.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm}>
              حذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
