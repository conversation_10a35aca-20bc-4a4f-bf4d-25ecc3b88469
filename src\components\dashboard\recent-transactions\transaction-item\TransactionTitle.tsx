
import { useIsMobile } from "@/hooks/use-mobile";

interface TransactionTitleProps {
  transactionType: string;
  customerName: string;
  description: string;
  customerPhone?: string;
}

export function TransactionTitle({ transactionType, customerName, description, customerPhone }: TransactionTitleProps) {
  const isMobile = useIsMobile();
  const isReceive = transactionType === 'receive';
  const displayName = customerName || description || 'غير معروف';
  
  return (
    <div>
      <p className={`font-medium ${isMobile ? 'text-sm' : 'text-sm'}`}>
        {isReceive 
          ? `استلام من ${displayName}` 
          : `إرسال لـ ${displayName}`}
      </p>
      {customerPhone && (
        <p className="text-xs text-muted-foreground">{customerPhone}</p>
      )}
    </div>
  );
}
