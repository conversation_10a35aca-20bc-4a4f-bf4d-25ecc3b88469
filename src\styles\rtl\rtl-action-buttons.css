
/* تنسيقات خاصة بأزرار الإجراءات في النظام */
@layer utilities {
  /* تنسيق مجموعة أزرار الإجراءات */
  .rtl-actions-group {
    @apply flex gap-2 justify-start items-center my-1;
  }

  /* نسخة متجاوبة لأجهزة الموبايل */
  .rtl-actions-group-mobile {
    @apply flex flex-wrap gap-2 justify-center mt-3 w-full;
  }
  
  /* تنسيق زر الإجراء */
  .rtl-action-button {
    @apply inline-flex items-center justify-center gap-2 rounded-md border text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 shadow-sm min-w-[48px] active:scale-[0.98] h-9 px-3;
  }
  
  /* زر عادي */
  .rtl-action-button-default {
    @apply bg-primary/10 text-primary hover:bg-primary/20 border-primary/30;
  }
  
  /* زر حذف (أحمر) */
  .rtl-action-button-destructive {
    @apply text-destructive hover:text-destructive hover:bg-destructive/10 border-destructive/30 hover:border-destructive;
  }
  
  /* زر تعديل (برتقالي) */
  .rtl-action-button-edit {
    @apply text-amber-500 hover:text-amber-700 hover:bg-amber-50 border-amber-300/40 hover:border-amber-300;
  }
  
  /* زر عرض (أزرق) */
  .rtl-action-button-view {
    @apply text-blue-500 hover:text-blue-700 border-blue-300/40 hover:bg-blue-50/60 hover:border-blue-400;
  }
  
  /* زر نجاح (أخضر) */
  .rtl-action-button-success {
    @apply bg-green-600 text-white hover:bg-green-700 border-green-500;
  }
  
  /* تحسينات للتباعد بين الأزرار على الموبايل */
  .rtl-action-button-mobile {
    @apply h-8 px-2 text-xs;
  }
  
  /* زر الإجراء في الوضع الداكن */
  .dark .rtl-action-button {
    @apply border-muted bg-transparent transition-all;
  }
  
  .dark .rtl-action-button-default {
    @apply bg-primary/20 border-primary/40 hover:bg-primary/30 text-primary-foreground;
  }
  
  .dark .rtl-action-button-destructive {
    @apply border-destructive/40 hover:bg-destructive/20 text-destructive-foreground hover:text-destructive-foreground;
  }
  
  .dark .rtl-action-button-edit {
    @apply border-amber-500/40 hover:bg-amber-500/20 text-amber-400 hover:text-amber-300;
  }
  
  /* زر عرض (أزرق) في الوضع الداكن */
  .dark .rtl-action-button-view {
    @apply border-blue-500/30 hover:bg-blue-500/20 text-blue-400 hover:text-blue-300;
  }
  
  .dark .rtl-action-button-success {
    @apply bg-green-700 text-white hover:bg-green-600 border-green-600;
  }

  /* إضافة تأثير انتقال سلس للألوان عند تغيير الثيم */
  .theme-transition {
    @apply transition-all duration-200 ease-in-out;
  }
}
