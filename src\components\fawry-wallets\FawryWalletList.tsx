
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ModernTable } from "@/components/ui/modern-table/ModernTable";
import { Edit, Trash2, Eye } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import EditFawryWalletDialog from "./EditFawryWalletDialog";
import DeleteFawryWalletDialog from "./DeleteFawryWalletDialog";
import ViewFawryWalletOperationsDialog from "./ViewFawryWalletOperationsDialog";
import { Wallet } from "./utils/operationSchema";

interface FawryWalletListProps {
  refreshKey?: number;
}

const FawryWalletList: React.FC<FawryWalletListProps> = ({ refreshKey = 0 }) => {
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const isMobile = useIsMobile();
  
  // حالة الحوارات
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [viewOperationsDialogOpen, setViewOperationsDialogOpen] = useState(false);
  const [selectedWallet, setSelectedWallet] = useState<Wallet | null>(null);

  const fetchWallets = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("fawry_wallets")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      if (data) {
        // Transform the data to match our Wallet interface
        const transformedWallets: Wallet[] = data.map(wallet => ({
          id: wallet.id,
          name: wallet.name,
          phone_number: wallet.phone_number,
          balance: wallet.balance || 0
        }));
        setWallets(transformedWallets);
      }
    } catch (error) {
      console.error("Error fetching wallets:", error);
      toast({
        variant: "destructive",
        title: "خطأ في تحميل البيانات",
        description: "حدث خطأ أثناء تحميل المحافظ. الرجاء المحاولة مرة أخرى.",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWallets();
  }, [refreshKey]);

  const handleEditClick = (wallet: Wallet) => {
    setSelectedWallet(wallet);
    setEditDialogOpen(true);
  };

  const handleDeleteClick = (wallet: Wallet) => {
    setSelectedWallet(wallet);
    setDeleteDialogOpen(true);
  };
  
  const handleViewOperations = (wallet: Wallet) => {
    setSelectedWallet(wallet);
    setViewOperationsDialogOpen(true);
  };

  const columns = [
    {
      header: "اسم المحفظة",
      accessor: (row: Wallet) => row.name,
      mobile: true,
    },
    {
      header: "رقم الهاتف",
      accessor: (row: Wallet) => row.phone_number,
    },
    {
      header: "الرصيد",
      accessor: (row: Wallet) => `${row.balance?.toLocaleString('ar-EG') || "0"} جنيه`,
      mobile: true,
    },
    {
      header: "التفاصيل",
      accessor: (row: Wallet) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            handleViewOperations(row);
          }}
        >
          <Eye size={16} className="mr-1" /> عرض العمليات
        </Button>
      ),
      mobile: true,
    },
    {
      header: "تعديل",
      accessor: (row: Wallet) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            handleEditClick(row);
          }}
        >
          <Edit size={16} className="text-blue-500" />
        </Button>
      ),
    },
    {
      header: "حذف",
      accessor: (row: Wallet) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            handleDeleteClick(row);
          }}
        >
          <Trash2 size={16} className="text-red-500" />
        </Button>
      ),
    },
  ];

  return (
    <>
      <div>
        <h2 className="text-xl font-semibold mb-4">المحافظ المضافة</h2>
        <ModernTable
          data={wallets}
          columns={columns}
          loading={loading}
          emptyMessage="لا توجد محافظ مضافة بعد"
          keyField="id"
          dir="rtl"
        />
      </div>

      {/* حوار التعديل */}
      {selectedWallet && (
        <EditFawryWalletDialog
          isOpen={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          wallet={selectedWallet}
          onWalletUpdated={fetchWallets}
        />
      )}

      {/* حوار الحذف */}
      {selectedWallet && (
        <DeleteFawryWalletDialog
          isOpen={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          wallet={selectedWallet}
          onWalletDeleted={fetchWallets}
        />
      )}
      
      {/* حوار عرض العمليات */}
      {selectedWallet && (
        <ViewFawryWalletOperationsDialog
          isOpen={viewOperationsDialogOpen}
          onOpenChange={setViewOperationsDialogOpen}
          wallet={selectedWallet}
        />
      )}
    </>
  );
};

export default FawryWalletList;
