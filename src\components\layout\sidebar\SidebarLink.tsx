
import { cn } from "@/lib/utils";
import { useLocation } from "react-router-dom";
import { Link } from "react-router-dom";

interface SidebarLinkProps {
  to: string;
  icon: React.ElementType;
  label: string;
  isCollapsed: boolean;
  onClick?: () => void;
}

export const SidebarLink = ({ to, icon: Icon, label, isCollapsed, onClick }: SidebarLinkProps) => {
  const location = useLocation();
  const isActive = location.pathname === to;

  return (
    <Link
      to={to}
      onClick={onClick}
      className={cn(
        "flex items-center px-2 py-2 gap-2 rounded-md transition-all duration-200 sidebar-items",
        isActive
          ? "bg-primary text-primary-foreground"
          : "hover:bg-accent hover:text-accent-foreground"
      )}
    >
      <Icon size={20} />
      {!isCollapsed && <span className="animate-slide-in">{label}</span>}
    </Link>
  );
};
