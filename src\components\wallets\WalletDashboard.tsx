
import { Button } from "@/components/ui/button";
import { AddWalletDialog } from "./AddWalletDialog";
import WalletCard from "./WalletCard";
import DeleteWalletDialog from "./DeleteWalletDialog";
import { useWalletDashboard } from "./useWalletDashboard";
import { useIsMobile } from "@/hooks/use-mobile";
import { LifeBuoy } from "lucide-react";
import { useNavigate } from "react-router-dom";

const WalletDashboard = () => {
  const {
    wallets,
    loading,
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    handleDeleteClick,
    handleDeleteWallet,
    fetchWallets
  } = useWalletDashboard();
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  
  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">إدارة المحافظ</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/support')}
            className="hidden sm:flex items-center gap-1 hover:bg-accent/80 active:bg-accent transition-all duration-150"
          >
            <LifeBuoy className="h-4 w-4 ml-2" />
            الدعم والاشتراك
          </Button>
          <AddWalletDialog onWalletAdded={fetchWallets} />
        </div>
      </div>
      
      {isMobile && (
        <div className="mb-4">
          <Button
            variant="outline"
            size="mobile-sm"
            onClick={() => navigate('/support')}
            className="w-full items-center gap-1 hover:bg-accent/80 active:bg-accent transition-all duration-150"
          >
            <LifeBuoy className="h-4 w-4 ml-2" />
            الدعم والاشتراك
          </Button>
        </div>
      )}
      
      {loading ? (
        <div className="text-center py-8">جاري تحميل البيانات...</div>
      ) : (
        <div className={`grid gap-4 md:gap-5 lg:gap-6 ${isMobile ? 'grid-cols-1' : 'sm:grid-cols-2 lg:grid-cols-3'} wallet-grid`}>
          {wallets.map(wallet => (
            <div key={wallet.id} className="golden-border-card">
              <div className="golden-border-animation"></div>
              <WalletCard 
                id={wallet.id} 
                name={wallet.name} 
                balance={wallet.balance} 
                color={wallet.color_class || ''}
                onDelete={handleDeleteClick}
              />
            </div>
          ))}
        </div>
      )}
      
      <DeleteWalletDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onDelete={handleDeleteWallet}
      />
    </>
  );
};

export default WalletDashboard;
