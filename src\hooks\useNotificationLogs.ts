
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface NotificationLog {
  id: string;
  user_id: string;
  message: string;
  type: string;
  status: string;
  created_at: string;
}

export function useNotificationLogs() {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Fixed page size at 10 rows
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch notification logs with pagination
  const { data: logsData, isLoading, error } = useQuery({
    queryKey: ['notificationLogs', currentPage, pageSize],
    queryFn: async () => {
      const from = (currentPage - 1) * pageSize;
      const to = from + pageSize - 1;

      // First get total count
      const { count, error: countError } = await supabase
        .from("notification_logs")
        .select("*", { count: "exact", head: true });

      if (countError) {
        console.error("Error fetching count:", countError);
        throw new Error(countError.message);
      }

      // Then fetch paginated data
      const { data, error } = await supabase
        .from("notification_logs")
        .select("*")
        .order('created_at', { ascending: false })
        .range(from, to);
      
      if (error) {
        console.error("Error fetching notification logs:", error);
        throw new Error(error.message);
      }
      
      return {
        logs: data as NotificationLog[],
        totalCount: count || 0,
        totalPages: Math.ceil((count || 0) / pageSize)
      };
    }
  });

  // Clear logs mutation
  const { mutate: clearLogs, isPending: isDeleting } = useMutation({
    mutationFn: async () => {
      const { error } = await supabase
        .from("notification_logs")
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all logs
      
      if (error) throw new Error(error.message);
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notificationLogs'] });
      setShowDeleteDialog(false);
      toast({
        title: "تم مسح السجل",
        description: "تم مسح سجل الإشعارات بنجاح",
      });
    },
    onError: (error) => {
      console.error("Error clearing logs:", error);
      toast({
        title: "حدث خطأ",
        description: "فشل في مسح سجل الإشعارات",
        variant: "destructive",
      });
    }
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "sent":
        return "bg-green-500 hover:bg-green-600";
      case "failed":
        return "bg-red-500 hover:bg-red-600";
      case "pending":
        return "bg-yellow-500 hover:bg-yellow-600";
      default:
        return "bg-blue-500 hover:bg-blue-600";
    }
  };

  const handleClearLogs = () => {
    clearLogs();
  };

  const goToNextPage = () => {
    if (logsData && currentPage < logsData.totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  return {
    logs: logsData?.logs || [],
    totalCount: logsData?.totalCount || 0,
    totalPages: logsData?.totalPages || 0,
    currentPage,
    pageSize,
    isLoading,
    error,
    showDeleteDialog,
    setShowDeleteDialog,
    isDeleting,
    clearLogs: handleClearLogs,
    getStatusColor,
    goToNextPage,
    goToPreviousPage,
    hasNextPage: logsData ? currentPage < logsData.totalPages : false,
    hasPreviousPage: currentPage > 1
  };
}
