
import {
  BarChart3,
  CircleDollarSign,
  LayoutDashboard,
  Receipt,
  Settings,
  Users,
  Database,
  Bell,
  Wallet,
  UserCog,
  LifeBuoy
} from "lucide-react";

export const navigationItems = [
  {
    path: "/",
    label: "لوحة التحكم",
    icon: LayoutDashboard,
    permission: "dashboard"
  },
  {
    path: "/transactions",
    label: "العمليات",
    icon: CircleDollarSign,
    permission: "transactions"
  },
  {
    path: "/wallets",
    label: "المحافظ",
    icon: Wallet,
    permission: "wallets"
  },
  {
    path: "/reports",
    label: "التقارير",
    icon: BarChart3,
    permission: "reports"
  },
  {
    path: "/debts",
    label: "الديون",
    icon: Receipt,
    permission: "debts"
  },
  {
    path: "/customers",
    label: "العملاء",
    icon: Users,
    permission: "customers"
  },
  {
    path: "/users",
    label: "المستخدمين",
    icon: UserCog,
    permission: "users"
  },
  {
    path: "/settings",
    label: "الإعدادات",
    icon: Settings,
    permission: "settings"
  },
  {
    path: "/database-tools",
    label: "أدوات قاعدة البيانات",
    icon: Database,
    permission: "database_tools"
  },
  {
    path: "/notification-logs",
    label: "سجل الإشعارات",
    icon: Bell,
    permission: "notification_logs"
  },
  {
    path: "/support",
    label: "الدعم والاشتراك",
    icon: LifeBuoy,
    permission: null // This allows all users to see it in the sidebar
  }
];
