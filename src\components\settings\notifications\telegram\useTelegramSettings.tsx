
import { useState, useEffect } from "react";
import { fetchTelegramSettings } from "./api/fetchTelegramSettings";
import { saveTelegramSettings } from "./api/saveTelegramSettings";
import { sendTestMessage } from "./api/sendTestMessage";
import { createEdgeFunction } from "./api/edgeFunctionUtils";
import { TelegramState } from "./types";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

export function useTelegramSettings() {
  const [state, setState] = useState<TelegramState>({
    isEnabled: false,
    botToken: "",
    chatId: "",
    userId: "",
    isSaving: false,
    isTesting: false,
    isCreatingFunction: false
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentRole, setCurrentRole] = useState<string | null>(null);
  const [roleLoading, setRoleLoading] = useState<boolean>(true);

  // Getter functions for cleaner code
  const isEnabled = state.isEnabled;
  const botToken = state.botToken;
  const chatId = state.chatId;
  const userId = state.userId;
  const isSaving = state.isSaving;
  const isTesting = state.isTesting;
  const isCreatingFunction = state.isCreatingFunction;

  // Setter functions
  const setIsEnabled = (value: boolean) => setState(prev => ({ ...prev, isEnabled: value }));
  const setBotToken = (value: string) => setState(prev => ({ ...prev, botToken: value }));
  const setChatId = (value: string) => setState(prev => ({ ...prev, chatId: value }));
  const setUserId = (value: string) => setState(prev => ({ ...prev, userId: value }));

  // Fetch user role
  useEffect(() => {
    async function getUserRole() {
      setRoleLoading(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          const { data: roleData, error: roleError } = await supabase
            .from("user_roles")
            .select("role")
            .eq("user_id", user.id)
            .single();
            
          if (roleError) throw roleError;
          setCurrentRole(roleData?.role || null);
        }
      } catch (error) {
        console.error("Error fetching user role:", error);
        setError("فشل في التحقق من صلاحيات المستخدم");
      } finally {
        setRoleLoading(false);
      }
    }
    
    getUserRole();
  }, []);

  // Fetch telegram settings on component mount
  useEffect(() => {
    loadTelegramSettings();
  }, []);

  const loadTelegramSettings = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const settings = await fetchTelegramSettings();
      if (settings) {
        setState(prev => ({ 
          ...prev, 
          isEnabled: settings.isEnabled,
          botToken: settings.botToken,
          chatId: settings.chatId,
          userId: settings.userId
        }));
      }
    } catch (err) {
      console.error("Error fetching Telegram settings:", err);
      setError("فشل في تحميل إعدادات تليجرام");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveTelegramSettings = async () => {
    setState(prev => ({ ...prev, isSaving: true }));
    
    try {
      await saveTelegramSettings({
        isEnabled: state.isEnabled,
        botToken: state.botToken,
        chatId: state.chatId,
        userId: state.userId
      });
      toast.success("تم حفظ إعدادات تليجرام بنجاح");
    } catch (err) {
      console.error("Error saving Telegram settings:", err);
      toast.error("فشل في حفظ إعدادات تليجرام");
    } finally {
      setState(prev => ({ ...prev, isSaving: false }));
    }
  };

  const handleTestMessage = async () => {
    setState(prev => ({ ...prev, isTesting: true }));
    
    try {
      // التحقق من وجود معرف اختباري أو معرف دردشة
      const targetId = state.userId || state.chatId;
      if (!state.botToken) {
        throw new Error("يجب إدخال رمز البوت أولاً");
      }
      if (!targetId) {
        throw new Error("يجب إدخال معرف المستخدم أو معرف الدردشة أولاً");
      }
      await sendTestMessage(state.botToken, targetId);
      toast.success("تم إرسال الرسالة التجريبية بنجاح");
    } catch (err) {
      console.error("Error sending test message:", err);
      toast.error(err.message || "فشل في إرسال الرسالة التجريبية");
    } finally {
      setState(prev => ({ ...prev, isTesting: false }));
    }
  };

  const handleCreateEdgeFunction = async () => {
    setState(prev => ({ ...prev, isCreatingFunction: true }));
    
    try {
      await createEdgeFunction();
      toast.success("تم إنشاء Edge Function بنجاح");
    } catch (err) {
      console.error("Error creating Edge Function:", err);
      toast.error("فشل في إنشاء Edge Function");
    } finally {
      setState(prev => ({ ...prev, isCreatingFunction: false }));
    }
  };

  return {
    isEnabled,
    setIsEnabled,
    botToken,
    setBotToken,
    chatId,
    setChatId,
    userId,
    setUserId,
    isSaving,
    isTesting,
    isCreatingFunction,
    isLoading,
    error,
    currentRole,
    roleLoading,
    handleSaveTelegramSettings,
    handleTestMessage,
    handleCreateEdgeFunction,
    reloadSettings: loadTelegramSettings
  };
}
