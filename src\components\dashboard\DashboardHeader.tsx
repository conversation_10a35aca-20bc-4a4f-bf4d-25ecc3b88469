
import { RefreshCcw, Filter, Calendar, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { useIsMobile } from "@/hooks/use-mobile";

interface DashboardHeaderProps {
  timeframe: string;
  setTimeframe: (timeframe: string) => void;
  handleRefresh: () => void;
}

export function DashboardHeader({
  timeframe,
  setTimeframe,
  handleRefresh
}: DashboardHeaderProps) {
  const today = new Date();
  const isMobile = useIsMobile();
  
  const formatTimeframeLabel = () => {
    switch (timeframe) {
      case "today":
        return `اليوم ${format(today, "dd/MM/yyyy", {
          locale: ar
        })}`;
      case "yesterday":
        {
          const yesterday = new Date(today);
          yesterday.setDate(yesterday.getDate() - 1);
          return `أمس ${format(yesterday, "dd/MM/yyyy", {
            locale: ar
          })}`;
        }
      case "week":
        return "هذا الأسبوع";
      case "month":
        return "هذا الشهر";
      case "all":
        return "جميع البيانات";
      default:
        return "اختر الفترة";
    }
  };
  
  return (
    <>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 md:mb-6 font-bold">
        <h1 className={`text-2xl md:text-3xl font-bold mb-2 md:mb-0 ${isMobile ? 'rtl-mobile-heading' : ''}`}>لوحة التحكم</h1>
        <div className="text-sm text-muted-foreground">
          مراجعة البيانات المالية والمعاملات الأخيرة
        </div>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-2">
        <div className="flex items-center gap-2 w-full sm:w-auto mb-2 sm:mb-0">
          <Button 
            variant="outline" 
            size={isMobile ? "sm" : "default"} 
            onClick={handleRefresh} 
            className="flex items-center gap-1 hover:bg-primary/10 transition-colors
                     bg-[#EF4343] hover:bg-[#EF4343]/90 text-white font-medium
                     dark:bg-[#D0AC19] dark:hover:bg-[#D0AC19]/90 dark:text-black dark:font-bold
                     rtl-mobile-button"
          >
            <RefreshCcw className="h-4 w-4" />
            <span className={isMobile ? "text-sm" : ""}>تحديث</span>
          </Button>
        </div>
        
        <Popover>
          <PopoverTrigger asChild>
            <Button 
              variant="outline" 
              size={isMobile ? "sm" : "default"} 
              className="w-full sm:w-auto flex items-center gap-1 border-primary/20 hover:border-primary/50 transition-colors rtl-mobile-button"
            >
              <Calendar className="h-4 w-4 text-primary" />
              <span className="mx-1">{formatTimeframeLabel()}</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[200px] p-2" align="end">
            <div className="space-y-1">
              {[{
              value: "today",
              label: "اليوم",
              icon: <Clock className="h-4 w-4" />
            }, {
              value: "yesterday",
              label: "أمس",
              icon: <Clock className="h-4 w-4" />
            }, {
              value: "week",
              label: "هذا الأسبوع",
              icon: <Calendar className="h-4 w-4" />
            }, {
              value: "month",
              label: "هذا الشهر",
              icon: <Calendar className="h-4 w-4" />
            }, {
              value: "all",
              label: "جميع البيانات",
              icon: <Calendar className="h-4 w-4" />
            }].map(option => (
              <Button 
                key={option.value} 
                variant={timeframe === option.value ? "default" : "ghost"} 
                size="sm" 
                className={cn(
                  "w-full justify-start text-right", 
                  timeframe === option.value ? "bg-primary text-primary-foreground" : ""
                )} 
                onClick={() => setTimeframe(option.value)}
              >
                <span className="ml-2">{option.icon}</span>
                {option.label}
              </Button>
            ))}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </>
  );
}
