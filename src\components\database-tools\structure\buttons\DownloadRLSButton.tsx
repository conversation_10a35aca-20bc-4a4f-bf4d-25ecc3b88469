
import { Button } from "@/components/ui/button";
import { Shield } from "lucide-react";
import { toast } from "sonner";
import { downloadDatabaseStructure } from "@/utils/database/downloadUtils";

export default function DownloadRLSButton() {
  const handleDownloadRLSAndRealtime = async () => {
    try {
      const rlsContent = `-- سكريپت لإعداد سياسات أمان الصفوف (RLS) وتفعيل ميزة Realtime
-- Generated: ${new Date().toISOString()}

-- تفعيل RLS على جميع الجداول الرئيسية
ALTER TABLE public.wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sims ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fawry_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fawry_wallet_operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commission_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wallet_commissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.telegram_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_settings ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات للمطورين (يمكنهم الوصول إلى كل شيء)
-- wallets
CREATE POLICY "المطورون يمكنهم قراءة المحافظ" ON public.wallets
    FOR SELECT USING (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم إضافة المحافظ" ON public.wallets
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم تعديل المحافظ" ON public.wallets
    FOR UPDATE USING (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم حذف المحافظ" ON public.wallets
    FOR DELETE USING (
        has_role(auth.uid(), 'developer')
    );

-- sims
CREATE POLICY "المطورون يمكنهم قراءة الشرائح" ON public.sims
    FOR SELECT USING (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم إضافة الشرائح" ON public.sims
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم تعديل الشرائح" ON public.sims
    FOR UPDATE USING (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم حذف الشرائح" ON public.sims
    FOR DELETE USING (
        has_role(auth.uid(), 'developer')
    );

-- transactions
CREATE POLICY "المطورون يمكنهم قراءة المعاملات" ON public.transactions
    FOR SELECT USING (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم إضافة المعاملات" ON public.transactions
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم تعديل المعاملات" ON public.transactions
    FOR UPDATE USING (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم حذف المعاملات" ON public.transactions
    FOR DELETE USING (
        has_role(auth.uid(), 'developer')
    );

-- customers
CREATE POLICY "المطورون يمكنهم قراءة العملاء" ON public.customers
    FOR SELECT USING (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم إضافة العملاء" ON public.customers
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم تعديل العملاء" ON public.customers
    FOR UPDATE USING (
        has_role(auth.uid(), 'developer')
    );

CREATE POLICY "المطورون يمكنهم حذف العملاء" ON public.customers
    FOR DELETE USING (
        has_role(auth.uid(), 'developer')
    );

-- إنشاء سياسات للمسؤولين
-- wallets
CREATE POLICY "المسؤولون يمكنهم قراءة المحافظ" ON public.wallets
    FOR SELECT USING (
        has_role(auth.uid(), 'admin')
    );

CREATE POLICY "المسؤولون يمكنهم إضافة المحافظ" ON public.wallets
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'admin')
    );

CREATE POLICY "المسؤولون يمكنهم تعديل المحافظ" ON public.wallets
    FOR UPDATE USING (
        has_role(auth.uid(), 'admin')
    );

-- sims
CREATE POLICY "المسؤولون يمكنهم قراءة الشرائح" ON public.sims
    FOR SELECT USING (
        has_role(auth.uid(), 'admin')
    );

CREATE POLICY "المسؤولون يمكنهم إضافة الشرائح" ON public.sims
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'admin')
    );

CREATE POLICY "المسؤولون يمكنهم تعديل الشرائح" ON public.sims
    FOR UPDATE USING (
        has_role(auth.uid(), 'admin')
    );

-- transactions
CREATE POLICY "المسؤولون يمكنهم قراءة المعاملات" ON public.transactions
    FOR SELECT USING (
        has_role(auth.uid(), 'admin')
    );

CREATE POLICY "المسؤولون يمكنهم إضافة المعاملات" ON public.transactions
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'admin')
    );

-- customers
CREATE POLICY "المسؤولون يمكنهم قراءة العملاء" ON public.customers
    FOR SELECT USING (
        has_role(auth.uid(), 'admin')
    );

CREATE POLICY "المسؤولون يمكنهم إضافة العملاء" ON public.customers
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'admin')
    );

CREATE POLICY "المسؤولون يمكنهم تعديل العملاء" ON public.customers
    FOR UPDATE USING (
        has_role(auth.uid(), 'admin')
    );

-- إنشاء سياسات للموظفين
-- wallets
CREATE POLICY "الموظفون يمكنهم قراءة المحافظ" ON public.wallets
    FOR SELECT USING (
        has_role(auth.uid(), 'employee') AND 
        has_permission(auth.uid(), 'wallets')
    );

-- sims
CREATE POLICY "الموظفون يمكنهم قراءة الشرائح" ON public.sims
    FOR SELECT USING (
        has_role(auth.uid(), 'employee') AND 
        has_permission(auth.uid(), 'wallets')
    );

-- transactions
CREATE POLICY "الموظفون يمكنهم قراءة المعاملات" ON public.transactions
    FOR SELECT USING (
        has_role(auth.uid(), 'employee') AND 
        has_permission(auth.uid(), 'transactions')
    );

CREATE POLICY "الموظفون يمكنهم إضافة المعاملات" ON public.transactions
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'employee') AND 
        has_permission(auth.uid(), 'transactions')
    );

-- customers
CREATE POLICY "الموظفون يمكنهم قراءة العملاء" ON public.customers
    FOR SELECT USING (
        has_role(auth.uid(), 'employee') AND 
        has_permission(auth.uid(), 'customers')
    );

CREATE POLICY "الموظفون يمكنهم إضافة العملاء" ON public.customers
    FOR INSERT WITH CHECK (
        has_role(auth.uid(), 'employee') AND 
        has_permission(auth.uid(), 'customers')
    );

-- تفعيل ميزة Realtime لجميع الجداول
ALTER PUBLICATION supabase_realtime ADD TABLE public.wallets;
ALTER PUBLICATION supabase_realtime ADD TABLE public.sims;
ALTER PUBLICATION supabase_realtime ADD TABLE public.transactions;
ALTER PUBLICATION supabase_realtime ADD TABLE public.customers;
ALTER PUBLICATION supabase_realtime ADD TABLE public.debts;
ALTER PUBLICATION supabase_realtime ADD TABLE public.employees;
ALTER PUBLICATION supabase_realtime ADD TABLE public.departments;
ALTER PUBLICATION supabase_realtime ADD TABLE public.branches;
ALTER PUBLICATION supabase_realtime ADD TABLE public.fawry_wallets;
ALTER PUBLICATION supabase_realtime ADD TABLE public.fawry_wallet_operations;
ALTER PUBLICATION supabase_realtime ADD TABLE public.commission_settings;
ALTER PUBLICATION supabase_realtime ADD TABLE public.wallet_commissions;
ALTER PUBLICATION supabase_realtime ADD TABLE public.notification_logs;
ALTER PUBLICATION supabase_realtime ADD TABLE public.notification_rules;
ALTER PUBLICATION supabase_realtime ADD TABLE public.telegram_settings;
ALTER PUBLICATION supabase_realtime ADD TABLE public.security_settings;

-- تعليقات إضافية
-- هذا الملف يحتوي على جميع السياسات اللازمة لتأمين قاعدة البيانات
-- تأكد من وجود الوظائف has_role و has_permission قبل تنفيذ هذا السكريپت
-- يُنصح بمراجعة السياسات وتخصيصها حسب احتياجات التطبيق

-- End of RLS and Realtime Setup Script
-- Generated: ${new Date().toISOString()}
`;

      // Create filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const fileName = `rls_realtime_setup_${timestamp}.sql`;
      
      // Download the RLS and Realtime setup file
      downloadDatabaseStructure(rlsContent, fileName);
      
      toast.success("تم تحميل ملف إعداد سياسات أمان الصفوف و Realtime بنجاح");
    } catch (error) {
      console.error("خطأ في تحميل ملف إعداد RLS و Realtime:", error);
      toast.error("حدث خطأ أثناء تحميل ملف إعداد RLS و Realtime");
    }
  };

  return (
    <Button 
      onClick={handleDownloadRLSAndRealtime} 
      className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 w-full"
      size="lg"
    >
      <Shield className="h-5 w-5" />
      إعداد سياسات أمان الصفوف وتفعيل Realtime
    </Button>
  );
}
