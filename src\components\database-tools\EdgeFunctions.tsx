
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { FileCode, Shield } from "lucide-react";
import { edgeFunctions } from "./edge-functions/types";
import { FunctionItem } from "./edge-functions/FunctionItem";
import { DownloadAllFunctions } from "./edge-functions/DownloadAllFunctions";
import { SaveSecurityAlertFunction } from "./edge-functions/SaveSecurityAlertFunction";
import { SaveAllFunctions } from "./edge-functions/SaveAllFunctions";
import { useIsMobile } from "@/hooks/use-mobile";

export default function EdgeFunctions() {
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});
  const [isDownloadingAll, setIsDownloadingAll] = useState(false);
  const isMobile = useIsMobile();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileCode className="h-5 w-5 text-primary" />
            وظائف Edge Functions
          </div>
          <div className="flex items-center gap-2 flex-wrap justify-end">
            {isMobile ? (
              <>
                <DownloadAllFunctions 
                  isDownloadingAll={isDownloadingAll} 
                  setIsDownloadingAll={setIsDownloadingAll} 
                />
                <SaveSecurityAlertFunction />
                <SaveAllFunctions />
              </>
            ) : (
              <>
                <SaveAllFunctions />
                <SaveSecurityAlertFunction />
                <DownloadAllFunctions 
                  isDownloadingAll={isDownloadingAll} 
                  setIsDownloadingAll={setIsDownloadingAll} 
                />
              </>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            إدارة وتنزيل وتحديث وظائف Edge Functions الخاصة بالنظام
          </p>

          <div className="bg-amber-50 dark:bg-amber-900/10 p-4 rounded-md border border-amber-200 dark:border-amber-800 mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-amber-600" />
              <span className="font-medium text-amber-800 dark:text-amber-300">ملاحظة هامة</span>
            </div>
            <p className="text-sm text-amber-700 dark:text-amber-300">
              إذا واجهت مشاكل في تحميل وظائف Edge Functions مثل تحميل محتوى فارغ أو أكواد قالب، يمكنك استخدام زر "حفظ جميع الوظائف في قاعدة البيانات" لحفظ محتوى الوظائف مباشرة في قاعدة البيانات.
            </p>
          </div>

          <div className="grid gap-4">
            {edgeFunctions.map((func) => (
              <FunctionItem 
                key={func.name} 
                func={func} 
                isLoading={isLoading} 
                setIsLoading={setIsLoading} 
              />
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
