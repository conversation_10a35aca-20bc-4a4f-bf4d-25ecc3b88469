
/* تأثيرات حدود ذهبية للبطاقات */
.golden-border-card {
  position: relative;
  border-radius: 0.75rem;
  overflow: visible;
  transform: translateZ(0);
  margin-bottom: 1rem;
}

.golden-border-animation {
  position: absolute;
  inset: -2px;
  border-radius: 0.75rem;
  padding: 2px;
  background: linear-gradient(
    -45deg, 
    rgba(230, 185, 117, 0), 
    rgba(230, 185, 117, 0.3), 
    rgba(255, 215, 0, 0.6), 
    rgba(230, 185, 117, 0.3), 
    rgba(230, 185, 117, 0)
  );
  background-size: 400% 400%;
  filter: blur(4px);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
  animation: none;
}

/* تأثير توهج الحدود عند تحويم المؤشر */
.golden-border-card:hover .golden-border-animation {
  opacity: 1;
  animation: animateBorder 3s ease infinite;
}

@keyframes animateBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* تعديلات للوضع المظلم - تأثير ذهبي أكثر توهجًا */
.dark .golden-border-animation {
  background: linear-gradient(
    -45deg, 
    rgba(255, 215, 0, 0), 
    rgba(255, 215, 0, 0.15), 
    rgba(255, 215, 0, 0.3), 
    rgba(255, 215, 0, 0.15), 
    rgba(255, 215, 0, 0)
  );
  filter: blur(3px);
}

/* تعديلات للوضع النهاري - تأثير أحمر متوهج */
.golden-border-card:hover {
  box-shadow: 0 0 15px 1px rgba(239, 67, 67, 0.25);
}

.dark .golden-border-card:hover {
  box-shadow: 0 0 20px 2px rgba(255, 215, 0, 0.25);
}

/* لتوهج أزرار التحرير داخل البطاقات */
.edit-button-glow {
  position: relative;
  overflow: hidden;
}

.edit-button-glow::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 215, 0, 0.4) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s;
}

.edit-button-glow:hover::after {
  opacity: 1;
}

/* تأثيرات حدود متحركة للبطاقات */
.animated-border {
  position: relative;
  border-radius: 0.75rem;
}

.animated-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.75rem;
  border: 2px solid transparent;
  background: linear-gradient(45deg, gold, transparent, gold, transparent) border-box;
  -webkit-mask: 
    linear-gradient(#fff 0 0) padding-box, 
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  animation: borderRotate 4s linear infinite;
  pointer-events: none;
}

@keyframes borderRotate {
  0% { background-position: 0% 0%; }
  100% { background-position: 130% 0%; }
}

/* تحسين تأثير الحركة للبطاقات */
.wallet-card {
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: transform, box-shadow;
}

/* تأثير النبض */
.pulse-effect {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* زيادة وضوح تأثير التوهج في الوضع النهاري */
@media (prefers-color-scheme: light) {
  .golden-border-card:hover {
    box-shadow: 0 0 15px 3px rgba(239, 67, 67, 0.4);
  }
}

/* زيادة وضوح تأثير التوهج الذهبي في الوضع المظلم */
.dark .golden-border-card:hover {
  box-shadow: 0 0 25px 5px rgba(255, 215, 0, 0.3);
}

