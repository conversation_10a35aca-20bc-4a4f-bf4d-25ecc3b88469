
import * as z from "zod";

// مخطط التحقق من بيانات المحفظة
export const walletFormSchema = z.object({
  name: z.string().min(2, { message: "يجب أن يحتوي اسم المحفظة على حرفين على الأقل" }),
  number: z.string().min(1, { message: "يجب إدخال رقم المحفظة" }),
  type: z.string().min(1, { message: "يجب اختيار نوع المحفظة" }),
});

export type WalletFormValues = z.infer<typeof walletFormSchema>;

// تعريف أنواع المحافظ المتاحة
export const walletTypes = [
  { id: "vodafone_cash", name: "فودافون كاش" },
  { id: "etisalat_cash", name: "اتصالات كاش" },
  { id: "orange_cash", name: "أورانج كاش" },
  { id: "instapay", name: "انستاباي" },
  { id: "fawry", name: "محفظة فوري" },
  { id: "pension_card", name: "بطاقة معاشات" },
  { id: "other", name: "أخرى" }
];
