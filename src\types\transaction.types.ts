
export interface Transaction {
  id: string;
  amount: number;
  transaction_type: string;
  created_at: string;
  description: string | null;
  commission: number;
  wallet: {
    name: string;
  } | null;
  customer?: {
    name: string;
    phone?: string;
  } | null;
  customer_name?: string;
  customer_phone?: string;
}

export interface TransactionStats {
  totalCount: number;
  sentAmount: number;
  receivedAmount: number;
  commissionAmount: number;
}

// Define wallet colors mapping
export const WALLET_COLORS: Record<string, string> = {
  default: "bg-gray-400",
  vodafone: "bg-red-500",
  orange: "bg-orange-500",
  etisalat: "bg-green-500",
  we: "bg-purple-500",
  bank: "bg-blue-500"
};

// Define wallet names mapping
export const WALLET_NAMES: Record<string, string> = {
  vodafone: "فودافون كاش",
  orange: "أورانج كاش",
  etisalat: "اتصالات كاش",
  we: "وي",
  bank: "بنك",
  default: "محفظة"
};

// Define transaction status colors
export const STATUS_COLORS: Record<string, string> = {
  completed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  failed: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
};

// Define transaction status names in Arabic
export const STATUS_NAMES: Record<string, string> = {
  completed: "مكتملة",
  pending: "قيد الانتظار",
  failed: "فشلت"
};

// Add these interfaces to the existing file
export interface ProcessedTransaction {
  id: string;
  amount: number;
  transaction_type: string;
  created_at: string;
  description: string | null;
  commission: number;
  walletName?: string;
  customerName?: string;
  customer_name?: string;
  customer_phone?: string; // Add customer phone field
}
