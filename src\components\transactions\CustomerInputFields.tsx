
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";

interface CustomerInputFieldsProps {
  form: UseFormReturn<any>;
  isPhoneRequired?: boolean;
}

export function CustomerInputFields({ form, isPhoneRequired = false }: CustomerInputFieldsProps) {
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="customerName"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              اسم العميل <span className="text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <Input 
                placeholder="أدخل اسم العميل" 
                {...field}
                className={!field.value ? "border-red-500" : ""}
                required
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="customerPhone"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              رقم هاتف العميل <span className="text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <Input 
                placeholder="أدخل رقم هاتف العميل" 
                type="tel" 
                {...field}
                className={`text-right ${!field.value ? "border-red-500" : ""}`}
                required
                dir="rtl"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
