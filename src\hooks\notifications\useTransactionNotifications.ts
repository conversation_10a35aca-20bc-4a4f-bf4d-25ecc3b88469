
import { useNotificationSender } from './useNotificationSender';
import { useBalance<PERSON>hecker } from './useBalanceChecker';

/**
 * Hook for transaction notifications functionality
 */
export function useTransactionNotifications() {
  const { sendTransactionAlert } = useNotificationSender();
  const { checkSimBalance } = useBalanceChecker();
  
  return {
    sendTransactionAlert,
    checkSimBalance
  };
}

// Also export the individual hooks for direct use
export { useNotificationSender, useBalanceChecker };
