
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";
import { Badge } from "@/components/ui/badge";
import { Loader2, Bell } from "lucide-react";
import { formatDate } from "@/utils/formatters";

interface NotificationLog {
  id: string;
  user_id: string;
  message: string;
  type: string;
  status: string;
  created_at: string;
}

interface NotificationTableProps {
  logs: NotificationLog[] | undefined;
  isLoading: boolean;
  getStatusColor: (status: string) => string;
}

export function NotificationTable({ logs, isLoading, getStatusColor }: NotificationTableProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!logs?.length) {
    return (
      <div className="text-center py-8 text-muted-foreground bg-green-50/30 dark:bg-green-900/10 rounded-lg border border-green-100/50 dark:border-green-800/30">
        <Bell className="h-10 w-10 text-muted-foreground/40 mx-auto mb-2" />
        لا توجد إشعارات مسجلة
      </div>
    );
  }

  const columns: TableColumn<NotificationLog>[] = [
    {
      header: "معرف المستخدم",
      accessor: "user_id",
      className: "font-medium"
    },
    {
      header: "الرسالة",
      accessor: "message",
      className: "max-w-[300px] truncate"
    },
    {
      header: "النوع",
      accessor: (log) => (
        <Badge variant="secondary">
          {log.type === "telegram" ? "تليجرام" : log.type}
        </Badge>
      )
    },
    {
      header: "الحالة",
      accessor: (log) => (
        <Badge
          className={getStatusColor(log.status)}
          variant="default"
        >
          {log.status === "sent" && "تم الإرسال"}
          {log.status === "failed" && "فشل"}
          {log.status === "pending" && "قيد الإنتظار"}
        </Badge>
      )
    },
    {
      header: "التاريخ",
      accessor: (log) => formatDate(log.created_at)
    }
  ];

  return (
    <ModernTable
      data={logs}
      columns={columns}
      loading={false}
      keyField="id"
      emptyMessage="لا توجد إشعارات مسجلة"
      dir="rtl"
      mobileCardTitle={(log) => log.message}
      mobileCardActions={(log) => (
        <Badge
          className={getStatusColor(log.status)}
          variant="default"
        >
          {log.status === "sent" && "تم الإرسال"}
          {log.status === "failed" && "فشل"}
          {log.status === "pending" && "قيد الإنتظار"}
        </Badge>
      )}
    />
  );
}
