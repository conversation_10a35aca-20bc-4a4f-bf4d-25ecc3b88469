
@layer utilities {
  /* تنسيقات خاصة بالبطاقات في الوضع المحمول */
  [dir="rtl"] .table-card-row {
    flex-direction: row-reverse !important;
    justify-content: space-between !important;
  }
  
  [dir="rtl"] .table-card-label {
    margin-right: 12px !important;
  }
  
  /* تحسينات للجدول في الوضع المحمول */
  @media (max-width: 768px) {
    [dir="rtl"] .mobile-card {
      text-align: right !important;
      direction: rtl !important;
      border-radius: 0.75rem !important;
      margin-bottom: 0.75rem !important;
    }
    
    /* فئة حدود جديدة تدعم الوضعين النهاري والليلي */
    .custom-border-card {
      border: 3px solid #ea384c !important;
      box-shadow: 0 2px 10px rgba(234, 56, 76, 0.2) !important;
      transition: all 0.3s ease !important;
    }
    
    .dark .custom-border-card {
      border: 3px solid #d4af37 !important; /* الذهبي */
      box-shadow: 0 2px 10px rgba(212, 175, 55, 0.2) !important;
    }
    
    [dir="rtl"] .mobile-card-header {
      padding: 0.75rem 1rem !important;
      border-bottom: 1px solid var(--border-color, #e2e8f0) !important;
      font-weight: 600 !important;
    }
    
    [dir="rtl"] .mobile-card-body {
      padding: 1rem !important;
    }
    
    [dir="rtl"] .mobile-card-row {
      display: flex !important;
      justify-content: space-between !important;
      padding: 0.5rem 0 !important;
      border-bottom: 1px solid var(--border-color, #f1f5f9) !important;
    }
    
    [dir="rtl"] .mobile-card-row:last-child {
      border-bottom: none !important;
    }
    
    [dir="rtl"] .mobile-card-label {
      color: var(--muted-foreground, #64748b) !important;
      font-size: 0.875rem !important;
    }
    
    [dir="rtl"] .mobile-card-value {
      font-weight: 500 !important;
      text-align: left !important;
    }
    
    /* حاوية جديدة للبطاقات المحمولة */
    .mobile-card-container {
      overflow: hidden !important; 
      border-radius: 0.75rem !important;
      margin-bottom: 1rem !important;
      transition: all 0.3s ease !important;
    }
    
    /* تجنب استخدام الحدود المضمنة وترك الأمر للبطاقة الداخلية */
    .mobile-card-container .mobile-card {
      margin-bottom: 0 !important;
    }
  }
}
