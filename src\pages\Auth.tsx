
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ThemeSwitcher } from "@/components/layout/ThemeSwitcher";
import { useAuth } from "@/contexts/AuthContext";
import AuthIllustration from "@/components/auth/AuthIllustration";
import { LifeBuoy } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { sendDeveloperLoginAlert } from "@/hooks/useAuthWithSecurity";

// Define form schema with validation
const formSchema = z.object({
  email: z.string().email({ message: "يرجى إدخال بريد إلكتروني صحيح" }),
  password: z.string().min(6, { message: "كلمة المرور يجب أن تكون 6 أحرف على الأقل" }),
});

const Auth = () => {
  const { signIn } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with validation
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Check if user is already authenticated
  useEffect(() => {
    const checkAuth = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session) {
        navigate("/");
      }
    };
    checkAuth();
  }, [navigate]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      await signIn(values.email, values.password);
      
      // Get user data to check if developer
      const { data } = await supabase.auth.getUser();
      if (data.user) {
        const { data: roleData } = await supabase
          .from("user_roles")
          .select("role")
          .eq("user_id", data.user.id)
          .single();
        
        // If user is developer, send security alert
        if (roleData?.role === "developer") {
          await sendDeveloperLoginAlert(data.user.id);
        }
      }
      
      navigate("/");
    } catch (error) {
      console.error("Login error:", error);
      // Send failed login attempt alert if it was a developer account
      try {
        const { data: userData } = await supabase
          .from("user_roles")
          .select("user_id, role")
          .eq("role", "developer")
          .limit(1)
          .single();
        
        if (userData) {
          await sendDeveloperLoginAlert(userData.user_id, false);
        }
      } catch (alertError) {
        console.error("Error sending failed login alert:", alertError);
      }
      
      toast.error("فشل تسجيل الدخول، يرجى التحقق من البيانات المدخلة");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="absolute top-4 left-4 flex gap-2">
        <Button 
          variant="outline" 
          size="icon-sm"
          onClick={() => navigate("/support")} 
          title="الدعم والاشتراك"
        >
          <LifeBuoy className="h-4 w-4" />
        </Button>
        <ThemeSwitcher />
      </div>
      
      <div className="bg-card border rounded-xl shadow-lg overflow-hidden max-w-4xl w-full grid grid-cols-1 md:grid-cols-2">
        <div className="p-8 flex flex-col justify-center">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-2">تسجيل الدخول</h1>
            <p className="text-muted-foreground">قم بتسجيل الدخول للوصول إلى لوحة التحكم</p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>البريد الإلكتروني</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="أدخل البريد الإلكتروني" 
                        {...field} 
                        disabled={isLoading}
                        type="email"
                        autoComplete="email"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>كلمة المرور</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="أدخل كلمة المرور" 
                        {...field} 
                        disabled={isLoading}
                        type="password" 
                        autoComplete="current-password"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading}
              >
                {isLoading ? "جاري تسجيل الدخول..." : "تسجيل الدخول"}
              </Button>
            </form>
          </Form>

          <div className="mt-4 text-center flex flex-col space-y-2">
            <a href="#" className="text-sm text-primary hover:underline">
              يتم إنشاء الحسابات بواسطة مسؤول النظام فقط
            </a>
            
            <Button 
              variant="link" 
              size="sm" 
              onClick={() => navigate("/support")}
              className="mx-auto"
            >
              <LifeBuoy className="ml-1 h-4 w-4" />
              الدعم والاشتراك
            </Button>
          </div>
        </div>

        <div className="hidden md:flex bg-muted p-8 items-center justify-center">
          <AuthIllustration />
        </div>
      </div>
    </div>
  );
};

export default Auth;
