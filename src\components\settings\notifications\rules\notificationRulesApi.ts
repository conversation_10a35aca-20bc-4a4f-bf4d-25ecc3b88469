
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { NotificationRuleFormValues } from "./types";

export const fetchNotificationRules = async (): Promise<NotificationRuleFormValues | null> => {
  try {
    const { data, error } = await supabase
      .from("notification_rules")
      .select("*")
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // "No rows found" error - this is expected for new installations
        console.log("No notification rules found, will use defaults");
        return {
          balanceAlertEnabled: false,
          minBalance: "50",
          transactionAlertEnabled: false,
          transactionTypes: [],
          transactionAmountOption: "all",
          minTransactionAmount: "1000",
        };
      }
      // Any other error is unexpected
      console.error("Error fetching notification rules:", error);
      throw error;
    }

    if (data) {
      return {
        balanceAlertEnabled: data.balance_alert_enabled || false,
        minBalance: data.min_balance?.toString() || "50",
        transactionAlertEnabled: data.transaction_alert_enabled || false,
        transactionTypes: data.transaction_types || [],
        transactionAmountOption: data.min_transaction_amount > 0 ? "threshold" : "all",
        minTransactionAmount: data.min_transaction_amount?.toString() || "1000",
      };
    }
    
    // If we get here with no data and no error, use defaults
    return {
      balanceAlertEnabled: false,
      minBalance: "50",
      transactionAlertEnabled: false,
      transactionTypes: [],
      transactionAmountOption: "all",
      minTransactionAmount: "1000",
    };
  } catch (error) {
    console.error("Error fetching notification rules:", error);
    throw error;
  }
};

export const saveNotificationRules = async (
  values: NotificationRuleFormValues
): Promise<boolean> => {
  try {
    // Check if settings already exist
    const { data: existingRules, error: checkError } = await supabase
      .from("notification_rules")
      .select("id")
      .single();

    if (checkError && checkError.code !== "PGRST116") {
      // Log unexpected errors, but continue with insert attempt
      console.error("Error checking for existing rules:", checkError);
    }

    const rulesData = {
      balance_alert_enabled: values.balanceAlertEnabled,
      min_balance: values.balanceAlertEnabled ? Number(values.minBalance) : 0,
      transaction_alert_enabled: values.transactionAlertEnabled,
      transaction_types: values.transactionTypes,
      min_transaction_amount:
        values.transactionAlertEnabled && values.transactionAmountOption === "threshold"
          ? Number(values.minTransactionAmount)
          : 0,
    };

    if (existingRules) {
      // Update existing rules
      const { error } = await supabase
        .from("notification_rules")
        .update(rulesData)
        .eq("id", existingRules.id);
        
      if (error) {
        console.error("Error updating notification rules:", error);
        throw error;
      }
    } else {
      // Insert new rules
      const { error } = await supabase
        .from("notification_rules")
        .insert([rulesData]);
        
      if (error) {
        console.error("Error inserting notification rules:", error);
        throw error;
      }
    }

    return true;
  } catch (error) {
    console.error("Error saving notification rules:", error);
    throw error;
  }
};
