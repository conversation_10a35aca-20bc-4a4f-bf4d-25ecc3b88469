
import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { CustomerInputFields } from "./CustomerInputFields";
import { SpecialCustomerSelector } from "@/components/customers/special/SpecialCustomerSelector";
import { Toggle } from "@/components/ui/toggle";
import { Label } from "@/components/ui/label";

interface Customer {
  id: string;
  name: string;
  phone?: string;
}

interface SpecialCustomer {
  id: string;
  name: string;
  phone?: string | null;
}

interface CustomerFieldsProps {
  form: UseFormReturn<any>;
  customers: Customer[];
  isPhoneRequired?: boolean;
}

export function CustomerFields({ form, isPhoneRequired = false }: CustomerFieldsProps) {
  const [useSpecialCustomer, setUseSpecialCustomer] = useState<boolean>(false);
  const [selectedSpecialCustomer, setSelectedSpecialCustomer] = useState<SpecialCustomer | null>(null);

  const handleSpecialCustomerSelect = (customer: SpecialCustomer | null) => {
    setSelectedSpecialCustomer(customer);
    if (customer) {
      form.setValue("customerName", customer.name);
      form.setValue("customerPhone", customer.phone || "");
    } else {
      form.setValue("customerName", "");
      form.setValue("customerPhone", "");
    }
  };

  const handleToggleChange = () => {
    setUseSpecialCustomer(!useSpecialCustomer);
    
    // Reset form values when toggling
    if (useSpecialCustomer) {
      setSelectedSpecialCustomer(null);
    }
    
    form.setValue("customerName", "");
    form.setValue("customerPhone", "");
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between space-x-4 rtl:space-x-reverse bg-muted/30 p-2.5 rounded-lg shadow-sm border border-muted/70">
        <Label htmlFor="use-special-customer" className="text-sm font-medium">
          استخدام عميل مميز
        </Label>
        <Toggle
          id="use-special-customer"
          pressed={useSpecialCustomer}
          onPressedChange={handleToggleChange}
          aria-label="استخدام عميل مميز"
          className="toggle-special-customer h-8 px-3 data-[state=on]:bg-gradient-to-r data-[state=on]:from-red-500 data-[state=on]:to-red-600 dark:data-[state=on]:from-yellow-500 dark:data-[state=on]:to-amber-600 data-[state=on]:text-white flex items-center gap-2"
        >
          {useSpecialCustomer ? 'مفعل' : 'غير مفعل'}
        </Toggle>
      </div>
      
      {useSpecialCustomer ? (
        <SpecialCustomerSelector
          onSelectCustomer={handleSpecialCustomerSelect}
          selectedCustomer={selectedSpecialCustomer}
        />
      ) : (
        <CustomerInputFields 
          form={form} 
          isPhoneRequired={isPhoneRequired} 
        />
      )}
    </div>
  );
}
