import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Wallet } from "../utils/operationSchema";

const operationSchema = z.object({
  operation_type: z.string().min(1, "نوع العملية مطلوب"),
  amount: z.coerce.number().min(0.01, "المبلغ يجب أن يكون أكبر من صفر"),
  commission: z.coerce.number().min(0, "العمولة يجب أن تكون صفر أو أكثر").optional(),
  wallet_id: z.string().min(1, "المحفظة مطلوبة"),
});

export type OperationFormValues = z.infer<typeof operationSchema>;

interface UseFawryOperationFormProps {
  onSuccess: () => void;
  onClose: () => void;
  wallets: Wallet[];
}

export const useFawryOperationForm = ({ onSuccess, onClose, wallets }: UseFawryOperationFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedWallet, setSelectedWallet] = useState<Wallet | null>(null);

  const form = useForm<OperationFormValues>({
    resolver: zodResolver(operationSchema),
    defaultValues: {
      operation_type: "استلام",
      amount: undefined,
      commission: 0,
      wallet_id: "",
    },
  });

  // تحديث المحفظة المختارة عند تغيير القيمة
  useEffect(() => {
    const walletId = form.watch('wallet_id');
    if (walletId) {
      const wallet = wallets.find(w => w.id === walletId);
      setSelectedWallet(wallet || null);
    } else {
      setSelectedWallet(null);
    }
  }, [form.watch('wallet_id'), wallets]);

  const handleClose = () => {
    form.reset();
    setSelectedWallet(null);
    onClose();
  };

  const onSubmit = async (values: OperationFormValues) => {
    setIsSubmitting(true);

    try {
      // 1. التحقق من عملية الإرسال ورصيد المحفظة
      if (values.operation_type === "إرسال") {
        // الحصول على رصيد المحفظة
        const { data: walletData, error: walletFetchError } = await supabase
          .from("fawry_wallets")
          .select("balance")
          .eq("id", values.wallet_id)
          .single();

        if (walletFetchError) {
          throw walletFetchError;
        }

        const currentBalance = walletData.balance || 0;
        
        // التحقق من كفاية الرصيد للإرسال
        if (currentBalance < values.amount) {
          toast.error("رصيد غير كافي", {
            description: `رصيد المحفظة الحالي (${currentBalance} جنيه) لا يكفي لإجراء عملية بقيمة ${values.amount} جنيه`,
          });
          setIsSubmitting(false);
          return;
        }
      }

      // 2. إضافة العملية
      const { data, error: operationError } = await supabase
        .from("fawry_wallet_operations")
        .insert({
          operation_type: values.operation_type,
          amount: values.amount,
          commission: values.commission || 0,
          wallet_id: values.wallet_id,
        })
        .select();

      if (operationError) {
        throw operationError;
      }

      // 3. تحديث رصيد المحفظة
      const { data: walletData, error: walletFetchError } = await supabase
        .from("fawry_wallets")
        .select("balance")
        .eq("id", values.wallet_id)
        .single();

      if (walletFetchError) {
        throw walletFetchError;
      }

      const currentBalance = walletData.balance || 0;
      let newBalance = currentBalance;
      
      // حساب الرصيد ال��ديد بناءً على نوع العملية
      if (values.operation_type === "استلام") {
        // في حالة استلام، نضيف المبلغ للرصيد
        newBalance = currentBalance + values.amount;
      } else if (values.operation_type === "إرسال") {
        // في حالة إرسال، نخصم المبلغ من الرصيد
        newBalance = currentBalance - values.amount;
      }

      // تحديث رصيد المحفظة في قاعدة البيانات
      const { error: walletUpdateError } = await supabase
        .from("fawry_wallets")
        .update({ balance: newBalance })
        .eq("id", values.wallet_id);

      if (walletUpdateError) {
        throw walletUpdateError;
      }

      toast.success("تمت الإضافة بنجاح", {
        description: `تم إضافة عملية ${values.operation_type} بمبلغ ${values.amount} جنيه بنجاح`,
      });

      handleClose();
      onSuccess();
    } catch (error) {
      console.error("Error adding operation:", error);
      toast.error("خطأ في الإضافة", {
        description: "حدث خطأ أثناء إضافة العملية. الرجاء المحاولة مرة أخرى.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    form,
    selectedWallet,
    isSubmitting,
    onSubmit,
    handleClose
  };
};
