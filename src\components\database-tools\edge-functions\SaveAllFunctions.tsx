
import { Button } from "@/components/ui/button";
import { Database } from "lucide-react";
import { edgeFunctions } from "./types";
import { useSaveFunctions } from "./hooks/useSaveFunctions";
import { useIsMobile } from "@/hooks/use-mobile";

export function SaveAllFunctions() {
  const { isSaving, handleSaveAllFunctions } = useSaveFunctions();
  const isMobile = useIsMobile();
  
  const onSaveAll = () => {
    handleSaveAllFunctions(edgeFunctions);
  };
  
  return (
    <Button
      variant="outline"
      size={isMobile ? "icon-sm" : "sm"}
      onClick={onSaveAll}
      disabled={isSaving}
      className={`flex items-center ${isMobile ? "px-2" : "gap-2"} border-primary text-primary hover:bg-primary/10 hover:text-primary`}
    >
      {isMobile ? (
        <Database className="h-4 w-4" />
      ) : (
        <>
          <Database className="h-4 w-4" />
          <span className="whitespace-nowrap">
            {isSaving ? "جاري الحفظ..." : "حفظ جميع الوظائف"}
          </span>
        </>
      )}
    </Button>
  );
}
