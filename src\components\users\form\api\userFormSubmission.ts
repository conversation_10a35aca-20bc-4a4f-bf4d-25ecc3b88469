
import { UserRole, UserPermissions } from "@/types/user.types";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

type FormSubmissionProps = {
  email: string;
  password: string;
  role: UserRole;
  permissions: UserPermissions;
  setLoading: (value: boolean) => void;
  resetForm: () => void;
  onSuccess: () => void;
};

export const submitUserForm = async ({
  email,
  password,
  role,
  permissions,
  setLoading,
  resetForm,
  onSuccess
}: FormSubmissionProps) => {
  if (!email || !password) {
    toast.error("يرجى إدخال البريد الإلكتروني وكلمة المرور");
    return;
  }
  
  setLoading(true);
  
  try {
    console.log("Creating new user with email:", email);
    
    // Use signUp instead of the edge function which is having JWT issues
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: { created_by: "admin" }
      }
    });
    
    if (error) {
      if (error.message?.includes("already registered")) {
        toast.error("هذا البريد الإلكتروني مسجل مسبقاً");
        setLoading(false);
        return;
      }
      throw error;
    }
    
    if (!data?.user) {
      throw new Error("فشل إنشاء المستخدم");
    }
    
    const userId = data.user.id;
    console.log("User created with ID:", userId);
    
    // 3. Delete old roles and permissions before adding new ones
    const { error: deleteRoleError } = await supabase
      .from("user_roles")
      .delete()
      .eq("user_id", userId);
      
    if (deleteRoleError) {
      console.warn("Error deleting existing role:", deleteRoleError);
    }
    
    const { error: deletePermError } = await supabase
      .from("user_permissions")
      .delete()
      .eq("user_id", userId);
      
    if (deletePermError) {
      console.warn("Error deleting existing permissions:", deletePermError);
    }
    
    // 4. Add new role
    const { error: roleError } = await supabase
      .from("user_roles")
      .insert({ 
        user_id: userId, 
        role: role 
      });
      
    if (roleError) {
      console.error("Role assignment error:", roleError);
      throw roleError;
    }
    
    console.log("Role assigned:", role);
    
    // 5. Add new permissions
    const permissionsData = {
      user_id: userId,
      dashboard: role === "admin" || permissions.dashboard,
      transactions: role === "admin" || permissions.transactions,
      customers: role === "admin" || permissions.customers,
      debts: role === "admin" || permissions.debts,
      reports: role === "admin" || permissions.reports,
      notification_logs: role === "admin" || permissions.notification_logs,
      wallets: role === "admin" || permissions.wallets,
      users: role === "admin" || permissions.users,
      settings: role === "admin" || permissions.settings,
      database_tools: role === "admin" || permissions.database_tools
    };
    
    console.log("Setting permissions:", permissionsData);
    
    const { error: permissionsError } = await supabase
      .from("user_permissions")
      .insert(permissionsData);
      
    if (permissionsError) {
      console.error("Permissions assignment error:", permissionsError);
      throw permissionsError;
    }
    
    // 6. Add to employees table for better data display
    const { error: employeeError } = await supabase
      .from("employees")
      .upsert({
        auth_id: userId,
        email: email,
        name: email,
        role: role
      });
    
    if (employeeError) {
      console.warn("Failed to add to employees table:", employeeError);
      // Not throwing error here as it's not critical
    }
    
    // Using setTimeout to ensure the UI updates before any navigation happens
    setTimeout(() => {
      toast.success("تم إنشاء المستخدم بنجاح");
      resetForm();
      onSuccess();
    }, 100);
  } catch (error: any) {
    console.error("Error creating user:", error);
    toast.error(error.message || "حدث خطأ أثناء إنشاء المستخدم");
  } finally {
    setLoading(false);
  }
};
