
import { deleteBranch } from "@/services/branchService";
import { Branch } from "@/types/slice.types";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface DeleteBranchDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  branch: Branch | null;
  onBranchDeleted: () => void;
}

export function DeleteBranchDialog({
  isOpen,
  onOpenChange,
  branch,
  onBranchDeleted,
}: DeleteBranchDialogProps) {
  const handleDelete = async () => {
    if (!branch) return;
    
    const success = await deleteBranch(branch.id);
    
    if (success) {
      onOpenChange(false);
      onBranchDeleted();
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            هل أنت متأكد من حذف الفرع "{branch?.name}"؟
          </AlertDialogTitle>
          <AlertDialogDescription>
            هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الفرع بشكل نهائي.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>إلغاء</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete}>
            نعم، حذف الفرع
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
