
// Types for localStorage
export interface DefaultCommissionSettings {
  defaultDepositRate: number;
  defaultWithdrawalRate: number;
  applyToAll: boolean;
}

export interface WalletCommission {
  depositRate: number;
  withdrawalRate: number;
  minCommission: number;
}

export interface WalletCommissionsSettings {
  [key: string]: WalletCommission;
}

// Schema types
export interface DefaultCommissionFormValues {
  defaultDepositRate: number;
  defaultWithdrawalRate: number;
  applyToAll: boolean;
}

export interface WalletCommissionFormValues {
  [key: string]: number;
  vodafoneDeposit: number;
  vodafoneWithdrawal: number;
  vodafoneMinCommission: number;
  etisalatDeposit: number;
  etisalatWithdrawal: number;
  etisalatMinCommission: number;
  orangeDeposit: number;
  orangeWithdrawal: number;
  orangeMinCommission: number;
  instaPayDeposit: number;
  instaPayWithdrawal: number;
  instaPayMinCommission: number;
}
