
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { SpecialCustomerList } from "./SpecialCustomerList";
import { AddSpecialCustomerDialog } from "./AddSpecialCustomerDialog";

export function SpecialCustomersTab() {
  const [isAddCustomerOpen, setIsAddCustomerOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
    toast.success("تم تحديث قائمة العملاء المميزين");
  };

  const handleCustomerAdded = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <Card className="border-none shadow-lg dark:bg-card">
      <CardHeader className="p-5">
        <div className="flex flex-col space-y-4">
          <CardTitle className="text-xl flex items-center gap-2">قائمة العملاء</CardTitle>
          
          {/* Desktop buttons */}
          <div className="hidden md:flex items-center gap-2 justify-end">
            <Button 
              onClick={() => setIsAddCustomerOpen(true)} 
              size="sm" 
              className="gap-1 hover:scale-105 transition-transform"
            >
              <Plus className="h-4 w-4" />
              إضافة عميل مميز
            </Button>
            <Button 
              onClick={handleRefresh} 
              variant="outline" 
              size="sm" 
              className="gap-1 hover:scale-105 transition-transform bg-red-600 text-white hover:bg-red-700 dark:bg-[#D0AC19] dark:text-black dark:hover:bg-[#D0AC19]/90" 
              title="تحديث"
            >
              <RefreshCw className="h-4 w-4" />
              تحديث
            </Button>
          </div>

          {/* Mobile icon buttons */}
          <div className="flex md:hidden items-center gap-2 justify-center">
            <Button 
              onClick={() => setIsAddCustomerOpen(true)} 
              size="icon" 
              className="hover:scale-105 transition-transform"
              title="إضافة عميل مميز"
            >
              <Plus className="h-4 w-4" />
            </Button>
            <Button 
              onClick={handleRefresh} 
              variant="outline" 
              size="icon" 
              className="hover:scale-105 transition-transform bg-red-600 text-white hover:bg-red-700 dark:bg-[#D0AC19] dark:text-black dark:hover:bg-[#D0AC19]/90" 
              title="تحديث"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-5">
        <div className="bg-card/50 rounded-xl border border-border/40 shadow-sm overflow-hidden animate-fade-in">
          <div className="p-6 bg-background">
            <SpecialCustomerList key={refreshKey} />
          </div>
        </div>
      </CardContent>

      <AddSpecialCustomerDialog 
        open={isAddCustomerOpen} 
        onOpenChange={setIsAddCustomerOpen} 
        onSuccess={handleCustomerAdded} 
      />
    </Card>
  );
}
