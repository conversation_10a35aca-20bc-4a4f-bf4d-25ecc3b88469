import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info, AlertCircle } from "lucide-react";
import { TransactionType } from "@/utils/transaction/types";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
interface TransactionWarningsProps {
  insufficientBalance: boolean;
  isZeroBalance: boolean;
  exceedsLimit: boolean;
  monthlyLimitExceeded: boolean;
  transactionType: TransactionType;
  amount: number;
  commission: number;
  selectedSimBalance: number | null;
  monthlyTotal: number;
  monthlyRemaining: number;
  isMobileSimType: boolean;
  receiveLimit?: number; // Add the new prop for receive limit
}
export function TransactionWarnings({
  insufficientBalance,
  isZeroBalance,
  exceedsLimit,
  monthlyLimitExceeded,
  transactionType,
  amount,
  commission,
  selectedSimBalance,
  monthlyTotal,
  monthlyRemaining,
  isMobileSimType,
  receiveLimit = 300000 // Default to 300,000 if not provided
}: TransactionWarningsProps) {
  const [maxReceiveLimit, setMaxReceiveLimit] = useState<number>(receiveLimit);

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'decimal',
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Fetch maximum receive limit from the database
  useEffect(() => {
    async function fetchMaxReceiveLimit() {
      try {
        const {
          data,
          error
        } = await supabase.from('sims').select('receive_limit').order('receive_limit', {
          ascending: false
        }).limit(1);
        if (error) {
          console.error('Error fetching max receive limit:', error);
          return;
        }
        if (data && data.length > 0 && data[0].receive_limit) {
          setMaxReceiveLimit(data[0].receive_limit);
        }
      } catch (err) {
        console.error('Unexpected error fetching max receive limit:', err);
      }
    }
    fetchMaxReceiveLimit();
  }, []);
  return <>
      {insufficientBalance && transactionType === 'send' && <Alert variant="destructive" className="text-right">
          <AlertDescription className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            رصيد الشريحة غير كافٍ لإتمام عملية الإرسال. المبلغ الكلي المطلوب: {Number(amount) + Number(commission)} ج.م، الرصيد المتاح: {selectedSimBalance} ج.م
          </AlertDescription>
        </Alert>}

      {isZeroBalance && transactionType === 'send' && <Alert variant="destructive" className="text-right">
          <AlertDescription className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            لا يمكن إرسال الأموال. الرصيد صفر. يمكن استلام الأموال فقط.
          </AlertDescription>
        </Alert>}

      {exceedsLimit && <Alert variant="destructive" className="text-right">
          <AlertDescription className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            الحد الأقصى لاستلام الأموال عبر شريحة الموبايل هو {formatCurrency(maxReceiveLimit)} جنيه.
          </AlertDescription>
        </Alert>}

      {monthlyLimitExceeded && transactionType === 'receive' && <Alert variant="destructive" className="text-right">
          <AlertDescription className="flex flex-col items-end gap-1">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              <span>تم تجاوز الحد الشهري لاستلام الأموال على هذه الشريحة ({formatCurrency(receiveLimit)} جنيه شهريًا)</span>
            </div>
            <div className="text-sm font-medium">
              إجمالي الاستلامات هذا الشهر: {formatCurrency(monthlyTotal)} ج.م
            </div>
            <div className="text-sm">
              المتبقي من الحد الشهري: {formatCurrency(monthlyRemaining)} ج.م
            </div>
          </AlertDescription>
        </Alert>}

      {isMobileSimType && transactionType === 'receive' && !monthlyLimitExceeded && monthlyTotal > 0 && <div className="text-sm text-amber-600 dark:text-amber-500 flex flex-col gap-1 text-right border border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <Info className="h-4 w-4" />
            <span>معلومات الحد الشهري للاستلام:</span>
          </div>
          <div>إجمالي الاستلامات هذا الشهر: <span className="font-medium">{formatCurrency(monthlyTotal)} ج.م</span></div>
          <div>المتبقي من الحد الشهري: <span className="font-medium">{formatCurrency(monthlyRemaining)} ج.م</span></div>
          
        </div>}

      {isMobileSimType && transactionType === 'receive'}
    </>;
}