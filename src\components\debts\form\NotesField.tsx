
import { 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormMessage 
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";

interface NotesFieldProps {
  loading: boolean;
}

export function NotesField({ loading }: NotesFieldProps) {
  return (
    <FormField
      name="notes"
      render={({ field }) => (
        <FormItem>
          <div className="flex justify-between">
            <FormLabel>ملاحظات</FormLabel>
            <span className="text-xs text-muted-foreground">
              {(field.value?.length || 0)}/40
            </span>
          </div>
          <FormControl>
            <Textarea
              placeholder="أدخل ملاحظات إضافية (اختياري)"
              disabled={loading}
              maxLength={40}
              {...field}
              value={field.value || ""}
              onChange={(e) => {
                if (e.target.value.length <= 40) {
                  field.onChange(e);
                }
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
