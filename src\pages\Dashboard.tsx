
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useNavigate } from 'react-router-dom';
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { DashboardStats } from "@/components/dashboard/DashboardStats";
import { DashboardContent } from "@/components/dashboard/DashboardContent";
import { filterTransactionsByTimeframe } from "@/components/dashboard/DashboardUtils";
import { useIsMobile } from "@/hooks/use-mobile";
import { formatCurrency } from "@/utils/formatters";

export default function Dashboard() {
  const [stats, setStats] = useState({
    receivedAmount: 0,
    sentAmount: 0,
    netProfit: 0,
    transactionCount: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [timeframe, setTimeframe] = useState("month");
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  // Fetch dashboard statistics
  const fetchDashboardStats = async () => {
    setIsLoading(true);
    try {
      console.log("Fetching dashboard statistics...");
      
      // Get transactions data
      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('*');

      if (error) {
        console.error("Error fetching transactions:", error);
        throw error;
      }

      console.log("Fetched transactions:", transactions?.length || 0);

      // Calculate statistics
      let received = 0;
      let sent = 0;
      let profit = 0;
      
      if (transactions && transactions.length > 0) {
        // إما نستخدم التصفية أو نعرض كل البيانات
        let filteredTransactions = transactions;
        
        // إذا كان الإطار الزمني ليس "all"، نطبق التصفية
        if (timeframe !== "all") {
          filteredTransactions = filterTransactionsByTimeframe(transactions, timeframe);
        }
        
        console.log("Filtered transactions:", filteredTransactions.length);
        
        // إذا لم توجد معاملات مفلترة، نستخدم كل المعاملات
        if (filteredTransactions.length === 0) {
          filteredTransactions = transactions;
          console.log("No filtered transactions found, using all transactions:", filteredTransactions.length);
        }
        
        received = filteredTransactions
          .filter(tx => tx.transaction_type === 'receive')
          .reduce((sum, tx) => sum + Number(tx.amount || 0), 0);
          
        sent = filteredTransactions
          .filter(tx => tx.transaction_type === 'send')
          .reduce((sum, tx) => sum + Number(tx.amount || 0), 0);
          
        profit = filteredTransactions
          .reduce((sum, tx) => sum + Number(tx.commission || 0), 0);

        console.log("Calculated stats:", { received, sent, profit, count: filteredTransactions.length });

        setStats({
          receivedAmount: received,
          sentAmount: sent,
          netProfit: profit,
          transactionCount: filteredTransactions.length
        });
      } else {
        console.log("No transactions found");
        setStats({
          receivedAmount: 0,
          sentAmount: 0,
          netProfit: 0,
          transactionCount: 0
        });
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      toast.error("حدث خطأ في جلب البيانات");
    } finally {
      setIsLoading(false);
    }
  };

  // Navigate to debts page
  const navigateToDebts = () => {
    navigate('/debts');
  };

  // Fetch data on component mount and when timeframe changes
  useEffect(() => {
    fetchDashboardStats();
  }, [timeframe]);

  // Refresh data
  const handleRefresh = () => {
    fetchDashboardStats();
    toast.success("تم تحديث البيانات");
  };

  // Transform the stats object into an array of StatItem objects
  const statsArray = [
    {
      title: "إجمالي المستلم",
      value: formatCurrency(stats.receivedAmount),
      icon: "arrow-down",
      description: "إجمالي المبلغ المستلم",
      valueColor: "green" as const
    },
    {
      title: "إجمالي المرسل",
      value: formatCurrency(stats.sentAmount),
      icon: "arrow-up",
      description: "إجمالي المبلغ المرسل",
      valueColor: "red" as const
    },
    {
      title: "صافي الأرباح",
      value: formatCurrency(stats.netProfit),
      icon: "wallet",
      description: "إجمالي العمولات المحصلة",
      valueColor: "green" as const
    },
    {
      title: "عدد المعاملات",
      value: stats.transactionCount.toString(),
      icon: "file-text",
      description: "إجمالي عمليات الإرسال والاستلام"
    }
  ];

  return (
    <div className={`p-4 sm:p-6 bg-background min-h-screen ${isMobile ? 'pt-20' : ''}`}>
      <DashboardHeader 
        timeframe={timeframe}
        setTimeframe={setTimeframe}
        handleRefresh={handleRefresh}
      />
      
      <DashboardStats 
        stats={statsArray}
        isLoading={isLoading}
      />
      
      <DashboardContent navigateToDebts={navigateToDebts} />
    </div>
  );
}
