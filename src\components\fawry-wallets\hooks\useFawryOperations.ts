
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Wallet, Operation } from "../utils/operationSchema";

interface JoinedOperation extends Operation {
  wallet: { name: string };
}

export const useFawryOperations = (refreshKey: number = 0) => {
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [operations, setOperations] = useState<Operation[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Fetch wallets
  const fetchWallets = async () => {
    try {
      const { data, error } = await supabase
        .from("fawry_wallets")
        .select("id, name, phone_number, balance");

      if (error) {
        throw error;
      }

      setWallets(data || []);
    } catch (error) {
      console.error("Error fetching wallets:", error);
    }
  };

  // Fetch operations
  const fetchOperations = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("fawry_wallet_operations")
        .select(`
          *,
          wallet:wallet_id(name)
        `)
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      const formattedData = data.map((item: JoinedOperation) => ({
        ...item,
        wallet_name: item.wallet?.name || "غير معروف",
      }));

      setOperations(formattedData);
    } catch (error) {
      console.error("Error fetching operations:", error);
      toast({
        variant: "destructive",
        title: "خطأ في تحميل البيانات",
        description: "حدث خطأ أثناء تحميل العمليات. الرجاء المحاولة مرة أخرى.",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWallets();
    fetchOperations();
  }, [refreshKey]);

  return {
    wallets,
    operations,
    loading,
    fetchOperations,
    fetchWallets
  };
};
