
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle2 } from "lucide-react";

export function ServiceInfo() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <Card className="hover:shadow-lg transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-xl font-bold flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 text-primary" />
            الاشتراك في الخدمة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            للاطلاع على الخطط المتاحة أو الحصول على معلومات تفصيلية حول الاشتراك، 
            يمكنك التواصل معنا عبر أي من وسائل الاتصال الموضحة أعلاه. 
            فريقنا المختص سيسعد بمساعدتك.
          </p>
          <div className="mt-4 space-y-2">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span>باقات شهرية وسنوية مرنة</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span>دعم فني مجاني مع كل باقة</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span>ترقية الباقة في أي وقت</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Technical Support Information */}
      <Card className="hover:shadow-lg transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-xl font-bold flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 text-primary" />
            الدعم الفني والاستفسارات العامة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            إذا كنت تواجه أي مشكلة فنية أو لديك استفسار، يرجى التواصل مع فريق الدعم الفني. 
            نحن نعمل بكل جد لضمان استجابتنا السريعة وحل مشكلتك بكفاءة.
          </p>
          <div className="mt-4 space-y-2">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span>استجابة سريعة خلال ساعات العمل</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span>حلول فعالة للمشكلات الفنية</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span>فريق متخصص ذو خبرة</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
