
import { useIsMobile } from "@/hooks/use-mobile";

interface TransactionAmountProps {
  amount: number;
  transactionType: string;
}

export function TransactionAmount({ amount, transactionType }: TransactionAmountProps) {
  const isMobile = useIsMobile();
  const isReceive = transactionType === 'receive';
  
  // تنسيق المبلغ باستخدام الأرقام العربية
  const formattedAmount = new Intl.NumberFormat("ar-EG", {
    style: "decimal",
    maximumFractionDigits: 0,
  }).format(amount);
  
  return (
    <div className={`text-left ${
      isReceive
        ? 'text-green-600 dark:text-green-400' 
        : 'text-red-600 dark:text-red-400'
    } ${isMobile ? 'text-xs' : ''} font-medium`}>
      <span className="rtl-currency">
        {isReceive ? '+' : '-'} 
        {'ج.م '} 
        {formattedAmount}
      </span>
    </div>
  );
}
