
import React from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Wallet } from "./utils/operationSchema";
import { useFawryOperationForm, OperationFormValues } from "./hooks/useFawryOperationForm";
import FawryOperationForm from "./components/FawryOperationForm";

interface AddFawryOperationDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  wallets: Wallet[];
  onOperationAdded: () => void;
}

const AddFawryOperationDialog: React.FC<AddFawryOperationDialogProps> = ({
  isOpen,
  onOpenChange,
  wallets,
  onOperationAdded,
}) => {
  const {
    form,
    selectedWallet,
    isSubmitting,
    onSubmit,
    handleClose
  } = useFawryOperationForm({
    onSuccess: onOperationAdded,
    onClose: () => onOpenChange(false),
    wallets
  });

  const handleFormSubmit = (values: OperationFormValues) => {
    onSubmit(values);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>إضافة عملية جديدة</DialogTitle>
          <DialogDescription>
            قم بإدخال بيانات العملية الجديدة في النموذج أدناه
          </DialogDescription>
        </DialogHeader>
        
        <FawryOperationForm
          form={form}
          wallets={wallets}
          selectedWallet={selectedWallet}
          isSubmitting={isSubmitting}
          onClose={handleClose}
          onSubmit={handleFormSubmit}
        />
      </DialogContent>
    </Dialog>
  );
};

export default AddFawryOperationDialog;
