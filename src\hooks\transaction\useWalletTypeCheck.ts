
import { useState } from 'react';
import { Wallet } from '@/hooks/useTransactionForm';

/**
 * Hook للتحقق من نوع المحفظة والوظائف المرتبطة بها
 */
export function useWalletTypeCheck(wallets: Wallet[]) {
  const [isFawryWallet, setIsFawryWallet] = useState(false);

  /**
   * التحقق مما إذا كانت المحفظة هي محفظة فوري
   */
  const checkWalletType = (walletId: string | null) => {
    if (!walletId) {
      setIsFawryWallet(false);
      return false;
    }

    const selectedWallet = wallets.find(wallet => wallet.id === walletId);
    if (selectedWallet) {
      // التحقق مما إذا كانت هذه محفظة فوري باستخدام خاصية wallet_type
      const isFawry = selectedWallet.wallet_type === 'fawry';
      console.log('Checking wallet type:', selectedWallet.name, 'isFawry:', isFawry, 'wallet_type:', selectedWallet.wallet_type);
      setIsFawryWallet(isFawry);
      return isFawry;
    }
    
    return false;
  };

  return {
    isFawryWallet,
    checkWalletType
  };
}
