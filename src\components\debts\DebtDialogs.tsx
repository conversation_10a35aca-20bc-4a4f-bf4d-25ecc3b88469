
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Debt } from "@/types/debt.types";
import { DebtViewDialog } from "./DebtViewDialog";
import { DebtEditDialog } from "./DebtEditDialog";
import { DebtDeleteDialog } from "./DebtDeleteDialog";
import { useIsMobile } from "@/hooks/use-mobile";

interface DebtDialogsProps {
  viewingDebt: Debt | null;
  setViewingDebt: (debt: Debt | null) => void;
  editingDebt: Debt | null;
  setEditingDebt: (debt: Debt | null) => void;
  deletingDebtId: string | null;
  setDeletingDebtId: (id: string | null) => void;
  onDeleteConfirm: () => void;
  onEditSuccess: () => void;
  getWalletName: (transactionId: string | null) => string;
  formatCurrency: (amount: number) => string;
  formatDate: (date: string) => string;
}

export function DebtDialogs({
  viewingDebt,
  setViewingDebt,
  editingDebt,
  setEditingDebt,
  deletingDebtId,
  setDeletingDebtId,
  onDeleteConfirm,
  onEditSuccess,
  getWalletName,
  formatCurrency,
  formatDate
}: DebtDialogsProps) {
  const isMobile = useIsMobile();
  
  return (
    <>
      {/* View Debt Dialog */}
      <Dialog
        open={!!viewingDebt}
        onOpenChange={(open) => !open && setViewingDebt(null)}
      >
        <DebtViewDialog
          debt={viewingDebt}
          walletName={viewingDebt ? getWalletName(viewingDebt.transaction_id) : ""}
          onClose={() => setViewingDebt(null)}
          formatCurrency={formatCurrency}
          formatDate={formatDate}
        />
      </Dialog>

      {/* Edit Debt Dialog */}
      <Dialog
        open={!!editingDebt}
        onOpenChange={(open) => !open && setEditingDebt(null)}
      >
        <DebtEditDialog
          debt={editingDebt}
          onSuccess={onEditSuccess}
          onClose={() => setEditingDebt(null)}
        />
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <DebtDeleteDialog
        isOpen={!!deletingDebtId}
        onClose={() => setDeletingDebtId(null)}
        onConfirm={onDeleteConfirm}
      />
    </>
  );
}
