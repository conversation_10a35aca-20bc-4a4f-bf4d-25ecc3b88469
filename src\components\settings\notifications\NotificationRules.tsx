
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Save, AlertTriangle, Loader2 } from "lucide-react";
import { useNotificationRules } from "./rules/useNotificationRules";
import { BalanceAlerts } from "./rules/BalanceAlerts";
import { TransactionAlerts } from "./rules/TransactionAlerts";
import { toast } from "sonner";
import { useIsMobile } from "@/hooks/use-mobile";

export function NotificationRules() {
  const {
    balanceAlertEnabled,
    setBalanceAlertEnabled,
    minBalance,
    setMinBalance,
    transactionAlertEnabled,
    setTransactionAlertEnabled,
    transactionTypes,
    transactionAmountOption,
    setTransactionAmountOption,
    minTransactionAmount,
    setMinTransactionAmount,
    isSaving,
    isLoading,
    error,
    handleTransactionTypeChange,
    handleSaveRules
  } = useNotificationRules();
  
  const isMobile = useIsMobile();

  const handleSave = async () => {
    try {
      await handleSaveRules();
      toast.success("تم حفظ الإعدادات بنجاح");
    } catch (error) {
      console.error("Error saving notification rules:", error);
      toast.error("حدث خطأ أثناء حفظ الإعدادات");
    }
  };

  if (isLoading) {
    return (
      <Card className="mb-6 overflow-hidden border-b-4 border-b-amber-500 shadow-sm">
        <CardHeader className="bg-gradient-to-r from-amber-50 to-transparent dark:from-amber-900/20 dark:to-transparent">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            <CardTitle>قواعد الإشعارات</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-4 md:p-6">
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-amber-500" />
            <span className="mr-2 text-lg text-muted-foreground">جاري تحميل القواعد...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="mb-6 overflow-hidden border-b-4 border-b-red-500 shadow-sm">
        <CardHeader className="bg-gradient-to-r from-red-50 to-transparent dark:from-red-900/20 dark:to-transparent">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <CardTitle>قواعد الإشعارات</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-4 md:p-6">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
            <p className="text-lg font-medium text-red-500 mb-2">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline" className="mt-4">
              إعادة تحميل الصفحة
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-6 overflow-hidden border-b-4 border-b-amber-500 shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="bg-gradient-to-r from-amber-50 to-transparent dark:from-amber-900/20 dark:to-transparent">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-amber-500" />
          <CardTitle>قواعد الإشعارات</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="p-4 md:p-6">
        <div className={`space-y-4 md:space-y-8 ${isMobile ? "pb-16" : ""}`}>
          {/* Balance Alerts Section */}
          <div className="bg-gradient-to-r from-amber-50/50 to-transparent dark:from-amber-900/10 dark:to-transparent p-3 md:p-4 rounded-lg border border-amber-100/50 dark:border-amber-800/30">
            <h3 className="text-base md:text-lg font-medium mb-3 md:mb-4 text-amber-700 dark:text-amber-400">إعدادات تنبيهات الرصيد</h3>
            <BalanceAlerts
              balanceAlertEnabled={balanceAlertEnabled}
              setBalanceAlertEnabled={setBalanceAlertEnabled}
              minBalance={minBalance}
              setMinBalance={setMinBalance}
            />
          </div>

          {/* Transaction Alerts Section */}
          <div className="bg-gradient-to-r from-amber-50/50 to-transparent dark:from-amber-900/10 dark:to-transparent p-3 md:p-4 rounded-lg border border-amber-100/50 dark:border-amber-800/30">
            <h3 className="text-base md:text-lg font-medium mb-3 md:mb-4 text-amber-700 dark:text-amber-400">إعدادات تنبيهات المعاملات</h3>
            <TransactionAlerts
              transactionAlertEnabled={transactionAlertEnabled}
              setTransactionAlertEnabled={setTransactionAlertEnabled}
              transactionTypes={transactionTypes}
              handleTransactionTypeChange={handleTransactionTypeChange}
              transactionAmountOption={transactionAmountOption}
              setTransactionAmountOption={setTransactionAmountOption}
              minTransactionAmount={minTransactionAmount}
              setMinTransactionAmount={setMinTransactionAmount}
            />
          </div>

          <div className="sticky bottom-4 pt-2 bg-card/80 backdrop-blur-sm z-10">
            <Button
              onClick={handleSave}
              className="w-full md:w-auto flex gap-2 bg-amber-500 hover:bg-amber-600 transition-colors"
              disabled={isSaving}
              size={isMobile ? "lg" : "default"}
            >
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  حفظ قواعد الإشعارات
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
