
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import WalletConnectionTest from "@/components/wallets/WalletConnectionTest";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Download, 
  Lock, 
  Shield, 
  Database as DatabaseIcon,
  ThumbsUp,
  ThumbsDown,
  ArrowUpDown,
  BarChart
} from "lucide-react";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

// Define types for database status
interface DatabaseStat {
  value: number | string;
  label: string;
  icon: React.ReactNode;
}

export function DatabaseSettings() {
  const [activeTab, setActiveTab] = useState<string>("connection");
  const [loading, setLoading] = useState<boolean>(true);
  const [statusLoading, setStatusLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<DatabaseStat[]>([]);
  
  useEffect(() => {
    // Simulate loading database statistics
    const loadDatabaseStats = async () => {
      setStatusLoading(true);
      try {
        // Make a simple query to check connection status
        const startTime = performance.now();
        const { data, error } = await supabase
          .from('user_roles')
          .select('count(*)', { count: 'exact', head: true });
        
        const endTime = performance.now();
        const queryTime = Math.round(endTime - startTime);
        
        if (error) throw error;
        
        // Generate mock stats - in a real app, these would come from the database
        setStats([
          {
            value: "متصل",
            label: "حالة الاتصال",
            icon: <Wifi className="h-4 w-4 text-green-500" />,
          },
          {
            value: `${queryTime} ms`,
            label: "زمن الاستجابة",
            icon: <RefreshCw className="h-4 w-4 text-blue-500" />,
          },
          {
            value: "Supabase",
            label: "نوع قاعدة البيانات",
            icon: <DatabaseIcon className="h-4 w-4 text-purple-500" />,
          },
          {
            value: "نشط",
            label: "الأمان",
            icon: <Shield className="h-4 w-4 text-amber-500" />,
          },
        ]);
      } catch (error) {
        console.error("Error fetching database stats:", error);
        setStats([
          {
            value: "غير متصل",
            label: "حالة الاتصال",
            icon: <WifiOff className="h-4 w-4 text-red-500" />,
          },
          {
            value: "غير متاح",
            label: "زمن الاستجابة",
            icon: <RefreshCw className="h-4 w-4 text-muted-foreground" />,
          },
          {
            value: "Supabase",
            label: "نوع قاعدة البيانات",
            icon: <DatabaseIcon className="h-4 w-4 text-purple-500" />,
          },
          {
            value: "نشط",
            label: "الأمان",
            icon: <Shield className="h-4 w-4 text-amber-500" />,
          },
        ]);
        toast.error("فشل الاتصال بقاعدة البيانات");
      } finally {
        setStatusLoading(false);
        setLoading(false);
      }
    };

    loadDatabaseStats();
  }, []);

  return (
    <Card className="overflow-hidden">
      <CardHeader className="bg-muted/50">
        <CardTitle>إعدادات قاعدة البيانات</CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        {loading ? (
          <div className="flex flex-col space-y-3">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="connection" className="flex items-center gap-2">
                <Wifi className="h-4 w-4" />
                اتصال القاعدة
              </TabsTrigger>
              <TabsTrigger value="status" className="flex items-center gap-2">
                <BarChart className="h-4 w-4" />
                حالة القاعدة
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Lock className="h-4 w-4" />
                الأمان
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="connection" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {statusLoading ? (
                  Array(4).fill(0).map((_, i) => (
                    <Skeleton key={i} className="h-24 w-full" />
                  ))
                ) : (
                  stats.map((stat, index) => (
                    <Card key={index} className="border-l-4 border-l-primary">
                      <CardContent className="p-4">
                        <div className="flex flex-col items-start">
                          <div className="flex items-center gap-2 mb-2">
                            {stat.icon}
                            <span className="text-sm font-medium">{stat.label}</span>
                          </div>
                          <div className="text-xl font-bold">{stat.value}</div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
              
              <div className="mt-6">
                <WalletConnectionTest />
              </div>
            </TabsContent>
            
            <TabsContent value="status" className="space-y-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex flex-col space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <ThumbsUp className="h-5 w-5 text-green-500" />
                        <span>السجلات النشطة</span>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-600">نشط</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <ArrowUpDown className="h-5 w-5 text-blue-500" />
                        <span>العمليات اليومية</span>
                      </div>
                      <Badge variant="outline" className="bg-blue-50 text-blue-600">15 عملية / دقيقة</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <ThumbsDown className="h-5 w-5 text-red-500" />
                        <span>الأخطاء المسجلة</span>
                      </div>
                      <Badge variant="outline" className="bg-red-50 text-red-600">0 خطأ</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end gap-2">
                <Button variant="outline" className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  تحديث الإحصائيات
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  تنزيل التقرير
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardContent className="p-4 space-y-4">
                  <div className="flex items-center gap-2">
                    <Shield className="h-6 w-6 text-green-500" />
                    <div>
                      <h3 className="font-medium">حماية البيانات</h3>
                      <p className="text-sm text-muted-foreground">يتم تطبيق سياسات أمان مستوى الصف (RLS) على جميع الجداول</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Lock className="h-6 w-6 text-green-500" />
                    <div>
                      <h3 className="font-medium">التشفير</h3>
                      <p className="text-sm text-muted-foreground">البيانات الحساسة مشفرة في الراحة وأثناء النقل</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <div className="flex justify-end">
                <Button variant="outline" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  تفاصيل الأمان
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
}

export default DatabaseSettings;
