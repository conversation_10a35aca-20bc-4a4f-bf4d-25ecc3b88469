
import { useIsMobile } from "@/hooks/use-mobile";
import { ProcessedTransaction } from "@/types/transaction.types";
import { 
  TransactionIcon, 
  TransactionTitle, 
  TransactionDetails, 
  TransactionAmount 
} from "./transaction-item";

interface TransactionItemProps {
  transaction: ProcessedTransaction;
  formatDate: (dateStr: string) => string;
}

export function TransactionItem({ transaction, formatDate }: TransactionItemProps) {
  const isMobile = useIsMobile();
  
  // استخدام اسم العميل من الحقل الجديد إذا كان متوفرًا
  const customerName = transaction.customer_name || transaction.customerName || '';
  // استخدام رقم هاتف العميل إذا كان متوفرًا
  const customerPhone = transaction.customer_phone || '';
  
  return (
    <div className={`card-item hover:bg-blue-50/50 dark:hover:bg-blue-900/10 px-3 sm:px-4 py-2 sm:py-3 ${isMobile ? 'border-b border-border/30' : ''}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 sm:gap-3">
          <TransactionIcon transactionType={transaction.transaction_type} />
          <div>
            <TransactionTitle 
              transactionType={transaction.transaction_type}
              customerName={customerName}
              description={transaction.description || ''}
              customerPhone={customerPhone}
            />
            <TransactionDetails 
              date={transaction.created_at}
              formatDate={formatDate}
              walletName={transaction.walletName || ''}
              commission={transaction.commission}
            />
          </div>
        </div>
        <TransactionAmount 
          amount={transaction.amount} 
          transactionType={transaction.transaction_type} 
        />
      </div>
    </div>
  );
}
