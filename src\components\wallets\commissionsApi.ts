
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { DefaultCommissionFormValues, WalletCommissionFormValues } from "./commissionsTypes";

// Load commission settings from database
export const loadDefaultCommissionSettings = async () => {
  try {
    // First try to get existing settings
    const { data, error } = await supabase
      .from("commission_settings")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(1)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        // No data exists, create default settings
        console.log("No commission settings found, creating default settings");
        
        const defaultSettings = {
          default_deposit_rate: 0,
          default_withdrawal_rate: 1,
          apply_to_all: false
        };
        
        const { data: newSettings, error: createError } = await supabase
          .from("commission_settings")
          .insert([defaultSettings])
          .select()
          .single();
          
        if (createError) {
          console.error("Error creating default commission settings:", createError);
          toast.error("حدث خطأ أثناء إنشاء إعدادات العمولة");
          return defaultSettings;
        }
        
        return newSettings;
      } else {
        console.error("Error loading default commission settings:", error);
        toast.error("حدث خطأ أثناء تحميل إعدادات العمولة");
        return null;
      }
    }

    return data;
  } catch (error) {
    console.error("Error loading default commission settings:", error);
    toast.error("حدث خطأ أثناء تحميل إعدادات العمولة");
    return null;
  }
};

export const loadWalletCommissionSettings = async () => {
  try {
    const { data, error } = await supabase
      .from("wallet_commissions")
      .select("*")
      .order("wallet_name");
    
    if (error) {
      console.error("Error loading wallet commission settings:", error);
      toast.error("حدث خطأ أثناء تحميل إعدادات العمولة");
      return null;
    }

    const result: Record<string, any> = {};
    if (data) {
      data.forEach(commission => {
        result[commission.wallet_name] = commission;
      });
    }

    return result;
  } catch (error) {
    console.error("Error loading wallet commission settings:", error);
    toast.error("حدث خطأ أثناء تحميل إعدادات العمولة");
    return null;
  }
};

// Save commission settings to database
export const saveDefaultCommissionSettings = async (data: DefaultCommissionFormValues) => {
  try {
    // Check if settings exist
    const existingSettings = await loadDefaultCommissionSettings();
    
    // TypeScript fix: Check if existingSettings is not null and has an id property
    if (existingSettings && 'id' in existingSettings) {
      // Update existing settings
      const { error } = await supabase
        .from("commission_settings")
        .update({
          default_deposit_rate: data.defaultDepositRate,
          default_withdrawal_rate: data.defaultWithdrawalRate,
          apply_to_all: data.applyToAll,
          updated_at: new Date().toISOString()
        })
        .eq("id", existingSettings.id as string);
      
      if (error) {
        console.error("Error saving default commission settings:", error);
        toast.error("حدث خطأ أثناء حفظ إعدادات العمولة");
        return null;
      }
    } else {
      // Create new settings
      const { error } = await supabase
        .from("commission_settings")
        .insert([{
          default_deposit_rate: data.defaultDepositRate,
          default_withdrawal_rate: data.defaultWithdrawalRate,
          apply_to_all: data.applyToAll,
        }]);
      
      if (error) {
        console.error("Error creating default commission settings:", error);
        toast.error("حدث خطأ أثناء إنشاء إعدادات العمولة");
        return null;
      }
    }

    // If apply to all is true
    if (data.applyToAll) {
      await applyDefaultSettingsToAllWallets(data.defaultDepositRate, data.defaultWithdrawalRate);
    }

    toast.success("تم حفظ إعدادات العمولة بنجاح");
    return data;
  } catch (error) {
    console.error("Error saving default commission settings:", error);
    toast.error("حدث خطأ أثناء حفظ إعدادات العمولة");
    return null;
  }
};

// تطبيق إعدادات العمولة الافتراضية على جميع المحافظ
export const applyDefaultSettingsToAllWallets = async (depositRate: number, withdrawalRate: number) => {
  try {
    // الحصول على جميع المحافظ من جدول wallet_commissions
    const { data, error } = await supabase
      .from("wallet_commissions")
      .select("wallet_name");
      
    if (error) throw error;
    
    // If there are no wallets in wallet_commissions, check the wallets table
    if (!data || data.length === 0) {
      const { data: walletData, error: walletError } = await supabase
        .from("wallets")
        .select("name");
        
      if (walletError) throw walletError;
      
      // Create commission entries for each wallet
      if (walletData && walletData.length > 0) {
        for (const wallet of walletData) {
          await supabase
            .from("wallet_commissions")
            .insert({
              wallet_name: wallet.name,
              deposit_rate: depositRate,
              withdrawal_rate: withdrawalRate,
              min_commission: 5,
              created_at: new Date().toISOString()
            });
        }
        
        return;
      }
    }
    
    const walletNames = data ? data.map(w => w.wallet_name) : [];
    
    // تحديث كل محفظة بإعدادات العمولة الافتراضية
    for (const walletName of walletNames) {
      await supabase
        .from("wallet_commissions")
        .update({
          deposit_rate: depositRate,
          withdrawal_rate: withdrawalRate,
          updated_at: new Date().toISOString()
        })
        .eq("wallet_name", walletName);
    }
  } catch (error) {
    console.error("Error applying default settings to all wallets:", error);
  }
};

export const saveWalletCommissionSettings = async (formValues: WalletCommissionFormValues) => {
  try {
    // Check if wallet commissions exist
    const { data, error } = await supabase
      .from("wallet_commissions")
      .select("*");
      
    if (error) throw error;
    
    // If no wallet commissions exist, create them
    if (!data || data.length === 0) {
      // Get all wallet names
      const { data: walletData, error: walletError } = await supabase
        .from("wallets")
        .select("name");
        
      if (walletError) throw walletError;
      
      // Create entries for each wallet
      if (walletData && walletData.length > 0) {
        for (const wallet of walletData) {
          const walletName = wallet.name;
          const depositKey = `${walletName}Deposit` as keyof WalletCommissionFormValues;
          const withdrawalKey = `${walletName}Withdrawal` as keyof WalletCommissionFormValues;
          const minCommissionKey = `${walletName}MinCommission` as keyof WalletCommissionFormValues;
          
          await supabase
            .from("wallet_commissions")
            .insert({
              wallet_name: walletName,
              deposit_rate: formValues[depositKey] || 0,
              withdrawal_rate: formValues[withdrawalKey] || 1,
              min_commission: formValues[minCommissionKey] || 5,
              created_at: new Date().toISOString()
            });
        }
        
        toast.success("تم حفظ إعدادات عمولات المحافظ بنجاح");
        return formValues;
      }
    }
    
    const updates = [];
    
    // Process each wallet commission
    for (const commission of data) {
      const walletName = commission.wallet_name;
      
      // Check if we have form values for this wallet
      const depositKey = `${walletName}Deposit` as keyof WalletCommissionFormValues;
      const withdrawalKey = `${walletName}Withdrawal` as keyof WalletCommissionFormValues;
      const minCommissionKey = `${walletName}MinCommission` as keyof WalletCommissionFormValues;
      
      if (formValues[depositKey] !== undefined || 
          formValues[withdrawalKey] !== undefined || 
          formValues[minCommissionKey] !== undefined) {
        
        updates.push(
          supabase
            .from("wallet_commissions")
            .update({
              deposit_rate: formValues[depositKey] || commission.deposit_rate,
              withdrawal_rate: formValues[withdrawalKey] || commission.withdrawal_rate,
              min_commission: formValues[minCommissionKey] || commission.min_commission,
              updated_at: new Date().toISOString()
            })
            .eq("wallet_name", walletName)
        );
      }
    }
    
    if (updates.length > 0) {
      await Promise.all(updates);
    }
    
    toast.success("تم حفظ إعدادات عمولات المحافظ بنجاح");
    return formValues;
  } catch (error) {
    console.error("Error saving wallet commission settings:", error);
    toast.error("حدث خطأ أثناء حفظ إعدادات العمولة");
    return null;
  }
};
