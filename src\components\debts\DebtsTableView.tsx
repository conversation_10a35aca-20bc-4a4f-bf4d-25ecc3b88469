
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Debt } from "@/types/debt.types";
import { DebtStatusBadge } from "./DebtStatusBadge";
import { DebtActions } from "./DebtActions";

interface DebtsTableViewProps {
  debts: Debt[];
  loading: boolean;
  handleEditClick: (debt: Debt) => void;
  handleViewClick: (debt: Debt) => void;
  handleDeleteClick: (debtId: string) => void;
  handleMarkAsPaid: (debtId: string) => void;
  getWalletName: (transactionId: string | null) => string;
  formatCurrency: (amount: number) => string;
  formatDate: (date: string | null) => string;
}

export function DebtsTableView({
  debts,
  loading,
  handleEditClick,
  handleViewClick,
  handleDeleteClick,
  handleMarkAsPaid,
  getWalletName,
  formatCurrency,
  formatDate
}: DebtsTableViewProps) {
  const renderActions = (debt: Debt) => (
    <DebtActions
      debt={debt}
      onEdit={handleEditClick}
      onView={handleViewClick}
      onDelete={handleDeleteClick}
      onMarkPaid={handleMarkAsPaid}
    />
  );

  return (
    <div className="modern-table-container animate-fade-in transition-all duration-300 elegant-table">
      <Table dir="rtl" className="rtl-table modern-table-gradient">
        <TableHeader>
          <TableRow className="bg-muted/20 border-b-2 border-amber-500/20 dark:border-amber-400/30">
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">العميل</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">المبلغ</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">المحفظة</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">تاريخ الاستحقاق</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">الحالة</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">الإجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-6 animate-pulse">
                جاري تحميل البيانات...
              </TableCell>
            </TableRow>
          ) : debts.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-6">
                لا يوجد ديون مسجلة
              </TableCell>
            </TableRow>
          ) : (
            debts.map((debt, index) => (
              <TableRow 
                key={debt.id} 
                className="hover:bg-amber-50/30 dark:hover:bg-amber-900/10 transition-all duration-200 animate-slide-in debt-card-hover"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <TableCell className="rtl-cell font-medium pr-6" label="العميل">
                  <div className="cell-content flex flex-col items-start">
                    <div className="font-medium">{debt.customer?.name || "غير محدد"}</div>
                    <div className="text-sm text-muted-foreground mt-1">{debt.customer?.phone || "—"}</div>
                  </div>
                </TableCell>
                <TableCell className="rtl-cell" label="المبلغ">
                  <div className="cell-content font-medium">
                    {formatCurrency(Number(debt.amount))}
                  </div>
                </TableCell>
                <TableCell className="rtl-cell" label="المحفظة">
                  <div className="cell-content">
                    {getWalletName(debt.transaction_id)}
                  </div>
                </TableCell>
                <TableCell className="rtl-cell" label="تاريخ الاستحقاق">
                  <div className="cell-content">
                    {formatDate(debt.due_date)}
                  </div>
                </TableCell>
                <TableCell className="rtl-cell" label="الحالة">
                  <div className="cell-content">
                    <DebtStatusBadge debt={debt} />
                  </div>
                </TableCell>
                <TableCell className="rtl-cell py-1" label="الإجراءات">
                  <div className="cell-content">
                    {renderActions(debt)}
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
