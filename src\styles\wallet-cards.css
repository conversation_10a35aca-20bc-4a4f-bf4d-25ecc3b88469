
@layer utilities {
  /* تنسيقات خاصة لبطاقات المحافظ */
  .wallet-card {
    @apply transition-all duration-300 rounded-xl overflow-hidden;
  }

  .wallet-card:hover {
    @apply transform -translate-y-1 shadow-md;
  }

  /* تنسيقات RTL للكروت */
  .rtl-card {
    @apply text-right;
  }

  /* مؤثرات تدريجية للبطاقات */
  .gradient-card {
    @apply relative overflow-hidden;
    background-size: 200% 200%;
    animation: gradientBackground 8s ease infinite;
  }

  @keyframes gradientBackground {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* خلفيات متدرجة للمحافظ حسب النوع */
  .vodafone-gradient {
    background: linear-gradient(135deg, #ff5252 0%, #ff1744 100%);
  }

  .etisalat-gradient {
    background: linear-gradient(135deg, #76ff03 0%, #64dd17 100%);
  }

  .orange-gradient {
    background: linear-gradient(135deg, #ffab40 0%, #ff9100 100%);
  }

  .instapay-gradient {
    background: linear-gradient(135deg, #40c4ff 0%, #00b0ff 100%);
  }

  /* تأثيرات إضافية للبطاقات */
  .card-shimmer {
    position: relative;
    overflow: hidden;
  }

  .card-shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 200%; }
  }

  /* تنسيقات مرنة للبطاقات على الأجهزة الصغيرة */
  .responsive-card {
    @apply w-full transition-all duration-300;
    min-height: 160px;
  }

  @media (min-width: 640px) {
    .responsive-card {
      @apply rounded-xl;
    }
  }

  /* تأثير بصري عند التفاعل */
  .interactive-element {
    @apply transition-all duration-200;
  }

  .interactive-element:hover {
    @apply transform scale-105;
  }

  .interactive-element:active {
    @apply transform scale-95;
  }

  /* تصميم بطاقات المعاملات والديون المستحقة - جديد */
  .dashboard-card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 
           dark:border-gray-700 transition-all duration-300 hover:shadow-md;
  }
  
  /* مؤثرات حركة بطيئة للبطاقات */
  .dashboard-card-animate {
    animation: cardEntrance 0.6s ease-out;
  }
  
  @keyframes cardEntrance {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* تنسيقات العناصر الداخلية للبطاقات */
  .card-item {
    @apply border-b border-gray-100 dark:border-gray-700 last:border-0
           transition-colors duration-200 py-3;
  }
  
  .card-item:hover {
    @apply bg-gray-50 dark:bg-gray-700/50;
  }
  
  /* ألوان دلالية للديون المستحقة */
  .debt-amount {
    @apply font-medium text-red-600 dark:text-red-400;
  }
  
  .debt-overdue {
    @apply text-xs text-red-500 dark:text-red-400 font-medium;
  }
  
  /* ألوان للمعاملات */
  .transaction-receive {
    @apply text-green-600 dark:text-green-400 font-medium;
  }
  
  .transaction-send {
    @apply text-red-600 dark:text-red-400 font-medium;
  }

  /* تأثيرات التوهج للبطاقات */
  .golden-glow {
    box-shadow: 0 0 15px 1px rgba(255, 215, 0, 0.3);
  }

  .blue-glow {
    box-shadow: 0 0 15px 1px rgba(59, 130, 246, 0.3);
  }

  .red-glow {
    box-shadow: 0 0 15px 1px rgba(239, 68, 68, 0.3);
  }

  .green-glow {
    box-shadow: 0 0 15px 1px rgba(34, 197, 94, 0.3);
  }

  .amber-glow {
    box-shadow: 0 0 15px 1px rgba(245, 158, 11, 0.3);
  }

  /* تحسينات للأزرار داخل البطاقات */
  .button-hover-effect {
    @apply transition-transform duration-150 ease-out;
  }

  .button-hover-effect:hover {
    @apply transform translate-x-0.5;
  }

  .button-hover-effect:active {
    @apply transform scale-95;
  }

  /* تحسين تأثير التوهج في وضع الظلام */
  .dark .golden-glow {
    box-shadow: 0 0 20px 2px rgba(255, 215, 0, 0.25);
  }
  
  .dark .blue-glow {
    box-shadow: 0 0 20px 2px rgba(59, 130, 246, 0.25);
  }
  
  .dark .red-glow {
    box-shadow: 0 0 20px 2px rgba(239, 68, 68, 0.25);
  }
  
  .dark .green-glow {
    box-shadow: 0 0 20px 2px rgba(34, 197, 94, 0.25);
  }
  
  .dark .amber-glow {
    box-shadow: 0 0 20px 2px rgba(245, 158, 11, 0.25);
  }
}
