
import { RecentTransactions } from "@/components/dashboard/RecentTransactions";
import { OutstandingDebts } from "@/components/dashboard/OutstandingDebts";
import { WalletBalances } from "@/components/dashboard/WalletBalances";
import { useIsMobile } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface DashboardContentProps {
  navigateToDebts: () => void;
}

export function DashboardContent({ navigateToDebts }: DashboardContentProps) {
  const isMobile = useIsMobile();
  const navigate = useNavigate();

  return (
    <>
      <div className="mb-6 sm:mb-8">
        <h2 className={`text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-foreground flex items-center ${isMobile ? 'rtl-mobile-text' : ''}`}>
          <span className="bg-primary/10 p-1 rounded-md mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" 
              stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" 
              className="text-primary">
              <rect width="20" height="14" x="2" y="5" rx="2" />
              <line x1="2" x2="22" y1="10" y2="10" />
            </svg>
          </span>
          المحافظ الإلكترونية
        </h2>
        <WalletBalances />
      </div>

      <div className={`grid grid-cols-1 ${isMobile ? "gap-6" : "md:grid-cols-2 gap-8"}`}>
        <div className="flex flex-col space-y-4">
          <OutstandingDebts navigateToDebts={navigateToDebts} />
          <Button 
            onClick={navigateToDebts}
            className={`gap-2 w-full bg-[#EF4343] hover:bg-[#EF4343]/90 text-white font-medium
                      dark:bg-[#D0AC19] dark:hover:bg-[#D0AC19]/90 dark:text-black dark:font-bold
                      shadow-sm hover:shadow transition-all duration-200 ${isMobile ? 'py-2 text-sm' : ''}`}
          >
            <Plus className={`${isMobile ? 'h-3.5 w-3.5' : 'h-4 w-4'}`} />
            إضافة دين جديد
          </Button>
        </div>
        <RecentTransactions />
      </div>
    </>
  );
}
