
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Database } from "lucide-react";
import { toast } from "sonner";
import { 
  createFullProjectBackup, 
  downloadBackupFile,
  saveBackupToHistory 
} from "../utils";

export function CreateFullBackupButton() {
  const [isFullBackupLoading, setIsFullBackupLoading] = useState(false);

  const handleCreateFullBackup = async () => {
    setIsFullBackupLoading(true);
    
    try {
      toast.info("جاري تنزيل البيانات المحدثة من قاعدة البيانات...", { duration: 3000 });
      
      // Create a timestamp to ensure we're getting fresh data
      const timestamp = new Date().toISOString();
      console.log(`Starting full backup at: ${timestamp}`);
      
      // Get fresh data directly from database
      const backupData = await createFullProjectBackup();
      
      // Log some information about the backup
      console.log(`Backup complete at: ${new Date().toISOString()}`);
      console.log(`Total tables: ${backupData.meta.tables_count}`);
      console.log(`Total records: ${backupData.meta.records_count}`);
      
      // Download the backup file
      await downloadBackupFile(backupData, 'full_project');
      
      // Save backup info to history with special type
      saveBackupToHistory(backupData, 'full');
      
      toast.success("تم تنزيل البيانات المحدثة من كل الجداول بنجاح");
    } catch (error) {
      console.error("Error creating full project backup:", error);
      toast.error("حدث خطأ أثناء تنزيل البيانات الفعلية من قاعدة البيانات");
    } finally {
      setIsFullBackupLoading(false);
    }
  };

  return (
    <Button 
      onClick={handleCreateFullBackup} 
      disabled={isFullBackupLoading}
      className="w-full bg-green-600 hover:bg-green-700"
      variant="default"
    >
      <Database className="h-4 w-4 ml-2" />
      {isFullBackupLoading ? "جاري تنزيل البيانات المحدثة..." : "تنزيل البيانات المحدثة من كل الجداول"}
    </Button>
  );
}
