
/**
 * Save backup information to local storage history
 */
export const saveBackupToHistory = (backupData: any, type: string = 'regular') => {
  try {
    // Generate unique ID for this backup
    const backupId = `backup_${Date.now()}`;
    
    // Create filename with current date in format YYYY-MM-DD_HH-MM
    const date = new Date();
    const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}_${String(date.getHours()).padStart(2, '0')}-${String(date.getMinutes()).padStart(2, '0')}`;
    const filename = type === 'full' 
      ? `database_full_backup_${formattedDate}.json` 
      : `cash_database_backup_${formattedDate}.json`;
    
    // Calculate total records
    const totalRecords = Object.values(backupData.tables).reduce((sum: number, table: any) => {
      return sum + (Array.isArray(table) ? table.length : 0);
    }, 0);
    
    // Calculate size (approximate)
    const dataString = JSON.stringify(backupData);
    const sizeInKb = Math.round(dataString.length / 1024);
    
    // Create backup record
    const backupRecord = {
      id: backupId,
      filename,
      createdAt: new Date().toISOString(),
      tables: Object.keys(backupData.tables),
      recordCount: totalRecords,
      size: `${sizeInKb} KB`,
      type: type,
    };
    
    // Save backup data to localStorage
    localStorage.setItem(`backup_${backupId}`, dataString);
    
    // Update history in localStorage
    const historyData = localStorage.getItem('database_backup_history');
    const history = historyData ? JSON.parse(historyData) : [];
    history.unshift(backupRecord);
    
    // Limit history to 20 items
    const limitedHistory = history.slice(0, 20);
    localStorage.setItem('database_backup_history', JSON.stringify(limitedHistory));
    
  } catch (error) {
    console.error("Error saving backup to history:", error);
  }
};
