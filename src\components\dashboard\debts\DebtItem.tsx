
import { Calendar, Clock, AlertCircle } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import type { Debt } from "@/hooks/useOutstandingDebts";

interface DebtItemProps {
  debt: Debt;
  formatCurrency: (amount: number) => string;
  onClick?: () => void;
}

export function DebtItem({ debt, formatCurrency, onClick }: DebtItemProps) {
  const isMobile = useIsMobile();
  
  // إضافة تصنيف للعنصر بناءً على حالة الدين (مستحق أو متأخر)
  const getItemClassName = (debt: Debt) => {
    return debt.status === 'overdue' 
      ? 'hover:bg-red-50/80 dark:hover:bg-red-900/20 border-r-4 border-red-500' 
      : 'hover:bg-red-50/50 dark:hover:bg-red-900/10';
  };

  return (
    <div
      className={`card-item ${getItemClassName(debt)} px-3 sm:px-4 py-2 sm:py-3 ${isMobile ? 'border-b border-border/30' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 sm:gap-3">
          <div className={`p-1.5 sm:p-2 rounded-full ${debt.status === 'overdue' ? 'bg-red-100 dark:bg-red-900/40' : 'bg-red-100 dark:bg-red-900/30'}`}>
            {debt.status === 'overdue' ? (
              <Clock className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-red-600 dark:text-red-400`} />
            ) : (
              <AlertCircle className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-red-500 dark:text-red-400`} />
            )}
          </div>
          <div>
            <p className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>{debt.customer_name}</p>
            <div className={`flex items-center gap-1.5 ${isMobile ? 'mt-0.5' : 'mt-1'}`}>
              {debt.due_date && (
                <div className={`flex items-center ${isMobile ? 'text-[10px]' : 'text-xs'} ${debt.status === 'overdue' ? 'text-red-600 dark:text-red-400' : 'text-muted-foreground'}`}>
                  <Calendar className={`${isMobile ? 'h-2.5 w-2.5' : 'h-3 w-3'} mr-1`} />
                  {new Date(debt.due_date).toLocaleDateString('ar-EG')}
                </div>
              )}
              {debt.days_overdue && (
                <div className={`flex items-center ${isMobile ? 'text-[10px]' : ''} text-red-600 dark:text-red-400 font-medium`}>
                  <Clock className={`${isMobile ? 'h-2.5 w-2.5' : 'h-3 w-3'} mr-1`} />
                  متأخر {debt.days_overdue} أيام
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="text-left">
          <span className={`${debt.status === 'overdue' ? 'text-red-600 dark:text-red-400' : 'debt-amount'} ${isMobile ? 'text-xs' : ''} font-medium`}>
            {formatCurrency(debt.amount)} ج.م
          </span>
        </div>
      </div>
    </div>
  );
}
