import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Dashboard from "./pages/Dashboard";
import Transactions from "./pages/Transactions";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";
import Reports from "./pages/Reports";
import Customers from "./pages/Customers";
import Debts from "./pages/Debts";
import Wallets from "./pages/Wallets";
import Users from "./pages/Users";
import Auth from "./pages/Auth";
import DatabaseTools from "./pages/DatabaseTools";
import Support from "./pages/Support";
import { Sidebar } from "./components/layout/Sidebar";
import { Header } from "./components/layout/Header";
import { useEffect } from "react";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import { useAuth } from "./contexts/AuthContext";
import { NotificationLogs } from "./components/settings/notifications/NotificationLogs";
const queryClient = new QueryClient();

// Component to handle layout for authenticated pages
const AuthenticatedLayout = ({
  children,
  requiredPermission
}: {
  children: React.ReactNode;
  requiredPermission?: string;
}) => {
  const {
    user
  } = useAuth();
  if (!user) return null;
  return <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex flex-col flex-1 overflow-hidden">
        <Header />
        <main className="flex-1 overflow-y-auto my-0 py-[19px] px-[27px]">
          {children}
        </main>
      </div>
    </div>;
};

// Simple layout for public pages (like Support)
const PublicLayout = ({
  children
}: {
  children: React.ReactNode;
}) => {
  return <div className="min-h-screen bg-background">
      <main className="container mx-auto py-8">
        {children}
      </main>
    </div>;
};
const App = () => {
  // Set RTL direction for the whole application
  useEffect(() => {
    document.documentElement.setAttribute("dir", "rtl");
    return () => {
      document.documentElement.removeAttribute("dir");
    };
  }, []);
  return <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <AuthProvider>
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/auth" element={<Auth />} />
              
              {/* Public support page - accessible to everyone */}
              <Route path="/support" element={<PublicLayout>
                  <Support />
                </PublicLayout>} />
              
              {/* Protected routes with layout */}
              <Route path="/" element={<ProtectedRoute requiredPermission="dashboard">
                  <AuthenticatedLayout requiredPermission="dashboard">
                    <Dashboard />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              <Route path="/transactions" element={<ProtectedRoute requiredPermission="transactions">
                  <AuthenticatedLayout requiredPermission="transactions">
                    <Transactions />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              <Route path="/reports" element={<ProtectedRoute requiredPermission="reports">
                  <AuthenticatedLayout requiredPermission="reports">
                    <Reports />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              <Route path="/settings" element={<ProtectedRoute requiredPermission="settings">
                  <AuthenticatedLayout requiredPermission="settings">
                    <Settings />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              <Route path="/database-tools" element={<ProtectedRoute requiredPermission="database_tools">
                  <AuthenticatedLayout requiredPermission="database_tools">
                    <DatabaseTools />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              <Route path="/notification-logs" element={<ProtectedRoute requiredPermission="notification_logs">
                  <AuthenticatedLayout requiredPermission="notification_logs">
                    <NotificationLogs />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              <Route path="/customers" element={<ProtectedRoute requiredPermission="customers">
                  <AuthenticatedLayout requiredPermission="customers">
                    <Customers />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              <Route path="/debts" element={<ProtectedRoute requiredPermission="debts">
                  <AuthenticatedLayout requiredPermission="debts">
                    <Debts />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              <Route path="/wallets" element={<ProtectedRoute requiredPermission="wallets">
                  <AuthenticatedLayout requiredPermission="wallets">
                    <Wallets />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              <Route path="/users" element={<ProtectedRoute requiredPermission="users">
                  <AuthenticatedLayout requiredPermission="users">
                    <Users />
                  </AuthenticatedLayout>
                </ProtectedRoute>} />
              
              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </AuthProvider>
      </TooltipProvider>
    </QueryClientProvider>;
};
export default App;