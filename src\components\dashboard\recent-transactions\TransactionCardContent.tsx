
import { CardContent } from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";
import { ProcessedTransaction } from "@/types/transaction.types";
import { TransactionSkeleton } from "./TransactionSkeleton";
import { EmptyTransactions } from "./EmptyTransactions";
import { TransactionList } from "./TransactionList";

interface TransactionCardContentProps {
  transactions: ProcessedTransaction[];
  isLoading: boolean;
  formatDate: (dateStr: string) => string;
}

export function TransactionCardContent({ transactions, isLoading, formatDate }: TransactionCardContentProps) {
  const isMobile = useIsMobile();
  
  return (
    <CardContent className={`p-0 max-h-[320px] sm:max-h-[360px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 dark:scrollbar-thumb-gray-700 ${isMobile ? 'max-h-[250px]' : ''}`}>
      {isLoading ? (
        <TransactionSkeleton />
      ) : transactions.length === 0 ? (
        <EmptyTransactions />
      ) : (
        <TransactionList 
          transactions={transactions} 
          formatDate={formatDate} 
        />
      )}
    </CardContent>
  );
}
