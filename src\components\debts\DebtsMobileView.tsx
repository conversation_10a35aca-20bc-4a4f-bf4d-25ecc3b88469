
import { Debt } from "@/types/debt.types";
import { TableCard, TableCardRow } from "@/components/ui/table";
import { DebtStatusBadge } from "./DebtStatusBadge";
import { DebtActions } from "./DebtActions";
import { Loader2 } from "lucide-react";

interface DebtsMobileViewProps {
  debts: Debt[];
  loading: boolean;
  handleEditClick: (debt: Debt) => void;
  handleViewClick: (debt: Debt) => void;
  handleDeleteClick: (debtId: string) => void;
  handleMarkAsPaid: (debtId: string) => void;
  getWalletName: (transactionId: string | null) => string;
  formatCurrency: (amount: number) => string;
  formatDate: (date: string | null) => string;
}

export function DebtsMobileView({
  debts,
  loading,
  handleEditClick,
  handleViewClick,
  handleDeleteClick,
  handleMarkAsPaid,
  getWalletName,
  formatCurrency,
  formatDate
}: DebtsMobileViewProps) {
  const renderActions = (debt: Debt) => (
    <div className="flex justify-center mt-3 pb-1">
      <DebtActions
        debt={debt}
        onEdit={handleEditClick}
        onView={handleViewClick}
        onDelete={handleDeleteClick}
        onMarkPaid={handleMarkAsPaid}
      />
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="mr-3">جاري تحميل البيانات...</span>
      </div>
    );
  }

  if (debts.length === 0) {
    return (
      <div className="text-center py-10 bg-muted/20 rounded-lg border border-border mt-4">
        <p className="text-muted-foreground">لا يوجد ديون مسجلة</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {debts.map((debt, index) => (
        <TableCard
          key={debt.id}
          title={
            <div className="w-full">
              <div className="font-medium">{debt.customer?.name || "غير محدد"}</div>
              {debt.customer?.phone && (
                <div className="text-sm text-muted-foreground mt-1">{debt.customer?.phone}</div>
              )}
            </div>
          }
          className="animate-fade-in mobile-card debt-card-hover transition-all duration-300"
          style={{ animationDelay: `${index * 50}ms` }}
          actions={null}
        >
          <TableCardRow label="المبلغ" value={formatCurrency(Number(debt.amount))} />
          <TableCardRow label="المحفظة" value={getWalletName(debt.transaction_id)} />
          <TableCardRow label="تاريخ الاستحقاق" value={formatDate(debt.due_date)} />
          <TableCardRow 
            label="الحالة" 
            value={<DebtStatusBadge debt={debt} />} 
          />
          {debt.notes && (
            <TableCardRow label="ملاحظات" value={debt.notes} />
          )}
          {/* إضافة الأزرار في نهاية الكارد */}
          {renderActions(debt)}
        </TableCard>
      ))}
    </div>
  );
}
