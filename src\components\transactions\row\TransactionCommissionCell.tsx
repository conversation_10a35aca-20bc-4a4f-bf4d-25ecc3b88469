
import { TableCell } from "@/components/ui/table";
import { formatCurrency } from "@/utils/formatters";

interface TransactionCommissionCellProps {
  commission: number;
}

export function TransactionCommissionCell({ commission }: TransactionCommissionCellProps) {
  return (
    <TableCell className="rtl-cell">
      <div className="cell-content">
        {commission > 0 ? (
          <span className="text-green-600 dark:text-green-400 font-medium">
            {formatCurrency(commission)}
          </span>
        ) : (
          <span className="text-gray-500">—</span>
        )}
      </div>
    </TableCell>
  );
}
