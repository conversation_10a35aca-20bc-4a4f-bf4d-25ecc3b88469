
import { Branch } from "@/services/branchService";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Edit, Trash, ToggleLeft, ToggleRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface BranchTableProps {
  branches: Branch[];
  isLoading: boolean;
  onEdit: (branch: Branch) => void;
  onDelete: (branch: Branch) => void;
  onStatusToggle: () => void;
}

export function BranchTable({
  branches,
  isLoading,
  onEdit,
  onDelete,
  onStatusToggle
}: BranchTableProps) {
  const toggleStatus = async (id: string, currentStatus: boolean) => {
    // توقع أن هناك وظيفة لتبديل حالة الفرع
    try {
      const { error } = await supabase
        .from("branches")
        .update({ active_status: !currentStatus })
        .eq("id", id);

      if (error) throw error;
      onStatusToggle(); // تحديث البيانات بعد التغيير
    } catch (error) {
      console.error("Error toggling branch status:", error);
    }
  };

  // تعريف أعمدة جدول الفروع
  const columns: TableColumn<Branch>[] = [
    {
      header: "اسم الفرع",
      accessor: (branch) => branch.name,
      className: "font-medium"
    },
    {
      header: "العنوان",
      accessor: (branch) => branch.address
    },
    {
      header: "المدير",
      accessor: (branch) => branch.manager
    },
    {
      header: "الحالة",
      accessor: (branch) => (
        <Badge variant={branch.active_status ? "success" : "outline"}>
          {branch.active_status ? "نشط" : "غير نشط"}
        </Badge>
      )
    },
    {
      header: "تفعيل/تعطيل",
      accessor: (branch) => (
        <Button 
          onClick={() => toggleStatus(branch.id, branch.active_status)}
          variant={branch.active_status ? "destructive" : "success"}
          size="sm"
          className="min-w-[80px] gap-1 font-medium text-xs"
          style={{
            backgroundColor: branch.active_status ? '#10b981' : '#ef4444',
            color: 'white',
            border: 'none'
          }}
        >
          {branch.active_status ? (
            <>
              <ToggleRight className="h-4 w-4 ml-1" />
              تعطيل
            </>
          ) : (
            <>
              <ToggleLeft className="h-4 w-4 ml-1" />
              تفعيل
            </>
          )}
        </Button>
      )
    },
    {
      header: "الإجراءات",
      accessor: (branch) => (
        <div className="rtl-actions-group">
          <Button
            onClick={() => onEdit(branch)}
            variant="outline"
            size="sm"
            className="border-blue-300/40 hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 min-w-[70px] gap-1 text-xs text-blue-600"
          >
            <Edit className="h-3.5 w-3.5 ml-1" />
            تعديل
          </Button>
          <Button
            onClick={() => onDelete(branch)}
            variant="outline"
            size="sm"
            className="border-[#F0112A]/40 text-red-600 hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-900/30 dark:hover:text-red-300 min-w-[70px] gap-1 text-xs border-[#F0112A]"
          >
            <Trash className="h-3.5 w-3.5 ml-1" />
            حذف
          </Button>
        </div>
      )
    }
  ];

  return (
    <ModernTable
      data={branches}
      columns={columns}
      loading={isLoading}
      keyField="id"
      emptyMessage="لا توجد فروع مضافة حتى الآن"
      dir="rtl"
      mobileCardTitle={(branch) => branch.name}
      mobileCardActions={(branch) => (
        <div className="rtl-actions-group-mobile">
          <Button
            onClick={() => onEdit(branch)}
            variant="outline"
            size="sm"
            className="border-blue-300/40 hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 min-w-[70px] gap-1 text-xs text-blue-600"
          >
            <Edit className="h-3.5 w-3.5 ml-1" />
            تعديل
          </Button>
          <Button
            onClick={() => onDelete(branch)}
            variant="outline"
            size="sm"
            className="border-[#F0112A]/40 text-red-600 hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-900/30 dark:hover:text-red-300 min-w-[70px] gap-1 text-xs border-[#F0112A]"
          >
            <Trash className="h-3.5 w-3.5 ml-1" />
            حذف
          </Button>
        </div>
      )}
    />
  );
}
