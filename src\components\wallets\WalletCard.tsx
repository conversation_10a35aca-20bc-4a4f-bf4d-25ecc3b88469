import { useState } from "react";
import { Trash2, <PERSON><PERSON>ard, Eye, Edit, Percent, LifeBuoy } from "lucide-react";
import { Button } from "@/components/ui/button";
import { WalletCommissionDialog } from "./WalletCommissionDialog";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
interface WalletCardProps {
  name: string;
  balance: number;
  color: string;
  id: string;
  onDelete: (id: string) => void;
}
const WalletCard = ({
  name,
  balance,
  color,
  id,
  onDelete
}: WalletCardProps) => {
  const [isHovering, setIsHovering] = useState(false);
  const [isCommissionDialogOpen, setIsCommissionDialogOpen] = useState(false);
  const [isGlowActive, setIsGlowActive] = useState(false);
  const navigate = useNavigate();

  // تحديد لون الخلفية والحدود بناءً على اللون المعطى
  const getCardStyle = () => {
    const baseClasses = "border rounded-lg p-5 shadow-sm transition-all duration-300 hover:shadow-lg wallet-card theme-transition theme-card rtl-card";
    switch (color) {
      case "wallet-card-blue":
        return `${baseClasses} bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-900/90 border-r-4 border-r-blue-500`;
      case "wallet-card-red":
        return `${baseClasses} bg-gradient-to-br from-red-50 to-white dark:from-red-900/20 dark:to-gray-900/90 border-r-4 border-r-red-500`;
      case "wallet-card-green":
        return `${baseClasses} bg-gradient-to-br from-green-50 to-white dark:from-green-900/20 dark:to-gray-900/90 border-r-4 border-r-green-500`;
      case "wallet-card-amber":
        return `${baseClasses} bg-gradient-to-br from-amber-50 to-white dark:from-amber-900/20 dark:to-gray-900/90 border-r-4 border-r-amber-500`;
      default:
        return `${baseClasses} bg-gradient-to-br from-gray-50 to-white dark:from-gray-800/20 dark:to-gray-900/90 border-r-4 border-r-primary`;
    }
  };

  // تحديد لون الأيقونة بناءً على اللون المعطى
  const getIconColor = () => {
    switch (color) {
      case "wallet-card-blue":
        return "text-blue-500";
      case "wallet-card-red":
        return "text-red-500";
      case "wallet-card-green":
        return "text-green-500";
      case "wallet-card-amber":
        return "text-amber-500";
      default:
        return "text-primary";
    }
  };

  // تحديد لون التوهج بناءً على اللون المعطى
  const getGlowColor = () => {
    switch (color) {
      case "wallet-card-blue":
        return "blue-glow";
      case "wallet-card-red":
        return "red-glow";
      case "wallet-card-green":
        return "green-glow";
      case "wallet-card-amber":
        return "amber-glow";
      default:
        return "golden-glow";
      // للذهبي الافتراضي
    }
  };
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation(); // منع انتشار الحدث إلى العناصر الأم
    e.preventDefault(); // منع السلوك الافتراضي
    onDelete(id);
  };

  // تفعيل أزرار التفاصيل والعمولة والدعم
  const handleViewDetails = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    toast.info(`عرض تفاصيل المحفظة: ${name}`, {
      description: `الرصيد الحالي: ${balance.toLocaleString()} جنيه`
    });
  };
  const handleOpenCommission = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsCommissionDialogOpen(true);
  };
  const handleOpenSupport = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    navigate("/support");
  };

  // إضافة وظيفة زر التعديل
  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    toast.info(`تعديل المحفظة: ${name}`, {
      description: "يمكنك تعديل بيانات المحفظة هنا"
    });
  };

  // تأثير عند النقر على الزر للتغذية البصرية - تم إصلاح المشكلة هنا
  const handleButtonClick = (action: (e: React.MouseEvent) => void) => (e: React.MouseEvent) => {
    // إزالة محاولة الوصول إلى classList لأنها تسبب خطأ
    // بدلاً من ذلك، نقوم بتنفيذ الإجراء مباشرة
    setTimeout(() => {
      action(e);
    }, 100);
  };
  return <>
      <div className={`${getCardStyle()} ${isHovering ? `${getGlowColor()}` : ''}`} onMouseEnter={() => setIsHovering(true)} onMouseLeave={() => setIsHovering(false)}>
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <div className={`p-2 rounded-full bg-background/80 shadow-sm ${getIconColor()}`}>
              <CreditCard className="h-5 w-5" />
            </div>
            <h2 className="text-xl font-semibold mb-2">{name}</h2>
          </div>
          <div className="flex gap-1">
            
            <Button variant="ghost" size="icon-sm" className="rtl-icon-button h-8 w-8 text-muted-foreground hover:text-destructive hover:bg-accent/50 dark:text-muted-foreground dark:hover:text-destructive transition-colors duration-200 active:scale-95" onClick={handleDelete} title="حذف">
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="mt-6 text-2xl font-bold text-primary flex items-baseline gap-1">
          {balance.toLocaleString()} 
          <span className="text-sm font-medium text-muted-foreground">جنيه</span>
        </div>
        
        <div className="mt-6 flex justify-between items-center">
          <div className="flex gap-2">
            
            
            <Button variant="link" className="text-primary hover:text-primary/80 hover:bg-accent/30 theme-button-link flex items-center gap-1 p-0 px-1 dark:hover:text-primary-foreground rtl-button transition-all duration-200 active:translate-x-0" onClick={handleOpenCommission}>
              <Percent className="h-4 w-4 ml-1" />
              العمولة
            </Button>
            
            <Button variant="link" className="text-primary hover:text-primary/80 hover:bg-accent/30 theme-button-link flex items-center gap-1 p-0 px-1 dark:hover:text-primary-foreground rtl-button transition-all duration-200 active:translate-x-0" onClick={handleOpenSupport}>
              <LifeBuoy className="h-4 w-4 ml-1" />
              الدعم
            </Button>
          </div>
        </div>
      </div>
      
      <WalletCommissionDialog isOpen={isCommissionDialogOpen} onOpenChange={setIsCommissionDialogOpen} walletId={id} walletName={name} />
    </>;
};
export default WalletCard;