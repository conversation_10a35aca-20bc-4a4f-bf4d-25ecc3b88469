
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Wallet } from "./utils/operationSchema";

const editWalletSchema = z.object({
  name: z.string().min(1, "اسم المحفظة مطلوب"),
  phone_number: z.string().min(1, "رقم الهاتف مطلوب"),
  balance: z.coerce.number().optional(),
});

type EditWalletFormValues = z.infer<typeof editWalletSchema>;

interface EditFawryWalletDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  wallet: Wallet;
  onWalletUpdated: () => void;
}

const EditFawryWalletDialog: React.FC<EditFawryWalletDialogProps> = ({
  isOpen,
  onOpenChange,
  wallet,
  onWalletUpdated,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<EditWalletFormValues>({
    resolver: zodResolver(editWalletSchema),
    defaultValues: {
      name: wallet.name,
      phone_number: wallet.phone_number,
      balance: wallet.balance,
    },
  });

  const onSubmit = async (values: EditWalletFormValues) => {
    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from("fawry_wallets")
        .update({
          name: values.name,
          phone_number: values.phone_number,
          balance: values.balance || 0,
        })
        .eq("id", wallet.id);

      if (error) {
        throw error;
      }

      toast({
        title: "تم التحديث بنجاح",
        description: `تم تحديث محفظة ${values.name} بنجاح`,
      });

      onOpenChange(false);
      onWalletUpdated();
    } catch (error) {
      console.error("Error updating wallet:", error);
      toast({
        variant: "destructive",
        title: "خطأ في التحديث",
        description: "حدث خطأ أثناء تحديث المحفظة. الرجاء المحاولة مرة أخرى.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>تعديل محفظة فوري</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 pt-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>اسم المحفظة</FormLabel>
                  <FormControl>
                    <Input placeholder="أدخل اسم المحفظة" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>رقم الهاتف</FormLabel>
                  <FormControl>
                    <Input placeholder="رقم الهاتف" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="balance"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>رصيد المحفظة</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value === "" ? undefined : e.target.value;
                        field.onChange(value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button variant="outline" type="button" onClick={() => onOpenChange(false)}>
                إلغاء
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "جاري الحفظ..." : "حفظ التغييرات"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditFawryWalletDialog;
