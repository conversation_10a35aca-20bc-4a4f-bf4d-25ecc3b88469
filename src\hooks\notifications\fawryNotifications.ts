
import { supabase } from "@/integrations/supabase/client";
import { FawryWalletOperation } from './types';
import { formatFawryOperationNotification, formatLowBalanceFawryNotification } from './formatNotificationMessages';
import { logNotification } from './notificationSettings';

/**
 * إرسال إشعار عملية محفظة فوري إلى تليجرام
 */
export const sendFawryOperationNotification = async (
  botToken: string,
  chatId: string,
  operation: FawryWalletOperation
): Promise<boolean> => {
  try {
    console.log(`📤 Sending Fawry operation notification to ${chatId}`);
    
    // تنسيق معرف الدردشة - إزالة @ إذا كان موجودًا
    const formattedChatId = chatId.startsWith('@') 
      ? chatId.substring(1) 
      : chatId;
    
    // تنسيق وإرسال الإشعار
    const message = formatFawryOperationNotification(operation);
    
    console.log('Formatted message:', message.substring(0, 100) + '...');
    
    const { data, error } = await supabase.functions.invoke('sendTelegramMessage', {
      body: {
        botToken,
        chatId: formattedChatId,
        message
      }
    });
    
    if (error) {
      console.error('Error from sendTelegramMessage edge function:', error);
      throw error;
    }
    
    console.log('Edge function response:', data);
    
    // تسجيل الإشعار
    const shortMessage = `إشعار محفظة فوري: ${operation.operation_type} - ${operation.amount} ج.م`;
    await logNotification(chatId, shortMessage, 'sent');
    
    return true;
  } catch (error) {
    console.error('Error sending fawry operation notification:', error);
    
    // تسجيل فشل الإشعار
    await logNotification(chatId, 'فشل في إرسال إشعار عملية محفظة فوري', 'failed');
    
    return false;
  }
};

/**
 * إرسال إشعار انخفاض رصيد محفظة فوري إلى تليجرام
 */
export const sendLowBalanceFawryNotification = async (
  botToken: string,
  chatId: string,
  wallet: { name: string; balance: number },
  minBalance: number
): Promise<boolean> => {
  try {
    console.log(`📤 Sending low balance Fawry wallet notification to ${chatId} for wallet ${wallet.name}`);
    
    // تنسيق معرف الدردشة - إزالة @ إذا كان موجودًا
    const formattedChatId = chatId.startsWith('@') 
      ? chatId.substring(1) 
      : chatId;
    
    // تنسيق وإرسال الإشعار
    const message = formatLowBalanceFawryNotification(wallet, minBalance);
    
    const { data, error } = await supabase.functions.invoke('sendTelegramMessage', {
      body: {
        botToken,
        chatId: formattedChatId,
        message
      }
    });
    
    if (error) {
      console.error('Error from sendTelegramMessage edge function:', error);
      throw error;
    }
    
    console.log('Edge function response:', data);
    
    // تسجيل الإشعار
    const shortMessage = `تنبيه انخفاض رصيد لمحفظة ${wallet.name}`;
    await logNotification(chatId, shortMessage, 'sent');
    
    return true;
  } catch (error) {
    console.error('Error sending low balance fawry wallet notification:', error);
    
    // تسجيل فشل الإشعار
    await logNotification(chatId, 'فشل في إرسال إشعار انخفاض رصيد محفظة فوري', 'failed');
    
    return false;
  }
};
