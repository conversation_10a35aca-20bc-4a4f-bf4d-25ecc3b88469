import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { formatTestMessage } from "../utils/formatMessage";

export async function sendTestMessage(
  botToken: string, 
  targetId: string
): Promise<boolean> {
  try {
    if (!botToken) {
      toast.error("يرجى إدخال رمز البوت");
      return false;
    }
    
    if (!targetId) {
      toast.error("يرجى إدخال معرف المستخدم للاختبار أو معرف الدردشة");
      return false;
    }

    const testMessageContent = formatTestMessage();

    // Format chat ID properly for the API call
    // If it's a numeric ID, use it as is
    // If it's not numeric and doesn't have @ prefix, add the @ prefix
    let formattedTargetId = targetId;
    if (isNaN(Number(targetId)) && !targetId.startsWith('@')) {
      formattedTargetId = '@' + targetId;
    }

    // Log the test message
    await supabase.from("notification_logs").insert([
      {
        user_id: targetId,
        message: "رسالة اختبارية من نظام Cash",
        type: "telegram",
        status: "pending",
      },
    ]);

    console.log("Sending test message to:", formattedTargetId);
    console.log("Using bot token:", `${botToken.substring(0, 5)}...`);
    console.log("Message content:", testMessageContent);

    // Use an edge function to send the message to avoid exposing the token
    const { data, error } = await supabase.functions.invoke('sendTelegramMessage', {
      body: {
        botToken,
        chatId: formattedTargetId, 
        message: testMessageContent
      }
    });

    console.log("Function response:", data, error);

    if (error) throw error;

    // Update the status in the log
    if (data && data.success) {
      await supabase
        .from("notification_logs")
        .update({ status: "sent" })
        .eq("user_id", targetId)
        .eq("status", "pending")
        .order('created_at', { ascending: false })
        .limit(1);
      
      toast.success("تم إرسال الرسالة الاختبارية بنجاح");
      return true;
    } else {
      await supabase
        .from("notification_logs")
        .update({ status: "failed" })
        .eq("user_id", targetId)
        .eq("status", "pending")
        .order('created_at', { ascending: false })
        .limit(1);
      
      throw new Error(data?.error || "فشل إرسال الرسالة: خطأ غير معروف");
    }
  } catch (error) {
    console.error("Error sending test message:", error);
    let errorMessage = "فشل في إرسال الرسالة الاختبارية";
    
    if (typeof error === 'object' && error !== null) {
      if (error.message && error.message.includes("chat not found")) {
        errorMessage = "معرف المستخدم غير صحيح أو أن المستخدم لم يبدأ محادثة مع البوت. تأكد من أن المستخدم قد بدأ محادثة مع البوت الخاص بك.";
      } else if (error.message && error.message.includes("bot was blocked")) {
        errorMessage = "تم حظر البوت من قبل المستخدم";
      } else if (error.message) {
        errorMessage = error.message;
      }
    }
    
    toast.error(errorMessage);
    return false;
  }
}
