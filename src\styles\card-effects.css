
/* نظام موحد لتأثيرات البطاقات */

/* التأثيرات الأساسية المشتركة */
.effect-card {
  position: relative;
  border-radius: 0.75rem;
  overflow: visible;
  transform: translateZ(0);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

/* تأثير الحدود المتحركة */
.animated-border {
  position: absolute;
  inset: -2px;
  border-radius: 0.75rem;
  padding: 2px;
  background-size: 400% 400%;
  filter: blur(4px);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
  animation: none;
}

/* تأثير التوهج عند التحويم */
.effect-card:hover .animated-border {
  opacity: 1;
  animation: animateBorder 3s ease infinite;
}

@keyframes animateBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* تأثيرات الزجاج */
.glass-effect::before {
  content: "";
  position: absolute;
  inset: 0;
  z-index: -2;
  border-radius: 0.7rem;
  background: rgba(255, 255, 255, 0.01);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.05);
}

.dark .glass-effect::before {
  background: rgba(20, 20, 30, 0.01);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}

/* تأثير الانعكاس الزجاجي */
.glass-effect::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  z-index: -1;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), transparent);
  backdrop-filter: blur(4px);
  border-top-left-radius: 0.7rem;
  border-top-right-radius: 0.7rem;
}

.dark .glass-effect::after {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.05), transparent);
}

/* تأثيرات التوهج حسب نوع البطاقة */
/* أحمر - الافتراضي */
.effect-red .animated-border {
  background: linear-gradient(
    -45deg, 
    rgba(239, 68, 68, 0), 
    rgba(239, 68, 68, 0.3), 
    rgba(239, 68, 68, 0.6), 
    rgba(239, 68, 68, 0.3), 
    rgba(239, 68, 68, 0)
  );
}

.effect-red:hover {
  box-shadow: 0 5px 25px 5px rgba(239, 68, 68, 0.15);
}

@media (prefers-color-scheme: light) {
  .effect-red:hover {
    box-shadow: 0 5px 25px 8px rgba(239, 68, 68, 0.2);
  }
}

/* أخضر */
.effect-green .animated-border {
  background: linear-gradient(
    -45deg, 
    rgba(34, 197, 94, 0), 
    rgba(34, 197, 94, 0.3), 
    rgba(34, 197, 94, 0.6), 
    rgba(34, 197, 94, 0.3), 
    rgba(34, 197, 94, 0)
  );
}

.effect-green:hover {
  box-shadow: 0 5px 25px 5px rgba(34, 197, 94, 0.2);
}

@media (prefers-color-scheme: light) {
  .effect-green:hover {
    box-shadow: 0 5px 25px 8px rgba(34, 197, 94, 0.2);
  }
}

/* كهرماني */
.effect-amber .animated-border {
  background: linear-gradient(
    -45deg, 
    rgba(245, 158, 11, 0), 
    rgba(245, 158, 11, 0.3), 
    rgba(245, 158, 11, 0.6), 
    rgba(245, 158, 11, 0.3), 
    rgba(245, 158, 11, 0)
  );
}

.effect-amber:hover {
  box-shadow: 0 5px 25px 5px rgba(245, 158, 11, 0.2);
}

@media (prefers-color-scheme: light) {
  .effect-amber:hover {
    box-shadow: 0 5px 25px 8px rgba(245, 158, 11, 0.2);
  }
}

/* أزرق */
.effect-blue .animated-border {
  background: linear-gradient(
    -45deg, 
    rgba(59, 130, 246, 0), 
    rgba(59, 130, 246, 0.3), 
    rgba(59, 130, 246, 0.6), 
    rgba(59, 130, 246, 0.3), 
    rgba(59, 130, 246, 0)
  );
}

.effect-blue:hover {
  box-shadow: 0 5px 25px 5px rgba(59, 130, 246, 0.2);
}

@media (prefers-color-scheme: light) {
  .effect-blue:hover {
    box-shadow: 0 5px 25px 8px rgba(59, 130, 246, 0.2);
  }
}

/* تأثيرات الوضع المظلم */
.dark .effect-red:hover {
  box-shadow: 0 5px 30px 8px rgba(255, 215, 0, 0.25);
}

.dark .effect-green:hover {
  box-shadow: 0 5px 30px 8px rgba(34, 197, 94, 0.25);
}

.dark .effect-amber:hover {
  box-shadow: 0 5px 30px 8px rgba(245, 158, 11, 0.25);
}

.dark .effect-blue:hover {
  box-shadow: 0 5px 30px 8px rgba(59, 130, 246, 0.25);
}

/* تحسينات للهوامش والتباعد على الأجهزة المحمولة */
@media (max-width: 640px) {
  .effect-card {
    margin-bottom: 0.75rem;
  }
  
  .effect-card .card-content {
    padding: 1rem;
  }
  
  .effect-card .card-title {
    font-size: 0.875rem;
  }
  
  .effect-card .card-value {
    font-size: 1.5rem;
  }
  
  .effect-card .card-icon {
    padding: 0.375rem;
  }
}

/* تحسينات للأجهزة الصغيرة جداً */
@media (max-width: 380px) {
  .effect-card .card-content {
    padding: 0.75rem;
  }
  
  .effect-card .card-icon {
    padding: 0.25rem;
  }
  
  .effect-card .card-value {
    font-size: 1.25rem;
  }
}
