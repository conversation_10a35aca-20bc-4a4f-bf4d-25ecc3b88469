
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DialogFooter } from "@/components/ui/dialog";
import { UseFormReturn } from "react-hook-form";
import { OperationFormValues } from "../hooks/useFawryOperationForm";
import WalletBalanceCard from "./WalletBalanceCard";
import OperationTypeSelector from "./OperationTypeSelector";
import { Wallet } from "../utils/operationSchema";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface FawryOperationFormProps {
  form: UseFormReturn<OperationFormValues>;
  wallets: Wallet[];
  selectedWallet: Wallet | null;
  isSubmitting: boolean;
  onClose: () => void;
  onSubmit: (values: OperationFormValues) => void;
}

const FawryOperationForm: React.FC<FawryOperationFormProps> = ({
  form,
  wallets,
  selectedWallet,
  isSubmitting,
  onClose,
  onSubmit,
}) => {
  const [insufficientBalance, setInsufficientBalance] = useState(false);
  
  // إزالة استدعاء useTransactionFormNotifications هنا
  
  // مراقبة التغييرات في نوع العملية والمبلغ والمحفظة المحددة للتحقق من كفاية الرصيد
  useEffect(() => {
    const operationType = form.watch("operation_type");
    const amount = form.watch("amount");
    const walletId = form.watch("wallet_id");

    // تعديل: التحقق فقط إذا كان نوع العملية "إرسال" (سحب من المحفظة)
    if (operationType === "إرسال" && amount && selectedWallet?.balance !== undefined) {
      setInsufficientBalance(amount > selectedWallet.balance);
    } else {
      setInsufficientBalance(false);
    }
  }, [form.watch("operation_type"), form.watch("amount"), form.watch("wallet_id"), selectedWallet]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 pt-4">
        <FormField
          control={form.control}
          name="wallet_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>المحفظة</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر المحفظة" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {wallets.map((wallet) => (
                    <SelectItem key={wallet.id} value={wallet.id}>
                      {wallet.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* عرض رصيد المحفظة المحددة */}
        <WalletBalanceCard wallet={selectedWallet} />

        <FormField
          control={form.control}
          name="operation_type"
          render={() => (
            <OperationTypeSelector form={form} />
          )}
        />

        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>المبلغ (جنيه مصري)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  {...field}
                  onChange={(e) => {
                    const value = e.target.value === "" ? undefined : e.target.value;
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* عرض تنبيه في حالة عدم كفاية الرصيد */}
        {insufficientBalance && (
          <Alert variant="destructive" className="mt-2">
            <AlertDescription className="text-sm">
              رصيد المحفظة غير كافي لإتمام هذه العملية
            </AlertDescription>
          </Alert>
        )}

        <FormField
          control={form.control}
          name="commission"
          render={({ field }) => (
            <FormItem>
              <FormLabel>العمولة (جنيه مصري)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  {...field}
                  onChange={(e) => {
                    const value = e.target.value === "" ? "0" : e.target.value;
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <DialogFooter className="pt-4">
          <Button variant="outline" type="button" onClick={onClose}>
            إلغاء
          </Button>
          <Button 
            type="submit" 
            disabled={isSubmitting || insufficientBalance}
          >
            {isSubmitting ? "جاري الإضافة..." : "إضافة العملية"}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
};

export default FawryOperationForm;
