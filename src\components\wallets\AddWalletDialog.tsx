
import { useState } from "react";
import { 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CreditCard, Plus } from "lucide-react";
import { WalletForm } from "./WalletForm";
import { useAddWallet } from "./hooks/useAddWallet";
import { WalletFormValues } from "./schemas/walletFormSchema";

interface AddWalletDialogProps {
  onWalletAdded?: () => void;
}

export function AddWalletDialog({ onWalletAdded }: AddWalletDialogProps) {
  const [open, setOpen] = useState(false);
  const { form, isLoading, onSubmit } = useAddWallet(onWalletAdded);

  const handleSubmit = async (values: WalletFormValues): Promise<boolean> => {
    const success = await onSubmit(values);
    if (success) {
      setOpen(false);
    }
    return success;
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          <span>إضافة محفظة جديدة</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            <span>إضافة محفظة جديدة</span>
          </DialogTitle>
          <DialogDescription>
            قم بإدخال بيانات المحفظة الجديدة في النموذج أدناه
          </DialogDescription>
        </DialogHeader>
        
        <WalletForm 
          form={form} 
          onSubmit={handleSubmit}
          isLoading={isLoading}
          onCancel={() => setOpen(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
