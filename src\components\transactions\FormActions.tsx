
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { Save, X } from "lucide-react";

interface FormActionsProps {
  onClose: () => void;
  isSubmitting: boolean;
  isCalculating: boolean;
  isDisabled?: boolean;
}

export function FormActions({ 
  onClose, 
  isSubmitting, 
  isCalculating, 
  isDisabled = false 
}: FormActionsProps) {
  const isMobile = useIsMobile();
  
  if (isMobile) {
    return (
      <div className="flex flex-col gap-3 mt-6">
        <Button 
          type="submit" 
          disabled={isSubmitting || isCalculating || isDisabled}
          size="mobile"
          className={`order-1 rounded-full shadow-md active:shadow-sm rtl-action-button theme-transition ${
            isDisabled 
              ? 'bg-gray-400 text-gray-700 cursor-not-allowed' 
              : 'bg-red-600 text-white dark:bg-yellow-500 dark:text-black'
          }`}
        >
          <Save className="h-4 w-4 ml-1" />
          {isSubmitting 
            ? "جاري الإضافة..." 
            : isDisabled 
              ? "يرجى إكمال الحقول المطلوبة" 
              : "إضافة العملية"
          }
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
          disabled={isSubmitting}
          size="mobile-sm"
          className="order-2 rounded-full hover:bg-muted rtl-action-button rtl-action-button-destructive theme-transition"
        >
          <X className="h-4 w-4 ml-1" />
          إلغاء
        </Button>
      </div>
    );
  }
  
  return (
    <div className="flex justify-end gap-3 rtl-actions-group">
      <Button
        type="button"
        variant="outline"
        onClick={onClose}
        disabled={isSubmitting}
        className="min-w-[100px] rounded-full rtl-action-button rtl-action-button-destructive theme-transition"
      >
        <X className="h-4 w-4 ml-1" />
        إلغاء
      </Button>
      <Button 
        type="submit" 
        disabled={isSubmitting || isCalculating || isDisabled}
        className={`min-w-[120px] rounded-full shadow-md theme-transition ${
          isDisabled 
            ? 'bg-gray-400 text-gray-700 cursor-not-allowed' 
            : 'bg-red-600 text-white dark:bg-yellow-500 dark:text-black'
        }`}
      >
        <Save className="h-4 w-4 ml-1" />
        {isSubmitting 
          ? "جاري الإضافة..." 
          : isDisabled 
            ? "يرجى إكمال الحقول المطلوبة" 
            : "إضافة العملية"
        }
      </Button>
    </div>
  );
}
