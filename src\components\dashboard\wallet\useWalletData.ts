
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { WalletData, WalletTheme } from "./types";

export function useWalletData() {
  const [wallets, setWallets] = useState<WalletData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchWalletData = async () => {
    try {
      setIsLoading(true);

      // Fetch wallets
      const {
        data: walletsData,
        error: walletsError
      } = await supabase.from('wallets').select('*');
      if (walletsError) throw walletsError;

      // Fetch sims to calculate balances
      const {
        data: simsData,
        error: simsError
      } = await supabase.from('sims').select('*');
      if (simsError) throw simsError;
      
      // Fetch transactions to calculate sent/received amounts
      const {
        data: transactionsData,
        error: transactionsError
      } = await supabase.from('transactions').select('*');
      if (transactionsError) throw transactionsError;

      // Process wallet data
      const processedWallets = walletsData.map((wallet, index) => {
        // Filter sims for this wallet
        const walletSims = simsData.filter(sim => sim.wallet_id === wallet.id);
        const totalBalance = walletSims.reduce((sum, sim) => sum + (sim.balance || 0), 0);
        const activeSims = walletSims.filter(sim => sim.active_status).length;
        const inactiveSims = walletSims.length - activeSims;
        
        // Calculate total sent and received amounts for this wallet
        // Get all transactions for sims in this wallet
        const simIds = walletSims.map(sim => sim.id);
        const walletTransactions = transactionsData.filter(tx => simIds.includes(tx.sim_id));
        
        const totalSent = walletTransactions
          .filter(tx => tx.transaction_type === 'send')
          .reduce((sum, tx) => sum + Number(tx.amount), 0);
          
        const totalReceived = walletTransactions
          .filter(tx => tx.transaction_type === 'receive')
          .reduce((sum, tx) => sum + Number(tx.amount), 0);

        // Get appropriate theme for this wallet
        const themeIndex = getWalletThemeIndex(wallet, index);

        return {
          id: wallet.id,
          name: wallet.name,
          balance: totalBalance,
          simCount: walletSims.length,
          activeSimCount: activeSims,
          inactiveSimCount: inactiveSims,
          totalSent,
          totalReceived,
          ...getWalletThemes()[themeIndex]
        };
      });
      setWallets(processedWallets);
    } catch (error) {
      console.error("Error fetching wallet data:", error);
      toast.error("حدث خطأ أثناء جلب بيانات المحافظ", {
        description: error instanceof Error ? error.message : "خطأ غير معروف"
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchWalletData();
  };

  useEffect(() => {
    fetchWalletData();
  }, []);

  return {
    wallets,
    isLoading,
    isRefreshing,
    handleRefresh
  };
}

// Helper functions
function getWalletThemes(): WalletTheme[] {
  return [
    {
      color: "border-blue-500",
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
      borderColor: "border-l-blue-500",
      iconColor: "text-blue-500"
    },
    {
      color: "border-red-500",
      bgColor: "bg-red-50 dark:bg-red-900/20",
      borderColor: "border-l-red-500",
      iconColor: "text-red-500"
    },
    {
      color: "border-green-500",
      bgColor: "bg-green-50 dark:bg-green-900/20",
      borderColor: "border-l-green-500",
      iconColor: "text-green-500"
    },
    {
      color: "border-amber-500",
      bgColor: "bg-amber-50 dark:bg-amber-900/20",
      borderColor: "border-l-amber-500",
      iconColor: "text-amber-500"
    }
  ];
}

function getWalletThemeIndex(wallet: any, index: number): number {
  const themes = getWalletThemes();
  let themeIndex = index % themes.length;
  
  if (wallet.color_class) {
    if (wallet.color_class.includes('red')) {
      themeIndex = 1;
    } else if (wallet.color_class.includes('green')) {
      themeIndex = 2;
    } else if (wallet.color_class.includes('orange') || wallet.color_class.includes('amber')) {
      themeIndex = 3;
    } else if (wallet.color_class.includes('blue')) {
      themeIndex = 0;
    }
  } else if (wallet.name.includes('فودافون')) {
    themeIndex = 1;
  } else if (wallet.name.includes('اتصالات')) {
    themeIndex = 2;
  } else if (wallet.name.includes('أورانج')) {
    themeIndex = 3;
  } else if (wallet.name.includes('انستا')) {
    themeIndex = 0;
  }
  
  return themeIndex;
}
