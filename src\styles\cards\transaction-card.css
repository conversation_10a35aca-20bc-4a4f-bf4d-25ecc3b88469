
/* تنسيقات لبطاقات المعاملات */
.transaction-stat-card {
  position: relative;
  border-radius: 0.75rem;
  overflow: visible;
  transform: translateZ(0);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.card-border-animation {
  position: absolute;
  inset: -3px;
  border-radius: 0.85rem;
  padding: 2px;
  background: linear-gradient(
    -45deg, 
    rgba(239, 68, 68, 0), 
    rgba(239, 68, 68, 0.4), 
    rgba(239, 68, 68, 0.7), 
    rgba(239, 68, 68, 0.4), 
    rgba(239, 68, 68, 0)
  );
  background-size: 400% 400%;
  filter: blur(5px);
  z-index: -1;
  animation: animateCardBorder 3s ease infinite;
}

.dark .card-border-animation {
  background: linear-gradient(
    -45deg, 
    rgba(255, 215, 0, 0), 
    rgba(255, 215, 0, 0.2), 
    rgba(255, 215, 0, 0.4), 
    rgba(255, 215, 0, 0.2), 
    rgba(255, 215, 0, 0)
  );
}

@keyframes animateCardBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.hover-glow-effect {
  box-shadow: 0 5px 25px 5px rgba(239, 68, 68, 0.15);
  transform: translateY(-2px);
}

.dark .hover-glow-effect {
  box-shadow: 0 5px 30px 8px rgba(255, 215, 0, 0.25);
}

/* تحسين وضوح التأثير في الوضع الفاتح */
@media (prefers-color-scheme: light) {
  .hover-glow-effect {
    box-shadow: 0 5px 25px 8px rgba(239, 68, 68, 0.2);
  }
}

/* ألوان متنوعة للبطاقات المختلفة */
.card-type-send .card-border-animation {
  background: linear-gradient(
    -45deg, 
    rgba(239, 68, 68, 0), 
    rgba(239, 68, 68, 0.4), 
    rgba(239, 68, 68, 0.7), 
    rgba(239, 68, 68, 0.4), 
    rgba(239, 68, 68, 0)
  );
}

.card-type-receive .card-border-animation {
  background: linear-gradient(
    -45deg, 
    rgba(34, 197, 94, 0), 
    rgba(34, 197, 94, 0.4), 
    rgba(34, 197, 94, 0.7), 
    rgba(34, 197, 94, 0.4), 
    rgba(34, 197, 94, 0)
  );
}

.card-type-commission .card-border-animation {
  background: linear-gradient(
    -45deg, 
    rgba(245, 158, 11, 0), 
    rgba(245, 158, 11, 0.4), 
    rgba(245, 158, 11, 0.7), 
    rgba(245, 158, 11, 0.4), 
    rgba(245, 158, 11, 0)
  );
}

/* تحسين تأثير الزجاج والعمق */
.transaction-stat-card::before {
  content: "";
  position: absolute;
  inset: 0;
  z-index: -2;
  border-radius: 0.7rem;
  background: rgba(255, 255, 255, 0.01);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.05);
}

.dark .transaction-stat-card::before {
  background: rgba(20, 20, 30, 0.01);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}

/* تأثير الانعكاس الزجاجي */
.transaction-stat-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  z-index: -1;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), transparent);
  backdrop-filter: blur(4px);
  border-top-left-radius: 0.7rem;
  border-top-right-radius: 0.7rem;
}

.dark .transaction-stat-card::after {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.05), transparent);
}

/* تأثير التوهج المحسن لكل نوع */
.card-type-send.hover-glow-effect {
  box-shadow: 0 5px 25px 5px rgba(239, 68, 68, 0.2);
}

.card-type-receive.hover-glow-effect {
  box-shadow: 0 5px 25px 5px rgba(34, 197, 94, 0.2);
}

.card-type-commission.hover-glow-effect {
  box-shadow: 0 5px 25px 5px rgba(245, 158, 11, 0.2);
}

.dark .card-type-send.hover-glow-effect {
  box-shadow: 0 5px 30px 8px rgba(239, 68, 68, 0.25);
}

.dark .card-type-receive.hover-glow-effect {
  box-shadow: 0 5px 30px 8px rgba(34, 197, 94, 0.25);
}

.dark .card-type-commission.hover-glow-effect {
  box-shadow: 0 5px 30px 8px rgba(245, 158, 11, 0.25);
}
