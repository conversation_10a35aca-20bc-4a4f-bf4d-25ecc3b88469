
import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"
import { Check, X, AlertTriangle, Info } from "lucide-react"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg flex gap-3 pr-8 font-jost",
          title: "group-[.toast]:font-semibold group-[.toast]:text-base",
          description: "group-[.toast]:text-muted-foreground group-[.toast]:text-sm",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
          closeButton:
            "group-[.toast]:text-foreground/50 group-[.toast]:hover:text-foreground group-[.toast]:absolute group-[.toast]:right-2 group-[.toast]:top-2 group-[.toast]:opacity-100 group-[.toast]:transition-opacity",
        },
        duration: 5000,
      }}
      icons={{
        success: <Check className="h-5 w-5 text-green-500" />,
        error: <X className="h-5 w-5 text-destructive" />,
        warning: <AlertTriangle className="h-5 w-5 text-amber-500" />,
        info: <Info className="h-5 w-5 text-blue-500" />,
      }}
      closeButton={true}
      richColors={true}
      style={{ 
        direction: "rtl", 
        flexDirection: "column-reverse"
      }} 
      position="top-center"
      expand={true}
      {...props}
    />
  )
}

export { Toaster, Sonner }

