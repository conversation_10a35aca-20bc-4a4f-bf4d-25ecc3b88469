
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

// Define form schema with validation
const specialCustomerFormSchema = z.object({
  name: z.string().min(2, { message: "يجب أن يحتوي الاسم على حرفين على الأقل" }),
  phone: z.string().optional(),
  notes: z.string().optional(),
});

type SpecialCustomerFormValues = z.infer<typeof specialCustomerFormSchema>;

interface SpecialCustomerFormProps {
  customer?: {
    id: string;
    name: string;
    phone: string;
    notes: string | null;
  };
  onSuccess: () => void;
  onCancel?: () => void;
}

export function SpecialCustomerForm({ customer, onSuccess, onCancel }: SpecialCustomerFormProps) {
  const [loading, setLoading] = useState(false);
  const isEditing = !!customer;

  // Initialize form with default values or customer data if editing
  const form = useForm<SpecialCustomerFormValues>({
    resolver: zodResolver(specialCustomerFormSchema),
    defaultValues: {
      name: customer?.name || "",
      phone: customer?.phone || "",
      notes: customer?.notes || "",
    },
  });

  const onSubmit = async (values: SpecialCustomerFormValues) => {
    setLoading(true);
    try {
      if (isEditing) {
        // Update existing special customer
        const { error } = await supabase
          .from("special_customers")
          .update({
            name: values.name,
            phone: values.phone || null,
            notes: values.notes || null,
          })
          .eq("id", customer.id);

        if (error) throw error;
        toast.success("تم تحديث بيانات العميل المميز بنجاح");
      } else {
        // Add new special customer
        const { error } = await supabase
          .from("special_customers")
          .insert({
            name: values.name,
            phone: values.phone || null,
            notes: values.notes || null,
          });

        if (error) throw error;
        toast.success("تم إضافة العميل المميز بنجاح");
        form.reset();
      }
      
      onSuccess();
    } catch (error) {
      console.error("Error saving special customer:", error);
      toast.error(`حدث خطأ أثناء ${isEditing ? "تحديث" : "إضافة"} العميل المميز`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>اسم العميل</FormLabel>
              <FormControl>
                <Input placeholder="أدخل اسم العميل" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>رقم الهاتف</FormLabel>
              <FormControl>
                <Input placeholder="أدخل رقم الهاتف" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>ملاحظات</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="أدخل ملاحظات (اختياري)" 
                  className="resize-none" 
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex justify-end gap-2 pt-2">
          {onCancel && (
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              disabled={loading}
            >
              إلغاء
            </Button>
          )}
          <Button type="submit" disabled={loading}>
            {loading ? "جاري الحفظ..." : isEditing ? "تحديث العميل المميز" : "إضافة عميل مميز"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
