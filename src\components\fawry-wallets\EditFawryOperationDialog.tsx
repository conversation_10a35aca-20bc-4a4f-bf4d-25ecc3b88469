
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import EditFawryOperationForm from "./components/EditFawryOperationForm";
import { useEditFawryOperationForm } from "./hooks/useEditFawryOperationForm";
import { Wallet, Operation } from "./utils/operationSchema";

interface EditFawryOperationDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  operation: Operation;
  wallets: Wallet[];
  onOperationUpdated: () => void;
}

const EditFawryOperationDialog: React.FC<EditFawryOperationDialogProps> = ({
  isOpen,
  onOpenChange,
  operation,
  wallets,
  onOperationUpdated,
}) => {
  const { form, isSubmitting, onSubmit, handleClose } = useEditFawryOperationForm({
    operation,
    wallets,
    onClose: () => onOpenChange(false),
    onOperationUpdated,
  });

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>تعديل العملية</DialogTitle>
        </DialogHeader>
        
        <EditFawryOperationForm
          form={form}
          wallets={wallets}
          isSubmitting={isSubmitting}
          onSubmit={onSubmit}
          onCancel={handleClose}
        />
      </DialogContent>
    </Dialog>
  );
};

export default EditFawryOperationDialog;
