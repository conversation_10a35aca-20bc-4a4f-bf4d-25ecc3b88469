
import { ProcessedTransaction } from "@/types/transaction.types";
import { TransactionItem } from "./TransactionItem";

interface TransactionListProps {
  transactions: ProcessedTransaction[];
  formatDate: (dateStr: string) => string;
}

export function TransactionList({ transactions, formatDate }: TransactionListProps) {
  return (
    <div>
      {transactions.map((transaction) => (
        <TransactionItem 
          key={transaction.id}
          transaction={transaction}
          formatDate={formatDate}
        />
      ))}
    </div>
  );
}
