
import React from "react";
import { Slice } from "@/services/simService";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface SliceDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  actionLabel: string;
  slice: any;
  setSlice: (slice: any) => void;
  handleAction: () => void;
  branches: { id: string; name: string }[];
  wallets: { id: string; name: string }[];
  isEdit?: boolean;
}

export const SliceDialog: React.FC<SliceDialogProps> = ({
  isOpen,
  onOpenChange,
  title,
  actionLabel,
  slice,
  setSlice,
  handleAction,
  branches,
  wallets,
  isEdit = false,
}) => {
  const fieldPrefix = isEdit ? "edit-" : "";

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            {isEdit ? "قم بتعديل بيانات الشريحة أدناه" : "قم بإدخال بيانات الشريحة الجديدة"}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor={`${fieldPrefix}slice-number`}>رقم الشريحة</Label>
            <Input
              id={`${fieldPrefix}slice-number`}
              placeholder="مثال: 01012345678"
              value={slice.number || ""}
              onChange={(e) =>
                setSlice({ ...slice, number: e.target.value })
              }
              dir="rtl"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor={`${fieldPrefix}wallet-type`}>نوع المحفظة</Label>
            <Select
              value={slice.wallet_id || "no-wallet-selected"}
              onValueChange={(value) =>
                setSlice({ ...slice, wallet_id: value === "no-wallet-selected" ? "" : value })
              }
            >
              <SelectTrigger id={`${fieldPrefix}wallet-type`}>
                <SelectValue placeholder="اختر نوع المحفظة" />
              </SelectTrigger>
              <SelectContent>
                {wallets.length === 0 ? (
                  <SelectItem value="no-wallets-available" disabled>
                    لا توجد محافظ متاحة
                  </SelectItem>
                ) : (
                  wallets.map((wallet) => (
                    <SelectItem key={wallet.id} value={wallet.id}>
                      {wallet.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor={`${fieldPrefix}branch`}>الفرع</Label>
            <Select
              value={slice.branch || "no-branch-selected"}
              onValueChange={(value) =>
                setSlice({ ...slice, branch: value === "no-branch-selected" ? "" : value })
              }
            >
              <SelectTrigger id={`${fieldPrefix}branch`}>
                <SelectValue placeholder="اختر الفرع" />
              </SelectTrigger>
              <SelectContent>
                {branches.length === 0 ? (
                  <SelectItem value="no-branches-available" disabled>
                    لا توجد فروع متاحة
                  </SelectItem>
                ) : (
                  branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id}>
                      {branch.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor={`${fieldPrefix}balance`}>الرصيد الحالي</Label>
            <Input
              id={`${fieldPrefix}balance`}
              type="number"
              placeholder="أدخل الرصيد الحالي"
              value={isEdit ? slice.balance || "" : slice.balance}
              onChange={(e) =>
                setSlice({
                  ...slice,
                  balance: isEdit
                    ? parseFloat(e.target.value) || 0
                    : e.target.value,
                })
              }
              dir="rtl"
            />
          </div>
          
          {/* New Send Limit Field */}
          <div className="grid gap-2">
            <Label htmlFor={`${fieldPrefix}send-limit`}>حد الإرسال</Label>
            <Input
              id={`${fieldPrefix}send-limit`}
              type="number"
              placeholder="أدخل حد الإرسال"
              value={isEdit ? slice.send_limit || "" : slice.send_limit || ""}
              onChange={(e) =>
                setSlice({
                  ...slice,
                  send_limit: isEdit
                    ? parseFloat(e.target.value) || undefined
                    : e.target.value,
                })
              }
              dir="rtl"
            />
          </div>
          
          {/* New Receive Limit Field */}
          <div className="grid gap-2">
            <Label htmlFor={`${fieldPrefix}receive-limit`}>حد الاستقبال</Label>
            <Input
              id={`${fieldPrefix}receive-limit`}
              type="number"
              placeholder="أدخل حد الاستقبال"
              value={isEdit ? slice.receive_limit || "" : slice.receive_limit || ""}
              onChange={(e) =>
                setSlice({
                  ...slice,
                  receive_limit: isEdit
                    ? parseFloat(e.target.value) || undefined
                    : e.target.value,
                })
              }
              dir="rtl"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleAction}>
            {actionLabel}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
