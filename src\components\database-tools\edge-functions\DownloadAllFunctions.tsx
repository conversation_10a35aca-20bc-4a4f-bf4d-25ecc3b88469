
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { EdgeFunction, edgeFunctions } from "./types";
import { downloadAsFile } from "@/utils/database/downloadUtils";
import { useIsMobile } from "@/hooks/use-mobile";

interface DownloadAllFunctionsProps {
  isDownloadingAll: boolean;
  setIsDownloadingAll: React.Dispatch<React.SetStateAction<boolean>>;
}

export const DownloadAllFunctions = ({
  isDownloadingAll,
  setIsDownloadingAll
}: DownloadAllFunctionsProps) => {
  const [progress, setProgress] = useState(0);
  const [loadingStatus, setLoadingStatus] = useState<string>("");
  const isMobile = useIsMobile();
  
  const getFunctionCode = async (functionName: string) => {
    try {
      setLoadingStatus(`جاري تحميل ${functionName}...`);
      
      const { data, error } = await supabase.functions.invoke("fetch-function-code", {
        body: { functionName }
      });
      
      if (error) {
        console.error(`Error fetching function ${functionName}:`, error);
        throw error;
      }
      
      if (!data || !data.code || data.code.trim() === '') {
        console.error(`No code content found for ${functionName}`);
        throw new Error(`لم يتم العثور على محتوى الوظيفة: ${functionName}`);
      }
      
      return data.code;
    } catch (error) {
      console.error(`Error fetching function code for ${functionName}:`, error);
      throw error;
    }
  };

  const handleDownloadAll = async () => {
    setIsDownloadingAll(true);
    setProgress(0);
    setLoadingStatus("جاري تجهيز التنزيل...");
    
    const totalFunctions = edgeFunctions.length;
    let successCount = 0;
    let failedFunctions: string[] = [];
    
    try {
      // إنشاء حاوية للملفات المجمعة في ZIP
      const JSZip = (await import('jszip')).default;
      const zip = new JSZip();
      
      // تنفيذ عمليات التحميل بشكل متتالي مع تحديث التقدم
      for (let i = 0; i < totalFunctions; i++) {
        const func = edgeFunctions[i];
        const currentProgress = Math.round(((i) / totalFunctions) * 100);
        setProgress(currentProgress);
        setLoadingStatus(`جاري تحميل (${i+1}/${totalFunctions}): ${func.name}`);
        
        try {
          console.log(`جاري تحميل الوظيفة: ${func.name}`);
          const code = await getFunctionCode(func.name);
          
          if (code && code.trim() !== '') {
            zip.file(`${func.name}.ts`, code);
            successCount++;
            console.log(`تم تحميل الوظيفة ${func.name} بنجاح`);
          } else {
            console.warn(`لم يتم العثور على كود للوظيفة: ${func.name}`);
            failedFunctions.push(func.name);
          }
        } catch (error: any) {
          console.error(`خطأ في تحميل الوظيفة ${func.name}:`, error);
          failedFunctions.push(func.name);
          
          // إضافة ملف نصي يشرح الخطأ
          zip.file(`${func.name}_error.txt`, `فشل تحميل الوظيفة: ${error.message || "خطأ غير معروف"}`);
        }
        
        // تحديث التقدم بعد كل وظيفة
        setProgress(Math.round(((i + 1) / totalFunctions) * 100));
      }
      
      // إنشاء الملف المضغوط وتنزيله
      if (successCount > 0 || failedFunctions.length > 0) {
        setLoadingStatus("جاري إنشاء ملف الحزمة...");
        const content = await zip.generateAsync({ 
          type: "blob",
          compression: "DEFLATE",
          compressionOptions: { level: 9 }
        });
        
        const url = URL.createObjectURL(content);
        const a = document.createElement("a");
        a.href = url;
        a.download = "edge-functions.zip";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        // إضافة تفاصيل في سجل الأحداث
        const details = [];
        if (successCount > 0) details.push(`${successCount} وظائف تم تنزيلها بنجاح`);
        if (failedFunctions.length > 0) details.push(`${failedFunctions.length} وظائف فشل تنزيلها`);
        
        console.log(`إحصائيات التنزيل: ${details.join(', ')}`);
        
        // إظهار رسالة نجاح
        if (successCount === totalFunctions) {
          toast.success(`تم تحميل جميع الوظائف بنجاح (${successCount} من ${totalFunctions})`);
        } else if (successCount > 0) {
          toast.success(`تم تحميل ${successCount} من ${totalFunctions} وظائف بنجاح`);
          
          if (failedFunctions.length > 0) {
            console.error("وظائف فشل تحميلها:", failedFunctions);
            toast.error(`فشل تحميل ${failedFunctions.length} وظائف: ${failedFunctions.join(", ")}`);
          }
        } else {
          toast.error("لم يتم تحميل أي وظائف بنجاح، تحقق من سجلات الأخطاء");
        }
      } else {
        toast.error("لم يتم تحميل أي وظائف، تحقق من سجلات الأخطاء");
      }
    } catch (error: any) {
      console.error("خطأ في تحميل جميع الملفات:", error);
      toast.error(`فشل تحميل جميع الملفات: ${error.message}`);
    } finally {
      setIsDownloadingAll(false);
      setProgress(0);
      setLoadingStatus("");
    }
  };

  return (
    <div className={`flex items-center ${isMobile ? 'ml-auto' : 'gap-2'}`}>
      {isDownloadingAll && !isMobile && (
        <div className="flex items-center gap-2">
          <div className="text-xs text-muted-foreground">
            {progress}%
          </div>
          {loadingStatus && (
            <div className="text-xs text-muted-foreground max-w-[150px] truncate">
              {loadingStatus}
            </div>
          )}
        </div>
      )}
      <Button
        variant="outline"
        size={isMobile ? "icon-sm" : "sm"}
        onClick={handleDownloadAll}
        disabled={isDownloadingAll}
        className={`flex items-center ${isMobile ? "" : "gap-2"}`}
      >
        <Download className="h-4 w-4" />
        {!isMobile && (
          <span className="whitespace-nowrap">
            {isDownloadingAll ? "جاري التحميل..." : "تحميل جميع الملفات"}
          </span>
        )}
      </Button>
    </div>
  );
};
