
import { useIsMobile } from "@/hooks/use-mobile";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useTransactions } from "@/hooks/useTransactions";
import { TransactionStatsCards } from "@/components/transactions/TransactionStats";
import { TransactionFilters } from "@/components/transactions/TransactionFilters";
import { TransactionTable } from "@/components/transactions/TransactionTable";
import { AddTransactionDialog } from "@/components/transactions/AddTransactionDialog";
import { DeleteAllTransactionsDialog } from "@/components/transactions/DeleteAllTransactionsDialog";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

const Transactions = () => {
  const {
    transactions,
    loading,
    searchTerm,
    setSearchTerm,
    typeFilter,
    setTypeFilter,
    currentPage,
    setCurrentPage,
    totalCount,
    pageSize,
    stats,
    refreshData,
    forceRefreshTransactions
  } = useTransactions();
  
  const isMobile = useIsMobile();
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isDeveloper, setIsDeveloper] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string | null>(null);

  // التحقق مما إذا كان المستخدم مديرًا أو مطورًا
  useEffect(() => {
    const checkUserRoles = async () => {
      if (!user) return;
      
      try {
        // التحقق من صلاحيات المدير
        const { data: adminData, error: adminError } = await supabase
          .from("user_roles")
          .select("role")
          .eq("user_id", user.id)
          .eq("role", "admin");
        
        if (adminError) {
          console.error('خطأ في التحقق من صلاحيات المدير:', adminError);
        }
        
        setIsAdmin(adminData && adminData.length > 0);
        
        // التحقق من صلاحيات المطور
        const { data: devData, error: devError } = await supabase
          .from("user_roles")
          .select("role")
          .eq("user_id", user.id)
          .eq("role", "developer");
        
        if (devError) {
          console.error('خطأ في التحقق من صلاحيات المطور:', devError);
        }
        
        setIsDeveloper(devData && devData.length > 0);
      } catch (error) {
        console.error('خطأ غير متوقع:', error);
      }
    };
    
    checkUserRoles();
  }, [user]);

  // This function will be called when the user changes the status in the filter
  useEffect(() => {
    const fetchFilteredData = async () => {
      if (!filterStatus) return;
      
      // Here we update the filterValue to use our filterStatus
      
      // And we need to trigger a reload of the data
      forceRefreshTransactions();
    };

    fetchFilteredData();
  }, [filterStatus, forceRefreshTransactions]);

  // Function to handle filter status change
  const handleFilterChange = (status: string) => {
    // Update our local state
    setFilterStatus(status === "all" ? null : status);
    
    // No need to call fetchFilteredData here as the useEffect will handle it
  };

  return (
    <div className="p-4 md:p-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 md:mb-6">
        <h1 className="text-2xl md:text-3xl font-bold mb-2 md:mb-0">المعاملات</h1>
        <div className="flex flex-col md:flex-row gap-2">
          <Button 
            onClick={() => setIsAddDialogOpen(true)} 
            className="flex items-center gap-2"
            size="sm"
          >
            <Plus className="h-4 w-4" />
            إضافة معاملة جديدة
          </Button>
          
          {/* إظهار زر حذف المعاملات للمدير والمطور */}
          {(isAdmin || isDeveloper) && (
            <DeleteAllTransactionsDialog onTransactionsDeleted={refreshData} />
          )}
        </div>
      </div>

      <TransactionStatsCards stats={stats} />

      <Card className="mt-4 md:mt-6">
        <CardHeader className={isMobile ? "pb-2 px-4" : "pb-2"}>
          <CardTitle>قائمة المعاملات</CardTitle>
        </CardHeader>
        <CardContent className={isMobile ? "px-3" : "px-6"}>
          <TransactionFilters
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            typeFilter={typeFilter}
            setTypeFilter={setTypeFilter}
            filterStatus={filterStatus}
            handleFilterChange={handleFilterChange}
          />

          <TransactionTable
            transactions={transactions}
            loading={loading}
            currentPage={currentPage}
            totalCount={totalCount}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
          />
        </CardContent>
      </Card>
      
      <AddTransactionDialog 
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onTransactionAdded={refreshData} 
      />
    </div>
  );
};

export default Transactions;
