
import { DateTime } from "luxon";

export const formatCurrency = (amount: number) => {
  // تعديل طريقة عرض المبالغ لتكون بالجنيه فقط بدون القروش
  return new Intl.NumberFormat("ar-EG", {
    style: "decimal",
    maximumFractionDigits: 0,
  }).format(amount) + " ج.م";
};

export const formatDate = (date: string) => {
  // تنسيق التاريخ الميلادي بشكل رقمي مع الوقت بنظام 12 ساعة
  return DateTime.fromISO(date)
    .setLocale("ar")
    .toFormat("dd/MM/yyyy, hh:mm:ss a");
};
