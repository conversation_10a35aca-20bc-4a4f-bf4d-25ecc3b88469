
export interface NotificationRule {
  balance_alert_enabled: boolean;
  min_balance: number;
  transaction_alert_enabled: boolean;
  transaction_types: string[];
  min_transaction_amount: number;
}

export interface TelegramSettings {
  is_enabled: boolean;
  bot_token: string;
  chat_id: string;
  user_id: string;
}

export interface TransactionNotification {
  id: string;
  created_at: string;
  amount: number;
  commission: number;
  transaction_type: 'receive' | 'send';
  customer_name: string;
  customer_phone?: string;
  description?: string;
  wallet?: {
    name?: string;
  };
  sim?: {
    number?: string;
    balance?: number;
  };
}

export interface LowBalanceSim {
  id: string;
  number: string;
  balance: number;
  wallet_name: string;
}

export interface FawryWalletOperation {
  id: string;
  created_at: string;
  amount: number;
  commission: number;
  operation_type: string;
  wallet?: {
    name?: string;
    balance?: number;
  };
}
