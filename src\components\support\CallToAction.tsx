
import { Button } from "@/components/ui/button";
import { Phone } from "lucide-react";

interface CallToActionProps {
  scrollToContact: () => void;
}

export function CallToAction({ scrollToContact }: CallToActionProps) {
  return (
    <div className="mt-12 text-center">
      <h2 className="text-2xl font-bold mb-4">جاهز للبدء؟</h2>
      <p className="text-muted-foreground mb-6">تواصل معنا اليوم للحصول على العرض الذي يناسبك</p>
      <div className="flex flex-wrap justify-center gap-4">
        <Button 
          size="lg" 
          onClick={scrollToContact}
          className="bg-primary hover:bg-primary/90 shadow-lg flex items-center gap-2 px-8 py-6 text-lg cta-button"
        >
          <Phone className="h-5 w-5 ml-2" /> تواصل معنا الآن
        </Button>
      </div>
    </div>
  );
}
