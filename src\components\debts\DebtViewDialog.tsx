
import {
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Debt } from "@/types/debt.types";
import { DebtStatusBadge } from "./DebtStatusBadge";
import { useIsMobile } from "@/hooks/use-mobile";

interface DebtViewDialogProps {
  debt: Debt | null;
  walletName: string;
  onClose: () => void;
  formatCurrency: (amount: number) => string;
  formatDate: (dateString: string | null) => string;
}

export function DebtViewDialog({
  debt,
  walletName,
  onClose,
  formatCurrency,
  formatDate
}: DebtViewDialogProps) {
  if (!debt) return null;
  
  const isMobile = useIsMobile();
  
  return (
    <DialogContent 
      className={`
        max-w-[95vw] sm:max-w-[550px] 
        ${isMobile ? 'p-3' : 'p-4'} 
        overflow-y-auto max-h-[90vh]
      `}
    >
      <DialogHeader className="mb-2 space-y-1">
        <DialogTitle className={`${isMobile ? 'text-lg' : 'text-xl'}`}>تفاصيل الدين</DialogTitle>
        <DialogDescription>
          عرض المعلومات الكاملة للدين
        </DialogDescription>
      </DialogHeader>
      
      <div className="space-y-3">
        <div className={`grid grid-cols-1 ${isMobile ? 'gap-1.5' : 'sm:grid-cols-2 gap-2'}`}>
          <div className="p-2 bg-muted/20 rounded-md">
            <p className="text-sm font-medium text-muted-foreground">العميل</p>
            <p className="font-medium truncate">{debt.customer?.name || "غير محدد"}</p>
          </div>
          
          <div className="p-2 bg-muted/20 rounded-md">
            <p className="text-sm font-medium text-muted-foreground">رقم الهاتف</p>
            <p className="font-medium truncate">{debt.customer?.phone || "غير محدد"}</p>
          </div>
          
          <div className="p-2 bg-muted/20 rounded-md">
            <p className="text-sm font-medium text-muted-foreground">المبلغ</p>
            <p className="font-medium truncate">{formatCurrency(Number(debt.amount))}</p>
          </div>
          
          <div className="p-2 bg-muted/20 rounded-md">
            <p className="text-sm font-medium text-muted-foreground">تاريخ الاستحقاق</p>
            <p className="font-medium truncate">{formatDate(debt.due_date)}</p>
          </div>
          
          <div className="p-2 bg-muted/20 rounded-md">
            <p className="text-sm font-medium text-muted-foreground">الحالة</p>
            <p><DebtStatusBadge debt={debt} /></p>
          </div>
          
          <div className="p-2 bg-muted/20 rounded-md">
            <p className="text-sm font-medium text-muted-foreground">تاريخ الدفع</p>
            <p className="font-medium truncate">{debt.paid_at ? formatDate(debt.paid_at) : "لم يتم الدفع"}</p>
          </div>
          
          <div className="p-2 bg-muted/20 rounded-md col-span-full">
            <p className="text-sm font-medium text-muted-foreground">المحفظة</p>
            <p className="font-medium truncate">{walletName}</p>
          </div>
        </div>
        
        {debt.notes && (
          <div className="p-2 bg-muted/20 rounded-md">
            <p className="text-sm font-medium text-muted-foreground">ملاحظات</p>
            <div className="mt-1">
              <p className="whitespace-pre-wrap break-words text-sm">{debt.notes}</p>
            </div>
          </div>
        )}
        
        <div className={`flex ${isMobile ? 'mt-2' : 'mt-3'} justify-end pt-2`}>
          <Button 
            onClick={onClose} 
            size={isMobile ? "mobile-sm" : "default"}
            className={`${isMobile ? 'w-full' : 'w-auto'} min-h-[36px]`}
          >
            إغلاق
          </Button>
        </div>
      </div>
    </DialogContent>
  );
}
