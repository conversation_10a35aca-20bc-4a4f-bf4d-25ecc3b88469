
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { TransactionType } from "@/utils/transaction/types";
import { calculateCommission } from "./transactionCommissionService";

/**
 * Submit transaction data to the database
 * @param transactionData Transaction data to submit
 * @returns Success status
 */
export const submitTransaction = async (transactionData: any): Promise<boolean> => {
  try {
    // Prepare transaction data, handling customer information
    const { status, customer_name, customer_phone, type, ...dataToSubmit } = transactionData;
    
    // Format data to match database structure
    const formattedData = {
      ...dataToSubmit,
      // Use appropriate transaction type
      transaction_type: type,
      // Add customer name and phone directly to transaction
      customer_name,
      customer_phone
    };

    console.log("Submitting transaction data:", formattedData);
    
    const { data: transactionResult, error } = await supabase
      .from("transactions")
      .insert([formattedData])
      .select()
      .single();
      
    if (error) throw error;
    
    // Update SIM balance based on transaction type
    if (formattedData.sim_id) {
      // Get current SIM balance
      const { data: simData, error: simError } = await supabase
        .from("sims")
        .select("balance")
        .eq("id", formattedData.sim_id)
        .single();
        
      if (simError) {
        console.error("Error fetching SIM balance:", simError);
      } else if (simData) {
        // Calculate new balance
        let newBalance = simData.balance;
        
        if (type === "receive") {
          // Receive operation: add amount to balance
          newBalance += Number(formattedData.amount);
        } else if (type === "send") {
          // Send operation: subtract amount from balance
          newBalance -= Number(formattedData.amount);
        }
        
        // Update SIM balance
        const { error: updateError } = await supabase
          .from("sims")
          .update({ balance: newBalance })
          .eq("id", formattedData.sim_id);
          
        if (updateError) {
          console.error("Error updating SIM balance:", updateError);
        }
      }
    }
    
    toast.success("تم إضافة العملية بنجاح");
    return true;
  } catch (error) {
    console.error("Error submitting transaction:", error);
    toast.error("حدث خطأ أثناء إضافة العملية");
    return false;
  }
};

// Re-export calculateCommission from transactionCommissionService
export { calculateCommission };
