
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";

interface AmountFieldsProps {
  form: UseFormReturn<any>;
  isCalculating: boolean;
  isFawryWallet?: boolean;
}

export function AmountFields({ 
  form, 
  isCalculating,
  isFawryWallet = false
}: AmountFieldsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <FormField
        control={form.control}
        name="amount"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              المبلغ (جنيه) <span className="text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <Input 
                type="number" 
                value={field.value ? field.value.toString() : '0'} 
                placeholder="أدخل المبلغ" 
                onChange={(e) => {
                  const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                  field.onChange(value);
                }}
                className={field.value <= 0 ? "border-red-500" : ""}
                required
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="commission"
        render={({ field }) => (
          <FormItem>
            <FormLabel>العمولة (جنيه)</FormLabel>
            <FormControl>
              <Input 
                type="number" 
                value={field.value ? field.value.toString() : '0'} 
                placeholder="العمولة" 
                onChange={(e) => {
                  const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                  field.onChange(value);
                }}
                disabled={isCalculating || (!isFawryWallet && form.watch('amount') > 0)}
                className={isCalculating ? 'bg-gray-100' : ''}
              />
            </FormControl>
            {isCalculating && <div className="text-xs text-muted-foreground mt-1">جاري حساب العمولة...</div>}
            {isFawryWallet && <div className="text-xs text-muted-foreground mt-1">يمكنك تعديل العمولة لمحافظ فوري</div>}
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
