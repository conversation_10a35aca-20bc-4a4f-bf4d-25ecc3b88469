
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Download } from "lucide-react";
import { CreateBackupButton } from "./actions/CreateBackupButton";
import { CreateFullBackupButton } from "./actions/CreateFullBackupButton";
import { SqlExportButton } from "./actions/SqlExportButton";

export default function BackupSection() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          إنشاء نسخة احتياطية
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="mb-4 text-muted-foreground">
          قم بإنشاء نسخة احتياطية كاملة من قاعدة البيانات وتحميلها على جهازك.
        </p>
        <div className="flex flex-col gap-3">
          <CreateBackupButton />
          <CreateFullBackupButton />
          <SqlExportButton />
        </div>
      </CardContent>
    </Card>
  );
}
