
import React from "react";
import { Edit, Trash } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  TableCard,
  TableCardRow,
} from "@/components/ui/table";
import { Slice } from "@/types/slice.types";

interface SliceMobileViewProps {
  slices: Slice[];
  isLoading: boolean;
  onEdit: (slice: Slice) => void;
  onDelete: (slice: Slice) => void;
  onToggleStatus: (id: string, currentStatus: boolean) => void;
  getBranchName: (branchId: string) => string;
}

export const SliceMobileView: React.FC<SliceMobileViewProps> = ({
  slices,
  isLoading,
  onEdit,
  onDelete,
  onToggleStatus,
  getBranchName,
}) => {
  return (
    <div className="space-y-4">
      {isLoading ? (
        <>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse bg-gray-100 dark:bg-gray-800/50 h-32 rounded-lg"></div>
          ))}
        </>
      ) : slices.length > 0 ? (
        slices.map((slice) => (
          <TableCard
            key={slice.id}
            title={
              <div>
                <div className="font-medium">{slice.walletName}</div>
                <div className="text-xs">شريحة #{slice.number}</div>
              </div>
            }
            actions={
              <div className="rtl-actions-group-mobile">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEdit(slice)}
                  className="rtl-action-button rtl-action-button-edit rtl-action-button-mobile"
                >
                  <Edit className="h-4 w-4" />
                  <span>تعديل</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onDelete(slice)}
                  className="rtl-action-button rtl-action-button-destructive rtl-action-button-mobile"
                >
                  <Trash className="h-4 w-4" />
                  <span>حذف</span>
                </Button>
              </div>
            }
            className="mobile-card theme-card"
          >
            <TableCardRow 
              label="المحفظة" 
              value={slice.walletName} 
              className="table-card-row"
            />
            <TableCardRow 
              label="الفرع" 
              value={getBranchName(slice.branch)} 
              className="table-card-row"
            />
            <TableCardRow 
              label="الرصيد" 
              value={slice.balance.toLocaleString() + " جنيه"} 
              className="table-card-row"
            />
            <TableCardRow 
              label="حد الإرسال" 
              value={slice.send_limit ? slice.send_limit.toLocaleString() + " جنيه" : "غير محدد"} 
              className="table-card-row"
            />
            <TableCardRow 
              label="حد الاستقبال" 
              value={slice.receive_limit ? slice.receive_limit.toLocaleString() + " جنيه" : "غير محدد"} 
              className="table-card-row"
            />
            <TableCardRow
              label="الحالة"
              value={
                <Button
                  variant={slice.active_status ? "success" : "destructive"}
                  size="sm"
                  onClick={() => onToggleStatus(slice.id, slice.active_status)}
                  className="rtl-action-button rtl-action-button-mobile"
                  style={{
                    backgroundColor: slice.active_status ? '#10b981' : '#ef4444',
                    color: 'white',
                    border: 'none'
                  }}
                >
                  <span
                    className={`h-2 w-2 rounded-full ml-2 ${
                      slice.active_status ? "bg-green-500" : "bg-destructive"
                    }`}
                  />
                  {slice.active_status ? "نشط" : "غير نشط"}
                </Button>
              }
              className="table-card-row"
            />
          </TableCard>
        ))
      ) : (
        <div className="text-center py-8 border rounded-lg">
          لا توجد شرائح مضافة حتى الآن
        </div>
      )}
    </div>
  );
}
