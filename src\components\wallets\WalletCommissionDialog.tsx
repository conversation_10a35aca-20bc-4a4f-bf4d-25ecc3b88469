
import { useState } from "react";
import { 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

// Create schema for wallet commission form
const commissionSchema = z.object({
  depositRate: z.number()
    .min(0, { message: "لا يمكن أن تكون العمولة بقيمة سالبة" })
    .max(100, { message: "لا يمكن أن تزيد العمولة عن 100%" }),
  withdrawalRate: z.number()
    .min(0, { message: "لا يمكن أن تكون العمولة بقيمة سالبة" })
    .max(100, { message: "لا يمكن أن تزيد العمولة عن 100%" }),
  minCommission: z.number()
    .min(0, { message: "لا يمكن أن تكون العمولة بقيمة سالبة" })
});

type CommissionValues = z.infer<typeof commissionSchema>;

interface WalletCommissionDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  walletId: string;
  walletName: string;
}

export function WalletCommissionDialog({ 
  isOpen, 
  onOpenChange,
  walletId,
  walletName 
}: WalletCommissionDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  
  const form = useForm<CommissionValues>({
    resolver: zodResolver(commissionSchema),
    defaultValues: {
      depositRate: 0,
      withdrawalRate: 1,
      minCommission: 5
    }
  });
  
  // Load existing commission settings when dialog opens
  const loadCommissionSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('wallet_commissions')
        .select('deposit_rate, withdrawal_rate, min_commission')
        .eq('wallet_name', walletName)
        .maybeSingle();
        
      if (error) {
        console.error("Error loading wallet commission:", error);
        return;
      }
      
      if (data) {
        form.setValue('depositRate', data.deposit_rate);
        form.setValue('withdrawalRate', data.withdrawal_rate);
        form.setValue('minCommission', data.min_commission);
      }
    } catch (error) {
      console.error("Error loading wallet commission:", error);
    }
  };
  
  // Load data when dialog opens
  if (isOpen) {
    loadCommissionSettings();
  }
  
  const onSubmit = async (values: CommissionValues) => {
    setIsLoading(true);
    
    try {
      // Check if commission entry exists
      const { data: existing, error: checkError } = await supabase
        .from('wallet_commissions')
        .select('id')
        .eq('wallet_name', walletName)
        .maybeSingle();
      
      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }
      
      let result;
      
      if (existing) {
        // Update existing commission
        result = await supabase
          .from('wallet_commissions')
          .update({
            deposit_rate: values.depositRate,
            withdrawal_rate: values.withdrawalRate,
            min_commission: values.minCommission,
            updated_at: new Date().toISOString()
          })
          .eq('wallet_name', walletName);
      } else {
        // Create new commission entry
        result = await supabase
          .from('wallet_commissions')
          .insert({
            wallet_name: walletName,
            deposit_rate: values.depositRate,
            withdrawal_rate: values.withdrawalRate,
            min_commission: values.minCommission
          });
      }
      
      if (result.error) {
        throw result.error;
      }
      
      toast.success("تم حفظ إعدادات العمولة بنجاح");
      onOpenChange(false);
    } catch (error) {
      console.error("Error saving wallet commission:", error);
      toast.error("حدث خطأ أثناء حفظ إعدادات العمولة");
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span>إعدادات عمولة {walletName}</span>
          </DialogTitle>
          <DialogDescription>
            قم بتعديل قيم العمولات المطبقة على هذه المحفظة
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="depositRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>عمولة الإيداع (%)</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.1"
                      placeholder="0" 
                      {...field} 
                      onChange={e => field.onChange(parseFloat(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="withdrawalRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>عمولة السحب (%)</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.1"
                      placeholder="1" 
                      {...field}
                      onChange={e => field.onChange(parseFloat(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="minCommission"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الحد الأدنى للعمولة (جنيه)</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="1"
                      placeholder="5" 
                      {...field}
                      onChange={e => field.onChange(parseFloat(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex justify-end gap-2 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                إلغاء
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "جاري الحفظ..." : "حفظ العمولة"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
