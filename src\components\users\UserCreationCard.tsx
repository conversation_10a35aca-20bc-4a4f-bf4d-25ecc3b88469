
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { UserCreateForm } from "./UserCreateForm";

interface UserCreationCardProps {
  onUserCreated: () => void;
}

export const UserCreationCard = ({ onUserCreated }: UserCreationCardProps) => {
  const [isCreating, setIsCreating] = useState(false);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>إضافة مستخدم جديد</span>
          <Button 
            variant="outline" 
            onClick={() => setIsCreating(!isCreating)}
            className="transition-colors dark:bg-[#D0AC19] dark:text-black dark:hover:bg-[#D0AC19]/90 bg-[#E6272E] text-white hover:bg-[#E6272E]/90 border-none"
          >
            {isCreating ? "إلغاء" : "إضافة مستخدم جديد"}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isCreating && (
          <UserCreateForm 
            onSuccess={() => {
              setIsCreating(false);
              onUserCreated();
            }} 
          />
        )}
      </CardContent>
    </Card>
  );
};
