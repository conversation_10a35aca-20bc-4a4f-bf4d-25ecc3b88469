
@layer utilities {
  /* تنسيقات خاصة بالواجهة العربية */
  [dir="rtl"] {
    @apply text-right;
  }

  /* تنسيقات الجداول بالعربية */
  [dir="rtl"] .rtl-table {
    @apply text-right;
  }

  [dir="rtl"] .rtl-table th,
  [dir="rtl"] .rtl-table td {
    @apply text-right;
  }

  /* تنسيقات محاذاة النصوص والعناصر */
  [dir="rtl"] .rtl-text {
    @apply text-right;
  }

  [dir="rtl"] .rtl-flex {
    @apply flex-row-reverse;
  }

  /* تنسيقات للهوامش والحشو */
  [dir="rtl"] .mr-rtl-ml {
    @apply ml-0.5;
  }

  /* تنسيقات الأزرار والإجراءات */
  [dir="rtl"] .rtl-button {
    @apply flex-row-reverse;
  }

  [dir="rtl"] .rtl-actions-group {
    @apply flex flex-row gap-2;
  }

  /* تنسيقات للأجهزة الصغيرة */
  .rtl-mobile-container {
    @apply text-right;
  }

  .rtl-mobile-text {
    @apply text-right;
  }

  .rtl-mobile-card {
    @apply text-right;
  }

  [dir="rtl"] .rtl-action-button {
    @apply flex items-center gap-1 flex-row-reverse;
  }

  [dir="rtl"] .rtl-action-button-mobile {
    @apply text-xs py-1 px-1.5;
  }

  /* تنسيقات مختلفة للأزرار */
  .rtl-action-button-edit {
    @apply text-blue-600 hover:bg-blue-50 hover:text-blue-700
           dark:text-blue-400 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 
           border-blue-200 dark:border-blue-800;
  }

  .rtl-action-button-destructive {
    @apply text-red-600 hover:bg-red-50 hover:text-red-700
           dark:text-red-400 dark:hover:bg-red-900/30 dark:hover:text-red-300
           border-red-200 dark:border-red-800;
  }

  .rtl-action-button-success {
    @apply text-green-600 hover:bg-green-50 hover:text-green-700
           dark:text-green-400 dark:hover:bg-green-900/30 dark:hover:text-green-300
           border-green-200 dark:border-green-800;
  }

  /* تنسيقات للفرزات والمرشحات */
  [dir="rtl"] .rtl-filters {
    @apply flex-row-reverse;
  }

  [dir="rtl"] .rtl-filter-buttons {
    @apply flex-row-reverse;
  }

  /* تنسيقات الأزرار المتجهة */
  [dir="rtl"] .rtl-arrow {
    transform: scaleX(-1);
  }

  /* تنسيقات مجموعة الإجراءات للأجهزة المحمولة */
  [dir="rtl"] .rtl-actions-group-mobile {
    @apply flex flex-col gap-2;
  }

  /* تحسينات للقراءة والاستخدام */
  .theme-transition {
    @apply transition-all duration-300 ease-in-out;
  }
}
