
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X } from "lucide-react";

interface SpecialCustomer {
  id: string;
  name: string;
  phone?: string | null;
}

interface SpecialCustomerSelectorProps {
  onSelectCustomer: (customer: SpecialCustomer | null) => void;
  selectedCustomer: SpecialCustomer | null;
}

export function SpecialCustomerSelector({ 
  onSelectCustomer, 
  selectedCustomer 
}: SpecialCustomerSelectorProps) {
  const [specialCustomers, setSpecialCustomers] = useState<SpecialCustomer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<SpecialCustomer[]>([]);

  useEffect(() => {
    const fetchSpecialCustomers = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from("special_customers")
          .select("id, name, phone")
          .order("name", { ascending: true });
        
        if (error) throw error;
        setSpecialCustomers(data || []);
      } catch (error) {
        console.error("Error fetching special customers:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSpecialCustomers();
  }, []);

  // Filter customers when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setSearchResults(specialCustomers);
      return;
    }

    const results = specialCustomers.filter(
      customer => 
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
        (customer.phone && customer.phone.includes(searchTerm))
    );
    
    setSearchResults(results);
  }, [searchTerm, specialCustomers]);

  const handleClearSelection = () => {
    onSelectCustomer(null);
    setSearchTerm("");
  };

  if (loading) {
    return <div className="text-center py-2">جاري التحميل...</div>;
  }

  return (
    <div className="space-y-2">
      <div className="relative">
        <Search className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="ابحث باسم العميل أو رقم الهاتف..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pr-9 rounded-md"
        />
      </div>
      
      {selectedCustomer ? (
        <div className="flex items-center justify-between p-3 border rounded-md bg-muted/30">
          <div>
            <p className="font-medium">{selectedCustomer.name}</p>
            {selectedCustomer.phone && <p className="text-sm text-muted-foreground">{selectedCustomer.phone}</p>}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearSelection}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <Select
          value={selectedCustomer?.id || ""}
          onValueChange={(value) => {
            if (!value) return;
            const customer = specialCustomers.find((c) => c.id === value);
            if (customer) {
              onSelectCustomer(customer);
            }
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="اختر عميل مميز" />
          </SelectTrigger>
          <SelectContent>
            {searchResults.length === 0 ? (
              <div className="p-2 text-center text-sm text-muted-foreground">
                لا توجد نتائج
              </div>
            ) : (
              searchResults.map((customer) => (
                <SelectItem key={customer.id} value={customer.id}>
                  {customer.name} {customer.phone ? `- ${customer.phone}` : ""}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      )}
    </div>
  );
}
