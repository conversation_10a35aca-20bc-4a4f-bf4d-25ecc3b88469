
import React from "react";
import { User } from "@/types/user.types";
import { Trash2, Edit } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";

interface UserActionsProps {
  user: User;
  isDeveloper: (email: string) => boolean;
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  hideLabels?: boolean;
}

export const UserActions = ({
  user,
  isDeveloper,
  onEdit,
  onDelete,
  hideLabels = false
}: UserActionsProps) => {
  const isMobile = useIsMobile();
  
  // If developer account, show protected message
  if (isDeveloper(user.email)) {
    return (
      <span className="text-sm italic text-destructive font-bold">
        {isMobile || hideLabels ? "" : "حساب محمي"}
      </span>
    );
  }

  return (
    <div className={`flex ${isMobile ? 'flex-col mt-4 w-full' : 'flex-row'} gap-2 rtl:flex-row-reverse`}>
      <Button
        variant="outline"
        size={isMobile || hideLabels ? "sm" : "sm"}
        onClick={() => onEdit(user)}
        className={`rtl-action-button-edit ${isMobile ? 'w-full' : ''}`}
      >
        <Edit size={16} className="rtl:ml-2 ltr:mr-2" />
        <span>تعديل</span>
      </Button>

      <Button
        variant="outline"
        size={isMobile || hideLabels ? "sm" : "sm"}
        onClick={() => onDelete(user)}
        className={`rtl-action-button-destructive ${isMobile ? 'w-full' : ''}`}
      >
        <Trash2 size={16} className="rtl:ml-2 ltr:mr-2" />
        <span>حذف</span>
      </Button>
    </div>
  );
};
