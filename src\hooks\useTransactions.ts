
import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Transaction, TransactionStats } from "@/types/transaction.types";
import { toast } from "sonner";

export function useTransactions() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 10;
  const [stats, setStats] = useState<TransactionStats>({
    totalCount: 0,
    sentAmount: 0,
    receivedAmount: 0,
    commissionAmount: 0
  });

  // Fetch transactions with filters and pagination
  const fetchTransactions = useCallback(async () => {
    try {
      setLoading(true);
      
      // Build count query with filters
      let countQuery = supabase
        .from('transactions')
        .select('id', { count: 'exact' });
      
      // Apply type filter to count query
      if (typeFilter) {
        countQuery = countQuery.eq('transaction_type', typeFilter);
      }
      
      // First get total count for pagination
      const { count, error: countError } = await countQuery;
        
      if (countError) throw countError;
      
      setTotalCount(count || 0);
      
      // Then fetch the actual data
      let query = supabase
        .from('transactions')
        .select(`
          id,
          amount,
          transaction_type,
          created_at,
          description,
          commission,
          wallet_id,
          wallet:wallet_id (name),
          customer_id,
          customer:customer_id (name, phone),
          customer_name,
          customer_phone,
          sim_id
        `)
        .order('created_at', { ascending: false });
      
      // Apply filters if they exist
      if (typeFilter) {
        query = query.eq('transaction_type', typeFilter);
      }
      
      // Calculate pagination
      const from = (currentPage - 1) * pageSize;
      const to = from + pageSize - 1;
      
      query = query.range(from, to);
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      setTransactions(data || []);
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toast.error("حدث خطأ أثناء جلب المعاملات");
    } finally {
      setLoading(false);
    }
  }, [typeFilter, currentPage]);

  // Calculate transaction stats
  const calculateStats = useCallback(async () => {
    try {
      // Get all transactions for calculating stats
      const { data, error } = await supabase
        .from('transactions')
        .select('transaction_type, amount, commission');

      if (error) throw error;
      
      if (data) {
        const sentAmount = data
          .filter(t => t.transaction_type === 'send')
          .reduce((sum, t) => sum + (t.amount || 0), 0);
          
        const receivedAmount = data
          .filter(t => t.transaction_type === 'receive')
          .reduce((sum, t) => sum + (t.amount || 0), 0);
          
        const commissionAmount = data
          .reduce((sum, t) => sum + (t.commission || 0), 0);
          
        setStats({
          totalCount: data.length,
          sentAmount,
          receivedAmount,
          commissionAmount
        });
      }
    } catch (error) {
      console.error("Error calculating stats:", error);
      toast.error("حدث خطأ أثناء حساب الإحصائيات");
    }
  }, []);

  // Initial data loading
  useEffect(() => {
    fetchTransactions();
  }, [typeFilter, currentPage, fetchTransactions]);

  useEffect(() => {
    calculateStats();
  }, [calculateStats]);

  // Function to refresh data (used after adding a transaction)
  const refreshData = useCallback(() => {
    // Reset to first page when refreshing data
    setCurrentPage(1);
    fetchTransactions();
    calculateStats();
    toast.success("تم تحديث البيانات");
  }, [fetchTransactions, calculateStats]);

  // Add the missing forceRefreshTransactions function
  const forceRefreshTransactions = useCallback(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  // Filter transactions based on search term locally
  const filteredTransactions = transactions.filter(
    (transaction) => {
      if (!searchTerm) return true;
      
      return (
        transaction.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.wallet?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.customer?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.customer_phone?.includes(searchTerm) ||
        String(transaction.amount).includes(searchTerm)
      );
    }
  );

  return {
    transactions: filteredTransactions,
    loading,
    searchTerm,
    setSearchTerm,
    typeFilter,
    setTypeFilter,
    currentPage,
    setCurrentPage,
    totalCount,
    pageSize,
    stats,
    refreshData,
    forceRefreshTransactions
  };
}
