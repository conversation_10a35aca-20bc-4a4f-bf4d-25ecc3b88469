
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { NotificationSettings } from "@/components/settings/notifications/NotificationSettings";
import { Bell, Settings as SettingsIcon } from "lucide-react";
import { useLocation } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { supabase } from "@/integrations/supabase/client";
import { useAuthWithSecurity } from "@/hooks/useAuthWithSecurity";

interface SettingsTabProps {
  title: string;
  icon: React.ReactNode;
  value: string;
}

const Settings = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<string>("notifications");
  const isMobile = useIsMobile();
  const [currentRole, setCurrentRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  
  // Initialize security hooks
  useAuthWithSecurity();
  
  useEffect(() => {
    // Get the current user's role
    async function getUserRole() {
      setLoading(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          const { data: roleData, error } = await supabase
            .from("user_roles")
            .select("role")
            .eq("user_id", user.id)
            .single();
            
          if (error) throw error;
          setCurrentRole(roleData?.role || null);
        }
      } catch (error) {
        console.error("Error fetching user role:", error);
      } finally {
        setLoading(false);
      }
    }
    
    getUserRole();
  }, []);
  
  // Generate settings tabs - removed database tab
  const settingsTabs: SettingsTabProps[] = [
    {
      title: "الإشعارات",
      icon: <Bell className="h-5 w-5" />,
      value: "notifications",
    }
  ];
  
  useEffect(() => {
    // Check for tab parameter in URL
    const params = new URLSearchParams(location.search);
    const tabParam = params.get("tab");
    if (tabParam && settingsTabs.some(tab => tab.value === tabParam)) {
      setActiveTab(tabParam);
    }
  }, [location.search, settingsTabs]);

  // تحديث عنوان URL عند تغيير التاب
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // تحديث URL للاحتفاظ بحالة التاب
    const params = new URLSearchParams(location.search);
    params.set("tab", value);
    window.history.replaceState(
      null, 
      '', 
      `${location.pathname}?${params.toString()}`
    );
  };
  
  if (loading) {
    return (
      <div className="container mx-auto p-4 md:p-6 lg:p-8 max-w-7xl">
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto p-4 md:p-6 lg:p-8 max-w-7xl animate-fade-in">
      <div className="mb-8 space-y-2">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <SettingsIcon className="h-8 w-8 text-primary" />
          إعدادات النظام
        </h1>
        <p className="text-muted-foreground">
          تخصيص إعدادات النظام وإدارة تفضيلات المستخدم
        </p>
      </div>

      {isMobile ? (
        <div className="space-y-4">
          <Card className="shadow-md border-primary/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-4 text-primary">
                <SettingsIcon className="h-6 w-6" />
                <h2 className="font-semibold text-xl">الإعدادات</h2>
              </div>
              
              <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
                <TabsList className="mb-6 bg-muted/50 w-full grid grid-cols-1 h-auto p-1">
                  {settingsTabs.map((tab) => (
                    <TabsTrigger
                      key={tab.value}
                      value={tab.value}
                      className="flex items-center justify-center gap-2 p-3 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                      title={tab.title}
                    >
                      {tab.icon}
                      <span>{tab.title}</span>
                    </TabsTrigger>
                  ))}
                </TabsList>
                
                <TabsContent value="notifications" className="mt-0 animate-fade-in">
                  <NotificationSettings />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="bg-card rounded-lg border shadow-sm p-4">
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="mb-8 bg-muted/50">
              {settingsTabs.map((tab) => (
                <TabsTrigger
                  key={tab.value}
                  value={tab.value}
                  className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
                >
                  {tab.icon}
                  <span>{tab.title}</span>
                </TabsTrigger>
              ))}
            </TabsList>
            
            <TabsContent value="notifications" className="mt-0 animate-fade-in">
              <NotificationSettings />
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
};

export default Settings;
