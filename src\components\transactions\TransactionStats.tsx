
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/utils/formatters";
import { TransactionStats } from "@/types/transaction.types";
import { Coins, SendHorizonal, ReceiptText, Percent } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface TransactionStatsCardsProps {
  stats: TransactionStats;
}

export function TransactionStatsCards({ stats }: TransactionStatsCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-4 mb-6">
      <TransactionStatCard
        title="إجمالي المعاملات"
        value={stats.totalCount.toString()}
        icon={<Coins className="h-5 w-5 text-primary" />}
        description="+12% من الشهر الماضي"
        effectColor="amber"
      />
      
      <TransactionStatCard
        title="مبالغ مرسلة"
        value={formatCurrency(stats.sentAmount)}
        icon={<SendHorizonal className="h-5 w-5 text-red-500" />}
        description="-2% من الشهر الماضي"
        textColor="text-red-500"
        effectColor="red"
      />
      
      <TransactionStatCard
        title="مبالغ مستلمة"
        value={formatCurrency(stats.receivedAmount)}
        icon={<ReceiptText className="h-5 w-5 text-green-500" />}
        description="+18% من الشهر الماضي"
        textColor="text-green-500"
        effectColor="green"
      />
      
      <TransactionStatCard
        title="صافي العمولات"
        value={formatCurrency(stats.commissionAmount)}
        icon={<Percent className="h-5 w-5 text-primary" />}
        description="+5% من الشهر الماضي"
        textColor="text-primary"
        effectColor="blue"
      />
    </div>
  );
}

interface TransactionStatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  description?: string;
  textColor?: string;
  effectColor?: "red" | "green" | "amber" | "blue";
}

function TransactionStatCard({ 
  title, 
  value, 
  icon, 
  description, 
  textColor = "text-foreground",
  effectColor = "amber"
}: TransactionStatCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  
  // تحديد لون خلفية الأيقونة حسب نوع البطاقة
  const getIconBgColor = () => {
    switch (effectColor) {
      case 'red':
        return 'bg-red-500/10';
      case 'green':
        return 'bg-green-500/10';
      case 'amber':
        return 'bg-amber-500/10';
      case 'blue':
        return 'bg-blue-500/10';
      default:
        return 'bg-primary/10';
    }
  };

  return (
    <div 
      className={cn(
        `effect-card glass-effect effect-${effectColor}`,
        isHovered ? "scale-[1.01]" : ""
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* تأثير توهج الحدود عند تحويم المؤشر */}
      {isHovered && (
        <div className="animated-border"></div>
      )}
      
      <Card className="relative z-10 border-0 backdrop-blur-sm bg-white/80 dark:bg-gray-800/80 overflow-hidden rounded-[0.7rem] transition-all duration-300">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium card-title">{title}</CardTitle>
          <div className={cn("p-2 rounded-full card-icon", getIconBgColor())}>{icon}</div>
        </CardHeader>
        <CardContent className="card-content">
          <div className={cn("text-2xl font-bold card-value", textColor)}>{value}</div>
          {description && (
            <p className="text-xs text-muted-foreground card-description">{description}</p>
          )}
        </CardContent>
      </Card>

      {/* إضافة حدود خفيفة */}
      <div className={cn(
        "absolute inset-0 rounded-xl border-2 pointer-events-none", 
        effectColor === 'red' ? "border-red-500/40 dark:border-red-500/30" : 
        effectColor === 'green' ? "border-green-500/40 dark:border-green-500/30" : 
        effectColor === 'amber' ? "border-amber-500/40 dark:border-amber-500/30" :
        effectColor === 'blue' ? "border-blue-500/40 dark:border-blue-500/30" :
        "border-[#EF4343]/40 dark:border-amber-500/30"
      )}></div>
    </div>
  );
}
