
import { 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface AmountFieldProps {
  loading: boolean;
}

export function AmountField({ loading }: AmountFieldProps) {
  return (
    <FormField
      name="amount"
      render={({ field }) => (
        <FormItem>
          <FormLabel>المبلغ (ج.م)</FormLabel>
          <FormControl>
            <Input
              type="number"
              step="0.01"
              placeholder="أدخل المبلغ"
              disabled={loading}
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
