
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface SimplePaginationProps {
  currentPage: number;
  totalPages: number;
  onNextPage: () => void;
  onPreviousPage: () => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export function SimplePagination({
  currentPage,
  totalPages,
  onNextPage,
  onPreviousPage,
  hasNextPage,
  hasPreviousPage
}: SimplePaginationProps) {
  const isMobile = useIsMobile();

  return (
    <div className="flex justify-center items-center gap-4 mt-2 w-full">
      <Button
        variant="outline"
        size={isMobile ? "sm" : "default"}
        onClick={onPreviousPage}
        disabled={!hasPreviousPage}
        className="flex items-center gap-1"
      >
        <ChevronLeft className="h-4 w-4" />
        {!isMobile && "السابق"}
      </Button>

      <span className="text-sm font-medium">
        الصفحة {currentPage} من {totalPages}
      </span>

      <Button
        variant="outline"
        size={isMobile ? "sm" : "default"}
        onClick={onNextPage}
        disabled={!hasNextPage}
        className="flex items-center gap-1"
      >
        {!isMobile && "التالي"}
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
