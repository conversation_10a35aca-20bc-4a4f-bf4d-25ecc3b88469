
import { TableCell } from "@/components/ui/table";

interface TransactionAmountCellProps {
  amount: number;
  transactionType: string;
}

export function TransactionAmountCell({ amount, transactionType }: TransactionAmountCellProps) {
  // تنسيق المبلغ باستخدام الأرقام العربية
  const formattedAmount = new Intl.NumberFormat("ar-EG", {
    style: "decimal",
    maximumFractionDigits: 0,
  }).format(amount);

  return (
    <TableCell
      className={`rtl-cell ${
        transactionType === "receive"
          ? "text-green-600 dark:text-green-400"
          : "text-red-600 dark:text-red-400"
      }`}
    >
      <div className="cell-content font-medium">
        {transactionType === "receive" ? "+" : "-"}
        {" ج.م "} 
        {formattedAmount}
      </div>
    </TableCell>
  );
}
