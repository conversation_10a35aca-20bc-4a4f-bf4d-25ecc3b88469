
import { 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormMessage 
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface StatusFieldProps {
  loading: boolean;
}

export function StatusField({ loading }: StatusFieldProps) {
  return (
    <FormField
      name="status"
      render={({ field }) => (
        <FormItem>
          <FormLabel>الحالة</FormLabel>
          <Select
            disabled={loading}
            onValueChange={field.onChange}
            value={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="اختر الحالة" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="pending">مستحق</SelectItem>
              <SelectItem value="overdue">متأخر</SelectItem>
              <SelectItem value="paid">تم الدفع</SelectItem>
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
