
import { supabase } from "@/integrations/supabase/client";
import { TransactionNotification, LowBalanceSim } from './types';
import { 
  formatTransactionNotification, 
  formatLowBalanceNotification
} from './formatNotificationMessages';
import { logNotification } from './notificationSettings';

// Export Fawry notification functions from the new file
export { 
  sendFawryOperationNotification, 
  sendLowBalanceFawryNotification 
} from './fawryNotifications';

/**
 * إرسال إشعار معاملة إلى تليجرام
 */
export const sendTransactionNotification = async (
  botToken: string,
  chatId: string,
  transaction: TransactionNotification
): Promise<boolean> => {
  try {
    console.log(`📤 Sending transaction notification to ${chatId}`);
    
    // تنسيق معرف الدردشة - إزالة @ إذا كان موجودًا
    const formattedChatId = chatId.startsWith('@') 
      ? chatId.substring(1) 
      : chatId;
    
    // تنسيق وإرسال الإشعار
    const message = formatTransactionNotification(transaction);
    
    console.log('Invoking sendTelegramMessage edge function');
    const { data, error } = await supabase.functions.invoke('sendTelegramMessage', {
      body: {
        botToken,
        chatId: formattedChatId,
        message
      }
    });
    
    if (error) {
      console.error('Error from sendTelegramMessage edge function:', error);
      throw error;
    }
    
    console.log('Edge function response:', data);
    
    // تسجيل الإشعار
    const shortMessage = `إشعار معاملة مالية: ${transaction.transaction_type} - ${transaction.amount} ج.م`;
    await logNotification(chatId, shortMessage, 'sent');
    
    return true;
  } catch (error) {
    console.error('Error sending transaction notification:', error);
    
    // تسجيل فشل الإشعار
    await logNotification(chatId, 'فشل في إرسال إشعار المعاملة', 'failed');
    
    return false;
  }
};

/**
 * إرسال إشعار انخفاض الرصيد إلى تليجرام
 */
export const sendLowBalanceNotification = async (
  botToken: string,
  chatId: string,
  sim: LowBalanceSim,
  minBalance: number
): Promise<boolean> => {
  try {
    console.log(`📤 Sending low balance notification to ${chatId} for SIM ${sim.number}`);
    
    // تنسيق معرف الدردشة - إزالة @ إذا كان موجودًا
    const formattedChatId = chatId.startsWith('@') 
      ? chatId.substring(1) 
      : chatId;
    
    // تنسيق وإرسال الإشعار
    const message = formatLowBalanceNotification(sim, minBalance);
    
    const { data, error } = await supabase.functions.invoke('sendTelegramMessage', {
      body: {
        botToken,
        chatId: formattedChatId,
        message
      }
    });
    
    if (error) {
      console.error('Error from sendTelegramMessage edge function:', error);
      throw error;
    }
    
    console.log('Edge function response:', data);
    
    // تسجيل الإشعار
    const shortMessage = `تنبيه انخفاض رصيد للشريحة ${sim.number}`;
    await logNotification(chatId, shortMessage, 'sent');
    
    return true;
  } catch (error) {
    console.error('Error sending low balance notification:', error);
    
    // تسجيل فشل الإشعار
    await logNotification(chatId, 'فشل في إرسال إشعار انخفاض الرصيد', 'failed');
    
    return false;
  }
};
