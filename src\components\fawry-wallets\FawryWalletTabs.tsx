
import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";
import FawryWalletForm from "@/components/fawry-wallets/FawryWalletForm";
import FawryWalletList from "@/components/fawry-wallets/FawryWalletList";
import FawryOperations from "@/components/fawry-wallets/FawryOperations";

interface FawryWalletTabsProps {
  isAdminOrDeveloper: boolean;
  refreshKey: number;
  onWalletAdded: () => void;
  onOperationAdded: () => void;
}

const FawryWalletTabs = ({ 
  isAdminOrDeveloper, 
  refreshKey, 
  onWalletAdded, 
  onOperationAdded 
}: FawryWalletTabsProps) => {
  const [activeTab, setActiveTab] = useState("operations");
  const isMobile = useIsMobile();

  return (
    <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full mt-6">
      <TabsList className={`grid w-full ${isMobile ? 'grid-cols-3 gap-1' : 'max-w-md grid-cols-3'} mb-6 md:mb-8`}>
        {isAdminOrDeveloper && (
          <TabsTrigger value="add">إضافة محفظة</TabsTrigger>
        )}
        {isAdminOrDeveloper && (
          <TabsTrigger value="wallets">المحافظ</TabsTrigger>
        )}
        <TabsTrigger value="operations" className={isAdminOrDeveloper ? '' : 'col-span-3'}>العمليات</TabsTrigger>
      </TabsList>
      
      {isMobile && <div className="mb-4 text-sm text-muted-foreground my-[43px]">
        {activeTab === "add" && "إضافة محفظة فوري جديدة مع البيانات الأساسية"}
        {activeTab === "wallets" && "عرض المحافظ المضافة وتفاصيلها"}
        {activeTab === "operations" && "عرض وإضافة العمليات على محافظ فوري"}
      </div>}
      
      {isAdminOrDeveloper && (
        <TabsContent value="add" className="space-y-4">
          <Card className="p-6">
            <FawryWalletForm onWalletAdded={onWalletAdded} />
          </Card>
        </TabsContent>
      )}
      
      {isAdminOrDeveloper && (
        <TabsContent value="wallets" className="space-y-6">
          <FawryWalletList refreshKey={refreshKey} />
        </TabsContent>
      )}

      <TabsContent value="operations" className="space-y-6">
        <FawryOperations refreshKey={refreshKey} onOperationAdded={onOperationAdded} />
      </TabsContent>
    </Tabs>
  );
};

export default FawryWalletTabs;
