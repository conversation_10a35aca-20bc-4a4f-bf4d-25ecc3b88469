
import { useState, useEffect } from "react";
import { TransactionFormValues } from "@/components/transactions/transactionSchema";

interface ValidationProps {
  simId: string;
  transactionType: string;
  amount: number;
  selectedSimBalance: number | null;
  isMobileSimType: boolean;
  sims: any[];
}

/**
 * Hook for handling transaction validation logic
 */
export function useTransactionValidation({
  simId,
  transactionType,
  amount,
  selectedSimBalance,
  isMobileSimType,
  sims,
}: ValidationProps) {
  const [insufficientBalance, setInsufficientBalance] = useState(false);
  const [isInactiveSim, setIsInactiveSim] = useState(false);
  const [isZeroBalance, setIsZeroBalance] = useState(false);
  const [exceedsLimit, setExceedsLimit] = useState(false);

  // Check for validation issues
  useEffect(() => {
    if (simId) {
      const selectedSim = sims.find(sim => sim.id === simId);

      // Check for inactive SIM
      setIsInactiveSim(selectedSim?.active_status === false);

      // Check for zero balance
      setIsZeroBalance(selectedSim?.balance === 0);

      // Check for insufficient balance in send transaction
      if (transactionType === 'send' && selectedSim?.balance !== undefined && amount > 0) {
        // تعديل هنا: نتحقق فقط إذا كان المبلغ أكبر من الرصيد المتاح وليس أكبر من أو يساوي
        setInsufficientBalance(selectedSim.balance < amount && selectedSim.balance > 0);
      } else {
        setInsufficientBalance(false);
      }

      // Check for mobile receive limit
      if (transactionType === 'receive' && isMobileSimType && amount > 300000) {
        setExceedsLimit(true);
      } else {
        setExceedsLimit(false);
      }
    } else {
      setIsInactiveSim(false);
      setIsZeroBalance(false);
      setInsufficientBalance(false);
      setExceedsLimit(false);
    }
  }, [transactionType, amount, simId, sims, isMobileSimType]);

  /**
   * Check if the sim has insufficient balance for the transaction
   */
  const hasInsufficientBalance = (commission: number): boolean => {
    if (transactionType === 'send' && selectedSimBalance !== null) {
      const totalAmount = Number(amount) + Number(commission);
      // تعديل هنا: نتحقق إذا كان المبلغ الكلي (المبلغ + العمولة) أكبر من الرصيد المتاح
      return selectedSimBalance < totalAmount;
    }
    return false;
  };
  
  /**
   * Check if the transaction exceeds the mobile receive limit
   */
  const exceedsMobileReceiveLimit = (): boolean => {
    return transactionType === 'receive' && 
           isMobileSimType && 
           Number(amount) > 300000;
  };
  
  return {
    insufficientBalance,
    isInactiveSim,
    isZeroBalance,
    exceedsLimit,
    hasInsufficientBalance,
    exceedsMobileReceiveLimit
  };
}
