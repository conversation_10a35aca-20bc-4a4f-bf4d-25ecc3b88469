
import React from "react";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import OperationTypeIcon from "./OperationTypeIcon";
import { Operation } from "../utils/operationSchema";

export const getOperationsTableColumns = () => [
  {
    header: "نوع العملية",
    accessor: (row: Operation) => (
      <div className="flex items-center">
        <OperationTypeIcon type={row.operation_type} />
        <span>{row.operation_type}</span>
      </div>
    ),
    mobile: true,
  },
  {
    header: "المبلغ (جنيه)",
    accessor: (row: Operation) => `${row.amount.toLocaleString('ar-EG')}`,
    mobile: true,
  },
  {
    header: "العمولة (جنيه)",
    accessor: (row: Operation) => `${row.commission?.toLocaleString('ar-EG') || "0"}`,
  },
  {
    header: "المحفظة",
    accessor: (row: Operation) => row.wallet_id || "",
  },
  {
    header: "التاريخ",
    accessor: (row: Operation) => format(new Date(row.created_at), 'yyyy-MM-dd HH:mm', { locale: ar }),
  },
];
