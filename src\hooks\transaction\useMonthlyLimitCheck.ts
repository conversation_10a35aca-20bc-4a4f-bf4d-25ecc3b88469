
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook for checking monthly transaction limits
 */
export function useMonthlyLimitCheck() {
  const [isCheckingLimit, setIsCheckingLimit] = useState(false);
  const [monthlyTotal, setMonthlyTotal] = useState(0);
  const [monthlyRemaining, setMonthlyRemaining] = useState(300000);
  const [monthlyLimitExceeded, setMonthlyLimitExceeded] = useState(false);
  const [receiveLimit, setReceiveLimit] = useState(300000);

  /**
   * Checks the monthly limit for a SIM and determines if a transaction would exceed it
   */
  const checkMonthlyLimits = async (simId: string, transactionAmount: number) => {
    setIsCheckingLimit(true);
    try {
      // Get the SIM's receive_limit
      const { data: simData, error: simError } = await supabase
        .from('sims')
        .select('receive_limit')
        .eq('id', simId)
        .single();
      
      if (simError) {
        throw simError;
      }
      
      // Use the SIM's receive_limit or default to 300,000
      const limit = simData?.receive_limit || 300000;
      setReceiveLimit(limit);
      
      // Get the current month's first and last day
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1).toISOString();
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString();
      
      // Get all receive transactions for this SIM in the current month
      const { data, error } = await supabase
        .from('transactions')
        .select('amount')
        .eq('sim_id', simId)
        .eq('transaction_type', 'receive')
        .gte('created_at', firstDay)
        .lte('created_at', lastDay);
      
      if (error) {
        throw error;
      }
      
      // Calculate total received amount
      const total = data?.reduce((sum, tx) => sum + Number(tx.amount), 0) || 0;
      setMonthlyTotal(total);
      
      // Calculate remaining amount before hitting the limit
      const remaining = limit - total;
      setMonthlyRemaining(Math.max(0, remaining));
      
      // Check if limit is already exceeded
      const limitExceeded = total >= limit;
      setMonthlyLimitExceeded(limitExceeded);
      
      // Check if current transaction would exceed the limit
      if (remaining < transactionAmount) {
        setMonthlyLimitExceeded(true);
      }
      
      return {
        total,
        remaining: Math.max(0, remaining),
        exceeds: limitExceeded || remaining < transactionAmount,
        limit
      };
    } catch (error) {
      console.error('Error checking monthly limits:', error);
      return {
        total: 0,
        remaining: 300000,
        exceeds: false,
        limit: 300000,
        error
      };
    } finally {
      setIsCheckingLimit(false);
    }
  };

  return {
    isCheckingLimit,
    monthlyTotal,
    monthlyRemaining,
    monthlyLimitExceeded,
    receiveLimit,
    checkMonthlyLimits
  };
}
