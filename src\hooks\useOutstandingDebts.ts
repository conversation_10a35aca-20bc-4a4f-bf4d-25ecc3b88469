
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface Debt {
  id: string;
  customer_name: string;
  amount: number;
  due_date: string | null;
  status: string;
  days_overdue: number | null;
}

export function useOutstandingDebts() {
  const [debts, setDebts] = useState<Debt[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchOutstandingDebts = async () => {
    try {
      setIsLoading(true);
      
      // Get current date
      const today = new Date();
      
      // استعلام لجلب الديون المستحقة والمتأخرة
      console.log("Fetching outstanding and overdue debts...");
      const { data, error } = await supabase
        .from('debts')
        .select(`
          id,
          amount,
          status,
          due_date,
          customer_id,
          customers(name)
        `)
        .in('status', ['pending', 'overdue'])
        .order('due_date', { ascending: true });
      
      console.log("Fetched data:", data);
      console.log("Any error:", error);
      
      if (error) throw error;
      
      if (data && data.length > 0) {
        // Process data to calculate days overdue
        const processedData = data.map(debt => {
          let daysOverdue = null;
          let status = debt.status;
          
          if (debt.due_date) {
            const dueDate = new Date(debt.due_date);
            // تحديد ما إذا كان الدين متأخرًا بناءً على التاريخ
            if (dueDate < today) {
              const diffTime = Math.abs(today.getTime() - dueDate.getTime());
              daysOverdue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
              status = "overdue"; // تعيين حالة متأخر إذا كان تاريخ الاستحقاق قد مر
            }
          }
          
          return {
            id: debt.id,
            customer_name: debt.customers?.name || 'غير معروف',
            amount: debt.amount,
            due_date: debt.due_date,
            status: status,
            days_overdue: daysOverdue
          };
        });
        
        // ترتيب البيانات بحيث تظهر الديون المتأخرة أولاً ثم الديون المستحقة
        const sortedDebts = processedData.sort((a, b) => {
          // الديون المتأخرة أولاً
          if (a.status === 'overdue' && b.status !== 'overdue') return -1;
          if (a.status !== 'overdue' && b.status === 'overdue') return 1;
          
          // ثم ترتيب حسب عدد أيام التأخير (الأكثر تأخيرًا أولاً)
          if (a.days_overdue && b.days_overdue) return b.days_overdue - a.days_overdue;
          
          // ثم ترتيب حسب تاريخ الاستحقاق
          if (a.due_date && b.due_date) return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
          
          return 0;
        });
        
        console.log("Processed and sorted debts:", sortedDebts);
        setDebts(sortedDebts);
      } else {
        console.log("No pending or overdue debts found");
        setDebts([]);
      }
    } catch (error) {
      console.error("Error fetching outstanding debts:", error);
      toast.error("حدث خطأ أثناء جلب الديون المستحقة");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOutstandingDebts();
    
    // Set up a polling mechanism instead of real-time subscription
    const pollingInterval = setInterval(() => {
      console.log("Polling for debt updates...");
      fetchOutstandingDebts();
    }, 30000); // Poll every 30 seconds
      
    // Clean up the interval
    return () => {
      clearInterval(pollingInterval);
    };
  }, []);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', { 
      style: 'decimal', 
      maximumFractionDigits: 0 
    }).format(amount);
  };

  return {
    debts,
    isLoading,
    formatCurrency
  };
}
