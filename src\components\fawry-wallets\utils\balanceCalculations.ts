
/**
 * Calculates the balance change for an operation
 */
export const calculateBalanceChange = (operationType: string, amount: number): number => {
  return operationType === "استلام" ? amount : -amount;
};

/**
 * Validates if a wallet balance will be sufficient after an operation
 */
export const validateSufficientBalance = (currentBalance: number, amountToDeduct: number): boolean => {
  return currentBalance - amountToDeduct >= 0;
};

/**
 * Calculates new balance after removing original impact and adding new impact
 */
export const calculateNewBalance = (
  currentBalance: number, 
  oldBalanceChange: number, 
  newBalanceChange: number
): number => {
  return currentBalance - oldBalanceChange + newBalanceChange;
};
