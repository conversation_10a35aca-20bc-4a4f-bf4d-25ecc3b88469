
import { TransactionNotification, LowBalanceSim, <PERSON><PERSON><PERSON><PERSON>alletOperation } from './types';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

/**
 * Format transaction notification message for Telegram
 */
export const formatTransactionNotification = (transaction: TransactionNotification): string => {
  // تنسيق التاريخ والوقت
  const date = new Date(transaction.created_at);
  const formattedDate = format(date, 'dd/MM/yyyy', { locale: ar });
  const formattedTime = format(date, 'h:mm:ss a', { locale: ar });
  
  // تحديد نوع المعاملة
  const transactionType = transaction.transaction_type === 'receive' ? 'استلام' : 'إرسال';
  
  // تنسيق المبلغ
  const formattedAmount = transaction.transaction_type === 'receive' 
    ? `+ ${transaction.amount}` 
    : `- ${transaction.amount}`;
  
  // بناء رسالة الإشعار
  let message = `🔔 معاملة مالية جديدة 🔔\n\n`;
  message += `نوع العميل: 🙍🏻عادي🙍🏻\n`;
  message += `نوع المعاملة: ${transactionType}\n`;
  message += `العميل: ${transaction.customer_name}\n`;
  
  if (transaction.customer_phone) {
    message += `رقم العميل: ${transaction.customer_phone}\n`;
  }
  
  message += `المبلغ: ${formattedAmount} ج.م\n`;
  
  if (transaction.sim?.number) {
    message += `رقم الشريحة: ${transaction.sim.number}\n`;
  }
  
  if (transaction.sim?.balance !== undefined) {
    message += `رصيد الشريحة: ${transaction.sim.balance} ج.م\n`;
  }
  
  if (transaction.wallet?.name) {
    message += `المحفظة: ${transaction.wallet.name}\n`;
  }
  
  message += `التاريخ: ${formattedDate}\n`;
  message += `الوقت: ${formattedTime}\n`;
  
  if (transaction.description) {
    message += `الملاحظات: ${transaction.description}\n`;
  }
  
  return message;
};

/**
 * Format low balance notification message for Telegram
 */
export const formatLowBalanceNotification = (sim: LowBalanceSim, minBalance: number): string => {
  let message = `⚠️ تنبيه: رصيد شريحة منخفض ⚠️\n\n`;
  message += `رقم الشريحة: ${sim.number}\n`;
  message += `الرصيد الحالي: ${sim.balance} ج.م\n`;
  message += `الحد الأدنى المسموح: ${minBalance} ج.م\n`;
  message += `المحفظة: ${sim.wallet_name}\n\n`;
  message += `يرجى إعادة شحن الشريحة في أقرب وقت ممكن.`;
  
  return message;
};

/**
 * Format Fawry wallet operation notification for Telegram
 */
export const formatFawryOperationNotification = (operation: FawryWalletOperation): string => {
  // تنسيق التاريخ والوقت
  const date = new Date(operation.created_at);
  const formattedDate = format(date, 'dd/MM/yyyy', { locale: ar });
  const formattedTime = format(date, 'h:mm:ss a', { locale: ar });
  
  // تحديد نوع العملية بالعربية
  let operationType = operation.operation_type;
  
  // تنسيق المبلغ (إيجابي للإيداع وسلبي للسحب)
  const isDeposit = operationType === 'إيداع' || operationType === 'استلام';
  const formattedAmount = isDeposit ? `+ ${operation.amount}` : `- ${operation.amount}`;
  
  // بناء رسالة الإشعار
  let message = `💳 عملية في محفظة فوري 💳\n\n`;
  message += `نوع العملية: ${operationType}\n`;
  message += `المبلغ: ${formattedAmount} ج.م\n`;
  
  if (operation.commission > 0) {
    message += `العمولة: ${operation.commission} ج.م\n`;
  }
  
  if (operation.wallet?.name) {
    message += `المحفظة: ${operation.wallet.name}\n`;
    
    if (operation.wallet.balance !== undefined) {
      message += `الرصيد الحالي: ${operation.wallet.balance} ج.م\n`;
    }
  }
  
  message += `التاريخ: ${formattedDate}\n`;
  message += `الوقت: ${formattedTime}\n`;
  
  return message;
};

/**
 * Format low balance Fawry wallet notification for Telegram
 */
export const formatLowBalanceFawryNotification = (
  wallet: { name: string; balance: number },
  minBalance: number
): string => {
  let message = `⚠️ تنبيه: رصيد محفظة فوري منخفض ⚠️\n\n`;
  message += `اسم المحفظة: ${wallet.name}\n`;
  message += `الرصيد الحالي: ${wallet.balance} ج.م\n`;
  message += `الحد الأدنى المسموح: ${minBalance} ج.م\n\n`;
  message += `يرجى إعادة شحن محفظة فوري في أقرب وقت ممكن.`;
  
  return message;
};
