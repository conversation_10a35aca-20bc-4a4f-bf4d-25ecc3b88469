import * as React from "react";
import { cn } from "@/lib/utils";
const Table = React.forwardRef<HTMLTableElement, React.HTMLAttributes<HTMLTableElement> & {
  dir?: string;
}>(({
  className,
  dir,
  ...props
}, ref) => <div className="relative w-full overflow-auto">
    <table ref={ref} className={cn("w-full caption-bottom text-sm", className)} dir={dir} {...props} />
  </div>);
Table.displayName = "Table";
const TableHeader = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(({
  className,
  ...props
}, ref) => <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props} />);
TableHeader.displayName = "TableHeader";
const TableBody = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(({
  className,
  ...props
}, ref) => <tbody ref={ref} className={cn("[&_tr:last-child]:border-0", className)} {...props} />);
TableBody.displayName = "TableBody";
const TableFooter = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(({
  className,
  ...props
}, ref) => <tfoot ref={ref} className={cn("bg-primary font-medium text-primary-foreground", className)} {...props} />);
TableFooter.displayName = "TableFooter";
const TableRow = React.forwardRef<HTMLTableRowElement, React.HTMLAttributes<HTMLTableRowElement>>(({
  className,
  ...props
}, ref) => <tr ref={ref} className={cn("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted", className)} {...props} />);
TableRow.displayName = "TableRow";
const TableHead = React.forwardRef<HTMLTableCellElement, React.ThHTMLAttributes<HTMLTableCellElement>>(({
  className,
  ...props
}, ref) => <th ref={ref} className={cn("h-12 px-4 text-right align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0", className)} {...props} />);
TableHead.displayName = "TableHead";
const TableCell = React.forwardRef<HTMLTableCellElement, React.TdHTMLAttributes<HTMLTableCellElement> & {
  label?: string;
}>(({
  className,
  label,
  ...props
}, ref) => <td ref={ref} className={cn("px-4 py-4 align-middle [&:has([role=checkbox])]:pr-0", className)} data-label={label} {...props} />);
TableCell.displayName = "TableCell";
const TableCaption = React.forwardRef<HTMLTableCaptionElement, React.HTMLAttributes<HTMLTableCaptionElement>>(({
  className,
  ...props
}, ref) => <caption ref={ref} className={cn("mt-4 text-sm text-muted-foreground", className)} {...props} />);
TableCaption.displayName = "TableCaption";

// Mobile responsive card components
interface TableCardProps {
  title: React.ReactNode;
  children: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties; // Add style prop to the interface
}
const TableCard = React.forwardRef<HTMLDivElement, TableCardProps>(({
  title,
  children,
  actions,
  className,
  style,
  ...props
}, ref) => <div ref={ref} className={cn("bg-card rounded-md border shadow-sm overflow-hidden", className)} style={style} // Pass style prop to the div element
{...props}>
    <div className="flex items-center justify-between p-4 border-b">
      
      {actions && <div>{actions}</div>}
    </div>
    <div className="p-4 space-y-3">{children}</div>
  </div>);
TableCard.displayName = "TableCard";
interface TableCardRowProps {
  label: string;
  value: React.ReactNode;
  className?: string;
}
const TableCardRow = React.forwardRef<HTMLDivElement, TableCardRowProps>(({
  label,
  value,
  className,
  ...props
}, ref) => <div ref={ref} className={cn("flex justify-between items-center text-sm", className)} {...props}>
    <span className="text-muted-foreground">{label}</span>
    <span className="font-medium">{value}</span>
  </div>);
TableCardRow.displayName = "TableCardRow";
export { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption, TableCard, TableCardRow };