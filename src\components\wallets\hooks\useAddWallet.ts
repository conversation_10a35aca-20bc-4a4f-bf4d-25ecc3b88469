
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { addWalletCommission } from "@/services/transaction/walletCommissionService";
import { WalletFormValues, walletFormSchema } from "../schemas/walletFormSchema";

export const useAddWallet = (onWalletAdded?: () => void) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<WalletFormValues>({
    resolver: zodResolver(walletFormSchema),
    defaultValues: {
      name: "",
      number: "",
      type: "",
    },
  });

  // تحديد لون المحفظة بناءً على نوعها
  const getWalletColor = (type: string): string => {
    switch (type) {
      case "vodafone_cash": return "red-500";
      case "etisalat_cash": return "green-500";
      case "orange_cash": return "orange-500";
      case "instapay": return "blue-400";
      case "fawry": return "teal-500"; // لون محفظة فوري
      default: return "gray-500";
    }
  };

  const onSubmit = async (values: WalletFormValues): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      // إعداد بيانات المحفظة الجديدة
      const walletData = {
        name: values.name,
        color_class: getWalletColor(values.type),
        // إضافة معرف نوع المحفظة للتمييز بين محفظة فوري وغيرها
        wallet_type: values.type
      };
      
      // إضافة المحفظة إلى قاعدة البيانات
      const { data, error } = await supabase
        .from('wallets')
        .insert(walletData)
        .select()
        .single();
        
      if (error) throw error;
      
      // إضافة عمولة افتراضية للمحفظة الجديدة
      const commissionAdded = await addWalletCommission(values.name);
      
      if (!commissionAdded) {
        toast.warning("تم إضافة المحفظة ولكن فشل إضافة إعدادات العمولة الخاصة بها");
      } else {
        toast.success("تم إضافة المحفظة بنجاح مع إعدادات العمولة");
      }
      
      form.reset();
      
      // استدعاء دالة التحديث إن وجدت
      if (onWalletAdded) onWalletAdded();
      
      return true;
    } catch (error) {
      console.error("خطأ في إضافة المحفظة:", error);
      toast.error("حدث خطأ أثناء إضافة المحفظة");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    form,
    isLoading,
    onSubmit,
  };
};
