
/* تنسيقات بطاقات المعلومات */
.stat-card {
  position: relative;
  border-radius: 0.75rem;
  overflow: visible;
  transform: translateZ(0);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.stat-card-animation {
  position: absolute;
  inset: -2px;
  border-radius: 0.75rem;
  padding: 2px;
  background: linear-gradient(
    -45deg, 
    rgba(239, 68, 68, 0), 
    rgba(239, 68, 68, 0.3), 
    rgba(239, 68, 68, 0.6), 
    rgba(239, 68, 68, 0.3), 
    rgba(239, 68, 68, 0)
  );
  background-size: 400% 400%;
  filter: blur(4px);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease-in-out;
  animation: none;
}

.stat-card:hover .stat-card-animation {
  opacity: 1;
  animation: animateStatBorder 3s ease infinite;
}

@keyframes animateStatBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.dark .stat-card-animation {
  background: linear-gradient(
    -45deg, 
    rgba(255, 215, 0, 0), 
    rgba(255, 215, 0, 0.15), 
    rgba(255, 215, 0, 0.3), 
    rgba(255, 215, 0, 0.15), 
    rgba(255, 215, 0, 0)
  );
  filter: blur(3px);
}

/* تحسين تأثير الزجاج والعمق */
.stat-card::before {
  content: "";
  position: absolute;
  inset: 0;
  z-index: -2;
  border-radius: 0.7rem;
  background: rgba(255, 255, 255, 0.01);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.05);
}

.dark .stat-card::before {
  background: rgba(20, 20, 30, 0.01);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}

/* تأثير الانعكاس الزجاجي */
.stat-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  z-index: -1;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), transparent);
  backdrop-filter: blur(4px);
  border-top-left-radius: 0.7rem;
  border-top-right-radius: 0.7rem;
}

.dark .stat-card::after {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.05), transparent);
}

.stat-card-hover {
  box-shadow: 0 5px 25px 5px rgba(239, 68, 68, 0.15);
  transform: translateY(-2px);
}

.dark .stat-card-hover {
  box-shadow: 0 5px 30px 8px rgba(255, 215, 0, 0.25);
}

/* تحسينات لوضوح التأثيرات */
@media (prefers-color-scheme: light) {
  .stat-card-hover {
    box-shadow: 0 5px 25px 8px rgba(239, 68, 68, 0.2);
  }
}

.dark .stat-card-hover {
  box-shadow: 0 5px 25px 5px rgba(255, 215, 0, 0.3);
}

/* ألوان متنوعة للبطاقات المختلفة */
.stat-card-green.stat-card-hover {
  box-shadow: 0 5px 25px 5px rgba(34, 197, 94, 0.2);
}

.stat-card-amber.stat-card-hover {
  box-shadow: 0 5px 25px 5px rgba(245, 158, 11, 0.2);
}

.stat-card-blue.stat-card-hover {
  box-shadow: 0 5px 25px 5px rgba(59, 130, 246, 0.2);
}

.dark .stat-card-green.stat-card-hover {
  box-shadow: 0 5px 30px 8px rgba(34, 197, 94, 0.25);
}

.dark .stat-card-amber.stat-card-hover {
  box-shadow: 0 5px 30px 8px rgba(245, 158, 11, 0.25);
}

.dark .stat-card-blue.stat-card-hover {
  box-shadow: 0 5px 30px 8px rgba(59, 130, 246, 0.25);
}
