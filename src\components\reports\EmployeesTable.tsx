
import React from "react";
import { EmployeeData, getRoleIcon, formatPercentage } from "@/components/reports/utils/employeeUtils";
import { formatCurrency } from "@/utils/formatters";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";

interface EmployeesTableProps {
  employeesData: EmployeeData[];
  isLoading: boolean;
}

export function EmployeesTable({ employeesData, isLoading }: EmployeesTableProps) {
  console.log("EmployeesTable rendering with", employeesData.length, "employees");

  const columns: TableColumn<EmployeeData>[] = [
    {
      header: "الموظف",
      accessor: "name",
      className: "font-medium"
    },
    {
      header: "المنصب",
      accessor: (employee) => (
        <div className="flex items-center">
          {getRoleIcon(employee.role)}
          <span>{employee.position}</span>
        </div>
      )
    },
    {
      header: "عدد العمليات",
      accessor: "transactions_count"
    },
    {
      header: "إجمالي المبالغ",
      accessor: (employee) => formatCurrency(employee.total_amount)
    },
    {
      header: "متوسط العمولة %",
      accessor: (employee) => formatPercentage(employee.average_commission)
    }
  ];
  
  return (
    <ModernTable
      data={employeesData}
      columns={columns}
      loading={isLoading}
      keyField="id"
      emptyMessage="لا توجد بيانات متاحة للفترة المحددة"
      dir="rtl"
    />
  );
}
