
import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface Wallet {
  id: string;
  name: string;
}

interface DeleteFawryWalletDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  wallet: Wallet;
  onWalletDeleted: () => void;
}

const DeleteFawryWalletDialog: React.FC<DeleteFawryWalletDialogProps> = ({
  isOpen,
  onOpenChange,
  wallet,
  onWalletDeleted,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from("fawry_wallets")
        .delete()
        .eq("id", wallet.id);

      if (error) {
        throw error;
      }

      toast({
        title: "تم الحذف بنجاح",
        description: `تم حذف محفظة ${wallet.name} بنجاح`,
      });

      onOpenChange(false);
      onWalletDeleted();
    } catch (error) {
      console.error("Error deleting wallet:", error);
      toast({
        variant: "destructive",
        title: "خطأ في الحذف",
        description: "حدث خطأ أثناء حذف المحفظة. الرجاء المحاولة مرة أخرى.",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>هل أنت متأكد من حذف هذه المحفظة؟</AlertDialogTitle>
          <AlertDialogDescription>
            هذا الإجراء لا يمكن التراجع عنه. سيتم حذف المحفظة "{wallet.name}" وجميع العمليات المرتبطة بها بشكل نهائي.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>إلغاء</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete} disabled={isDeleting} className="bg-red-600 hover:bg-red-700">
            {isDeleting ? "جاري الحذف..." : "حذف المحفظة"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteFawryWalletDialog;
