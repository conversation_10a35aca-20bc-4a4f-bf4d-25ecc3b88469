
import { useState, useCallback, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { User } from "@/types/user.types";
import { fetchAllUsers } from "@/services/user/fetchUsersService";
import { checkAdminStatus, createDefaultAdmin } from "@/services/user/adminService";
import { toast } from "sonner";

export function useUserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Fetch all users
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching users...");
      
      const usersData = await fetchAllUsers();
      console.log("Users data fetched:", usersData);
      
      setUsers(usersData);
    } catch (err) {
      console.error("Error fetching users:", err);
      setError(err instanceof Error ? err : new Error("Unknown error fetching users"));
      toast.error("حدث خطأ أثناء استرجاع بيانات المستخدمين");
    } finally {
      setLoading(false);
    }
  }, []);

  // Check if current user is admin
  const checkIsAdmin = useCallback(async (userId: string) => {
    try {
      const isUserAdmin = await checkAdminStatus(userId);
      setIsAdmin(isUserAdmin);
      return isUserAdmin;
    } catch (err) {
      console.error("Error checking admin status:", err);
      return false;
    }
  }, []);

  // Create default admin user if needed
  const handleCreateDefaultAdmin = useCallback(async () => {
    try {
      const success = await createDefaultAdmin();
      return success;
    } catch (err) {
      console.error("Error creating default admin:", err);
      return false;
    }
  }, []);

  return {
    users,
    loading,
    error,
    isAdmin,
    setIsAdmin,
    fetchUsers,
    checkAdminStatus: checkIsAdmin,
    createDefaultAdmin: handleCreateDefaultAdmin
  };
}
