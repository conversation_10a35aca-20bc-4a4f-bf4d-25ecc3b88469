export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      branches: {
        Row: {
          active_status: boolean
          address: string
          created_at: string
          id: string
          manager: string
          name: string
        }
        Insert: {
          active_status?: boolean
          address: string
          created_at?: string
          id?: string
          manager: string
          name: string
        }
        Update: {
          active_status?: boolean
          address?: string
          created_at?: string
          id?: string
          manager?: string
          name?: string
        }
        Relationships: []
      }
      commission_settings: {
        Row: {
          apply_to_all: boolean
          created_at: string
          default_deposit_rate: number
          default_withdrawal_rate: number
          id: string
          manual_commission: boolean | null
          updated_at: string
        }
        Insert: {
          apply_to_all?: boolean
          created_at?: string
          default_deposit_rate?: number
          default_withdrawal_rate?: number
          id?: string
          manual_commission?: boolean | null
          updated_at?: string
        }
        Update: {
          apply_to_all?: boolean
          created_at?: string
          default_deposit_rate?: number
          default_withdrawal_rate?: number
          id?: string
          manual_commission?: boolean | null
          updated_at?: string
        }
        Relationships: []
      }
      customers: {
        Row: {
          created_at: string
          email: string | null
          id: string
          name: string
          notes: string | null
          phone: string | null
        }
        Insert: {
          created_at?: string
          email?: string | null
          id?: string
          name: string
          notes?: string | null
          phone?: string | null
        }
        Update: {
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          notes?: string | null
          phone?: string | null
        }
        Relationships: []
      }
      debts: {
        Row: {
          amount: number
          created_at: string
          customer_id: string
          due_date: string | null
          id: string
          notes: string | null
          paid_at: string | null
          status: string
          transaction_id: string | null
        }
        Insert: {
          amount: number
          created_at?: string
          customer_id: string
          due_date?: string | null
          id?: string
          notes?: string | null
          paid_at?: string | null
          status?: string
          transaction_id?: string | null
        }
        Update: {
          amount?: number
          created_at?: string
          customer_id?: string
          due_date?: string | null
          id?: string
          notes?: string | null
          paid_at?: string | null
          status?: string
          transaction_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "debts_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "debts_transaction_id_fkey"
            columns: ["transaction_id"]
            isOneToOne: false
            referencedRelation: "transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      departments: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      employees: {
        Row: {
          auth_id: string | null
          created_at: string
          department_id: string | null
          email: string | null
          id: string
          name: string
          notes: string | null
          phone: string | null
          role: string
          status: string | null
        }
        Insert: {
          auth_id?: string | null
          created_at?: string
          department_id?: string | null
          email?: string | null
          id?: string
          name: string
          notes?: string | null
          phone?: string | null
          role?: string
          status?: string | null
        }
        Update: {
          auth_id?: string | null
          created_at?: string
          department_id?: string | null
          email?: string | null
          id?: string
          name?: string
          notes?: string | null
          phone?: string | null
          role?: string
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "employees_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
        ]
      }
      fawry_wallet_operations: {
        Row: {
          amount: number
          commission: number | null
          created_at: string | null
          id: string
          operation_type: string
          wallet_id: string | null
        }
        Insert: {
          amount: number
          commission?: number | null
          created_at?: string | null
          id?: string
          operation_type: string
          wallet_id?: string | null
        }
        Update: {
          amount?: number
          commission?: number | null
          created_at?: string | null
          id?: string
          operation_type?: string
          wallet_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fawry_wallet_operations_wallet_id_fkey"
            columns: ["wallet_id"]
            isOneToOne: false
            referencedRelation: "fawry_wallets"
            referencedColumns: ["id"]
          },
        ]
      }
      fawry_wallets: {
        Row: {
          balance: number | null
          created_at: string | null
          id: string
          name: string
          phone_number: string | null
          type: string | null
        }
        Insert: {
          balance?: number | null
          created_at?: string | null
          id?: string
          name: string
          phone_number?: string | null
          type?: string | null
        }
        Update: {
          balance?: number | null
          created_at?: string | null
          id?: string
          name?: string
          phone_number?: string | null
          type?: string | null
        }
        Relationships: []
      }
      notification_logs: {
        Row: {
          created_at: string | null
          id: string
          message: string
          status: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          message: string
          status: string
          type: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          message?: string
          status?: string
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      notification_rules: {
        Row: {
          all_transactions: boolean | null
          balance_alert_enabled: boolean | null
          created_at: string | null
          id: string
          min_balance: number | null
          min_transaction_amount: number | null
          transaction_alert_enabled: boolean | null
          transaction_types: string[] | null
          updated_at: string | null
        }
        Insert: {
          all_transactions?: boolean | null
          balance_alert_enabled?: boolean | null
          created_at?: string | null
          id?: string
          min_balance?: number | null
          min_transaction_amount?: number | null
          transaction_alert_enabled?: boolean | null
          transaction_types?: string[] | null
          updated_at?: string | null
        }
        Update: {
          all_transactions?: boolean | null
          balance_alert_enabled?: boolean | null
          created_at?: string | null
          id?: string
          min_balance?: number | null
          min_transaction_amount?: number | null
          transaction_alert_enabled?: boolean | null
          transaction_types?: string[] | null
          updated_at?: string | null
        }
        Relationships: []
      }
      security_settings: {
        Row: {
          alert_chat_id: string
          created_at: string
          dev_login_alerts: boolean
          id: number
          is_enabled: boolean
          updated_at: string
        }
        Insert: {
          alert_chat_id?: string
          created_at?: string
          dev_login_alerts?: boolean
          id?: number
          is_enabled?: boolean
          updated_at?: string
        }
        Update: {
          alert_chat_id?: string
          created_at?: string
          dev_login_alerts?: boolean
          id?: number
          is_enabled?: boolean
          updated_at?: string
        }
        Relationships: []
      }
      sims: {
        Row: {
          active_status: boolean
          balance: number
          branch: string
          created_at: string
          id: string
          number: string
          receive_limit: number | null
          send_limit: number | null
          wallet_id: string
        }
        Insert: {
          active_status?: boolean
          balance?: number
          branch: string
          created_at?: string
          id?: string
          number: string
          receive_limit?: number | null
          send_limit?: number | null
          wallet_id: string
        }
        Update: {
          active_status?: boolean
          balance?: number
          branch?: string
          created_at?: string
          id?: string
          number?: string
          receive_limit?: number | null
          send_limit?: number | null
          wallet_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "sims_wallet_id_fkey"
            columns: ["wallet_id"]
            isOneToOne: false
            referencedRelation: "wallets"
            referencedColumns: ["id"]
          },
        ]
      }
      special_customers: {
        Row: {
          created_at: string
          id: string
          name: string
          notes: string | null
          phone: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          notes?: string | null
          phone?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          notes?: string | null
          phone?: string | null
        }
        Relationships: []
      }
      supabase_functions_content: {
        Row: {
          content: string
          created_at: string
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      telegram_settings: {
        Row: {
          bot_token: string
          chat_id: string
          created_at: string | null
          id: string
          is_enabled: boolean | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          bot_token: string
          chat_id: string
          created_at?: string | null
          id?: string
          is_enabled?: boolean | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          bot_token?: string
          chat_id?: string
          created_at?: string | null
          id?: string
          is_enabled?: boolean | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      transactions: {
        Row: {
          amount: number
          commission: number
          created_at: string
          customer_id: string | null
          customer_name: string | null
          customer_phone: string | null
          description: string | null
          employee_id: string | null
          id: string
          sim_id: string | null
          transaction_type: string
          wallet_id: string
        }
        Insert: {
          amount: number
          commission?: number
          created_at?: string
          customer_id?: string | null
          customer_name?: string | null
          customer_phone?: string | null
          description?: string | null
          employee_id?: string | null
          id?: string
          sim_id?: string | null
          transaction_type: string
          wallet_id: string
        }
        Update: {
          amount?: number
          commission?: number
          created_at?: string
          customer_id?: string | null
          customer_name?: string | null
          customer_phone?: string | null
          description?: string | null
          employee_id?: string | null
          id?: string
          sim_id?: string | null
          transaction_type?: string
          wallet_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "transactions_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_sim_id_fkey"
            columns: ["sim_id"]
            isOneToOne: false
            referencedRelation: "sims"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_wallet_id_fkey"
            columns: ["wallet_id"]
            isOneToOne: false
            referencedRelation: "wallets"
            referencedColumns: ["id"]
          },
        ]
      }
      user_permissions: {
        Row: {
          created_at: string
          customers: boolean | null
          dashboard: boolean | null
          database_tools: boolean | null
          debts: boolean | null
          id: string
          notification_logs: boolean | null
          reports: boolean | null
          settings: boolean | null
          transactions: boolean | null
          user_id: string
          users: boolean | null
          wallets: boolean | null
        }
        Insert: {
          created_at?: string
          customers?: boolean | null
          dashboard?: boolean | null
          database_tools?: boolean | null
          debts?: boolean | null
          id?: string
          notification_logs?: boolean | null
          reports?: boolean | null
          settings?: boolean | null
          transactions?: boolean | null
          user_id: string
          users?: boolean | null
          wallets?: boolean | null
        }
        Update: {
          created_at?: string
          customers?: boolean | null
          dashboard?: boolean | null
          database_tools?: boolean | null
          debts?: boolean | null
          id?: string
          notification_logs?: boolean | null
          reports?: boolean | null
          settings?: boolean | null
          transactions?: boolean | null
          user_id?: string
          users?: boolean | null
          wallets?: boolean | null
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          created_at: string
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: []
      }
      wallet_commissions: {
        Row: {
          created_at: string
          deposit_rate: number
          id: string
          min_commission: number
          updated_at: string
          wallet_name: string
          withdrawal_rate: number
        }
        Insert: {
          created_at?: string
          deposit_rate: number
          id?: string
          min_commission?: number
          updated_at?: string
          wallet_name: string
          withdrawal_rate: number
        }
        Update: {
          created_at?: string
          deposit_rate?: number
          id?: string
          min_commission?: number
          updated_at?: string
          wallet_name?: string
          withdrawal_rate?: number
        }
        Relationships: []
      }
      wallets: {
        Row: {
          color_class: string | null
          created_at: string
          id: string
          name: string
          sim_count: number | null
          wallet_type: string | null
        }
        Insert: {
          color_class?: string | null
          created_at?: string
          id?: string
          name: string
          sim_count?: number | null
          wallet_type?: string | null
        }
        Update: {
          color_class?: string | null
          created_at?: string
          id?: string
          name?: string
          sim_count?: number | null
          wallet_type?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      clean_notification_logs: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      disable_log_cleanup_job: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_foreign_keys_for_table: {
        Args: { table_name_param: string }
        Returns: {
          column_name: string
          foreign_table_name: string
          foreign_column_name: string
          constraint_name: string
        }[]
      }
      get_indexes_for_table: {
        Args: { table_name_param: string }
        Returns: {
          indexname: string
          indexdef: string
        }[]
      }
      get_primary_keys_for_table: {
        Args: { table_name_param: string }
        Returns: {
          column_name: string
          constraint_name: string
        }[]
      }
      get_table_definition: {
        Args: { table_name: string }
        Returns: {
          column_name: string
          data_type: string
          is_nullable: string
          column_default: string
        }[]
      }
      get_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_schema: string
          table_name: string
        }[]
      }
      has_permission: {
        Args: { _user_id: string; _permission: string }
        Returns: boolean
      }
      has_role: {
        Args: {
          _user_id: string
          _role: Database["public"]["Enums"]["app_role"]
        }
        Returns: boolean
      }
      setup_log_cleanup_job: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
    }
    Enums: {
      app_role: "admin" | "employee" | "developer"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      app_role: ["admin", "employee", "developer"],
    },
  },
} as const
