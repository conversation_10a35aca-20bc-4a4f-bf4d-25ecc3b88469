
/* Support page animations and styling */
.card:hover {
  transform: translateY(-4px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-scale {
  transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(var(--primary), 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(var(--primary), 0.5);
  }
  100% {
    box-shadow: 0 0 5px rgba(var(--primary), 0.3);
  }
}

/* تطبيق تأثير الإضاءة على البطاقات عند التحويم */
.card:hover {
  animation: glow 2s infinite;
}

/* جعل أرقام الهواتف وأسماء المستخدمين باتجاه LTR */
.ltr\:text-left {
  text-align: left;
  direction: ltr;
  display: inline-block;
}

.rtl\:text-right {
  text-align: right;
}

/* تنسيقات خاصة بالتاب باسئلة الشائعة */
.accordion-item {
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

/* تأثيرات زر الاتصال للحث على العمل */
.cta-button {
  position: relative;
  overflow: hidden;
}

.cta-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.cta-button:hover::after {
  transform: translateX(100%);
}

/* تنسيقات لبطاقات الشهادات */
@media (max-width: 768px) {
  .testimonial-card {
    margin-bottom: 1rem;
  }
}

/* تحسينات للتجاوب في الشاشات الصغيرة */
@media (max-width: 640px) {
  .contact-cards {
    grid-template-columns: 1fr;
  }
  
  .testimonial-grid {
    grid-template-columns: 1fr;
  }
}
