
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 min-w-[48px] min-h-[40px] active:scale-[0.98] duration-200",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80 shadow-sm dark:shadow-primary/20",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/80 shadow-sm dark:shadow-destructive/20",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80 dark:hover:bg-secondary",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/70 shadow-sm dark:hover:bg-secondary/60",
        ghost: "hover:bg-accent hover:text-accent-foreground active:bg-accent/80 dark:hover:bg-secondary/70",
        link: "text-primary underline-offset-4 hover:underline",
        // تنسيقات إضافية للأزرار
        flat: "bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground dark:bg-muted/20 dark:hover:bg-muted/40",
        subtle: "bg-primary/10 text-primary hover:bg-primary/20 dark:bg-primary/20 dark:hover:bg-primary/30",
        glass: "bg-background/80 backdrop-blur-sm border border-border/30 hover:bg-background/90 dark:bg-background/50 dark:hover:bg-background/60",
        success: "bg-green-600 text-white hover:bg-green-700 active:bg-green-800 shadow-sm dark:shadow-green-900/20",
      },
      size: {
        default: "h-10 px-4 py-2 text-[14px]",
        sm: "h-9 rounded-md px-3 text-[14px]",
        lg: "h-11 rounded-md px-8 text-[15px]",
        icon: "h-10 w-10",
        mobile: "h-10 w-full px-5 py-2.5 text-[15px]",
        "mobile-sm": "h-9 w-full px-4 py-2 text-[14px]",
        pill: "h-10 px-6 rounded-full",
        "icon-sm": "h-8 w-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
