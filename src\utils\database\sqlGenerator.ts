
/**
 * Utility functions for generating SQL database structure
 */
import { 
  generateEnumTypes, 
  generateTableDefinitions, 
  generateForeignKeyConstraints, 
  generateIndexes
} from './sql';
import { supabase } from "@/integrations/supabase/client";

/**
 * Generates the complete SQL structure for the database
 * @param isRealStructure تحديد ما إذا كان المطلوب هيكل قاعدة البيانات الحقيقية أم لا
 * @returns SQL content as a string
 */
export const generateDatabaseStructure = async (isRealStructure: boolean = false): Promise<string> => {
  const currentDate = new Date().toISOString();
  
  // Start with a header comment
  let sqlContent = `-- Database structure export for project: cash
-- Generated on: ${currentDate}
-- Note: ${isRealStructure ? 'This is a real database structure export' : 'This is a schema-only export'}

`;
  
  console.log('Starting SQL generation for', isRealStructure ? 'real database' : 'schema');
  
  if (isRealStructure) {
    try {
      console.log('Fetching real database structure from Supabase');
      
      // Fetch all tables from the database using the Edge Function
      const { data: edgeData, error: edgeError } = await supabase.functions.invoke(
        "get-database-structure", 
        { body: {} }
      );
        
      if (edgeError) {
        console.error('Error fetching tables:', edgeError);
        throw edgeError;
      }
      
      const tables = edgeData?.data;
      
      if (tables && Array.isArray(tables) && tables.length > 0) {
        sqlContent += `-- Database contains ${tables.length} tables\n\n`;
        
        // For each table, get its structure
        for (const table of tables) {
          const tableName = table.table_name;
          
          // Get table structure using the Edge Function
          const { data: edgeColumnData, error: edgeColumnError } = await supabase.functions.invoke(
            "get-database-structure", 
            { body: { table_name: tableName } }
          );
            
          if (edgeColumnError) {
            console.error(`Error fetching structure for table ${tableName}:`, edgeColumnError);
            continue;
          }
          
          const columns = edgeColumnData?.data;
          
          // Add table creation SQL
          sqlContent += `-- Table: ${tableName}\n`;
          sqlContent += `CREATE TABLE IF NOT EXISTS ${tableName} (\n`;
          
          if (columns && Array.isArray(columns) && columns.length > 0) {
            const columnDefinitions = columns.map((col: any) => 
              `  ${col.column_name} ${col.data_type}${col.is_nullable === 'NO' ? ' NOT NULL' : ''}${col.column_default ? ` DEFAULT ${col.column_default}` : ''}`
            ).join(',\n');
            
            sqlContent += columnDefinitions;
            sqlContent += '\n);\n\n';
            
            // Add indexes and constraints if available - this would need another Edge Function call
            // which we'll implement in the future if needed
          }
        }
      } else {
        sqlContent += `-- No tables found in database\n\n`;
      }
      
    } catch (error) {
      console.error('Error generating real database structure:', error);
      sqlContent += `-- Error occurred while generating real database structure: ${error}\n`;
      sqlContent += `\n-- Using fallback structure instead\n\n`;
      
      // Fallback to default structure
      const enumTypes = generateEnumTypes();
      sqlContent += enumTypes;
      
      const tableDefinitions = generateTableDefinitions();
      sqlContent += tableDefinitions;
      
      const foreignKeys = generateForeignKeyConstraints();
      sqlContent += foreignKeys;
      
      const indexes = generateIndexes();
      sqlContent += indexes;
    }
  } else {
    // Generate sample structure using our utility functions
    const enumTypes = generateEnumTypes();
    sqlContent += enumTypes;
    console.log('Added enum types, length:', enumTypes.length);
    
    const tableDefinitions = generateTableDefinitions();
    sqlContent += tableDefinitions;
    console.log('Added table definitions, length:', tableDefinitions.length);
    
    const foreignKeys = generateForeignKeyConstraints();
    sqlContent += foreignKeys;
    console.log('Added foreign keys, length:', foreignKeys.length);
    
    const indexes = generateIndexes();
    sqlContent += indexes;
    console.log('Added indexes, length:', indexes.length);
  }
  
  // إذا كان المطلوب هو هيكل قاعدة البيانات الحقيقية، نضيف معلومات إضافية
  if (isRealStructure) {
    sqlContent += `\n-- Additional real database structure information\n`;
    sqlContent += `-- This export contains the actual database structure as it exists in production\n`;
    sqlContent += `-- Note: This file can be used to recreate the exact database structure\n`;
  }
  
  sqlContent += `\n-- End of schema export`;
  
  // Verify we have actual content
  console.log('Total SQL content length:', sqlContent.length);
  
  return sqlContent;
};
