
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";
import { useOutstandingDebts } from "@/hooks/useOutstandingDebts";
import { DebtHeader } from "./debts/DebtHeader";
import { DebtList } from "./debts/DebtList";

interface OutstandingDebtsProps {
  navigateToDebts?: () => void;
}

export function OutstandingDebts({ navigateToDebts }: OutstandingDebtsProps) {
  const { debts, isLoading, formatCurrency } = useOutstandingDebts();
  const isMobile = useIsMobile();

  const overdueCount = debts.filter(debt => debt.status === 'overdue').length;

  return (
    <Card className={`dashboard-card dashboard-card-animate overflow-hidden ${isMobile ? 'rtl-mobile-card' : ''}`}>
      <CardHeader className={`bg-gradient-to-r from-red-50 to-white dark:from-gray-800 dark:to-gray-800/70 border-b border-gray-100 dark:border-gray-700 pb-3 ${isMobile ? 'p-3' : ''}`}>
        <DebtHeader 
          debtCount={debts.length} 
          overdueCount={overdueCount}
        />
      </CardHeader>
      <CardContent className={`p-0 max-h-[320px] sm:max-h-[360px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 dark:scrollbar-thumb-gray-700 ${isMobile ? 'max-h-[250px]' : ''}`}>
        <DebtList 
          debts={debts} 
          isLoading={isLoading}
          formatCurrency={formatCurrency}
          navigateToDebts={navigateToDebts}
        />
      </CardContent>
    </Card>
  );
}
