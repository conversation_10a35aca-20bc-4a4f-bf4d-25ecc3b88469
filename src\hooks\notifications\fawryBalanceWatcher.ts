
import { supabase } from "@/integrations/supabase/client";
import { fetchNotificationRules, fetchTelegramSettings } from './notificationSettings';
import { sendLowBalanceFawryNotification } from './sendNotifications';
import { trackLowBalanceNotification } from './utils/notificationTracker';

/**
 * مراقبة أرصدة محافظ فوري والإشعار بانخفاض الرصيد
 */
export const checkFawryWalletBalances = async (): Promise<void> => {
  try {
    console.log('🔍 Checking Fawry wallet balances');
    
    // الحصول على قواعد الإشعار
    const rules = await fetchNotificationRules();
    
    if (!rules || !rules.balance_alert_enabled) {
      console.log('❌ Balance alerts disabled in notification rules');
      return;
    }
    
    // الحصول على إعدادات تليجرام
    const telegramSettings = await fetchTelegramSettings();
    
    if (!telegramSettings || !telegramSettings.is_enabled) {
      console.log('❌ Telegram notifications disabled in settings');
      return;
    }
    
    console.log('✅ Checking for low balance Fawry wallets (threshold:', rules.min_balance, ')');
    
    // الحصول على جميع محافظ فوري ذات الرصيد المنخفض
    const { data: lowBalanceWallets, error } = await supabase
      .from('fawry_wallets')
      .select('id, name, balance')
      .lt('balance', rules.min_balance);
    
    if (error) {
      console.error('Error fetching low balance Fawry wallets:', error);
      throw error;
    }
    
    if (!lowBalanceWallets || lowBalanceWallets.length === 0) {
      console.log('✅ No low balance Fawry wallets found');
      return;
    }
    
    console.log(`⚠️ Found ${lowBalanceWallets.length} low balance Fawry wallets`);
    
    // إرسال إشعار لكل محفظة ذات رصيد منخفض (مع تجنب التكرار)
    for (const wallet of lowBalanceWallets) {
      // التحقق من إمكانية إرسال الإشعار (تجاوز فترة الانتظار)
      if (!trackLowBalanceNotification(wallet.id)) {
        continue;
      }
      
      console.log(`📤 Sending low balance notification for wallet ${wallet.name} (${wallet.balance})`);
      
      try {
        await sendLowBalanceFawryNotification(
          telegramSettings.bot_token,
          telegramSettings.chat_id,
          wallet,
          rules.min_balance
        );
      } catch (notificationError) {
        console.error(`Error sending low balance notification for wallet ${wallet.name}:`, notificationError);
      }
    }
  } catch (error) {
    console.error('Error checking fawry wallet balances:', error);
  }
};
