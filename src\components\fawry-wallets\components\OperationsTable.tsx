
import { ModernTable } from "@/components/ui/modern-table";
import { formatCurrency } from "@/utils/formatters";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import OperationTypeIcon from "./OperationTypeIcon";

interface Operation {
  id: string;
  operation_type: string;
  amount: number;
  commission: number;
  created_at: string;
  wallet_id: string;
  wallet_name?: string;
}

interface OperationsTableProps {
  operations: Operation[];
  loading: boolean;
}

const OperationsTable: React.FC<OperationsTableProps> = ({
  operations,
  loading,
}) => {
  if (operations.length === 0 && !loading) {
    return <div className="p-4 text-center">لا توجد عمليات</div>;
  }

  // Define columns in the required order: wallet, amount, type, commission, date
  const columns = [
    {
      header: "المحفظة",
      accessor: (row: Operation) => row.wallet_name || "—",
      mobile: true,
    },
    {
      header: "المبلغ",
      accessor: (row: Operation) => {
        // Change color based on operation type
        const textColor = row.operation_type === "استلام" 
          ? "text-green-600 dark:text-green-400" 
          : "text-red-600 dark:text-red-400";
        
        return (
          <span dir="ltr" className={`text-right ${textColor} font-medium`}>
            {formatCurrency(row.amount)}
          </span>
        );
      },
      mobile: true,
      className: "font-medium",
    },
    {
      header: "النوع",
      accessor: (row: Operation) => (
        <div className="flex items-center gap-2">
          <OperationTypeIcon type={row.operation_type} />
          {row.operation_type}
        </div>
      ),
      mobile: true,
    },
    {
      header: "العمولة",
      accessor: (row: Operation) => (
        <span dir="ltr" className="text-right text-green-600 dark:text-green-400 font-medium">
          {formatCurrency(row.commission)}
        </span>
      ),
      mobile: true,
    },
    {
      header: "التاريخ",
      accessor: (row: Operation) => format(new Date(row.created_at), "dd/MM/yyyy, hh:mm:ss a", { locale: ar }),
      mobile: true,
    },
  ];

  return (
    <div className="p-1 sm:p-4">
      <ModernTable
        data={operations}
        columns={columns}
        loading={loading}
        emptyMessage="لا توجد عمليات"
        keyField="id"
        dir="rtl"
      />
    </div>
  );
};

export default OperationsTable;
