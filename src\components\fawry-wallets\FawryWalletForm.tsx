
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

const walletFormSchema = z.object({
  name: z.string().min(1, "اسم المحفظة مطلوب"),
  phone_number: z.string().min(1, "رقم الهاتف مطلوب"),
  balance: z.coerce.number().optional(),
});

type WalletFormValues = z.infer<typeof walletFormSchema>;

interface FawryWalletFormProps {
  onWalletAdded?: () => void;
}

const FawryWalletForm: React.FC<FawryWalletFormProps> = ({ onWalletAdded }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<WalletFormValues>({
    resolver: zodResolver(walletFormSchema),
    defaultValues: {
      name: "",
      phone_number: "",
      balance: undefined,
    },
  });

  const onSubmit = async (values: WalletFormValues) => {
    setIsSubmitting(true);

    try {
      const { error } = await supabase.from("fawry_wallets").insert({
        name: values.name,
        phone_number: values.phone_number,
        balance: values.balance || 0,
      });

      if (error) {
        throw error;
      }

      toast({
        title: "تمت الإضافة بنجاح",
        description: `تم إضافة محفظة ${values.name} بنجاح`,
      });

      form.reset();
      
      if (onWalletAdded) {
        onWalletAdded();
      }
    } catch (error) {
      console.error("Error adding wallet:", error);
      toast({
        variant: "destructive",
        title: "خطأ في الإضافة",
        description: "حدث خطأ أثناء إضافة المحفظة. الرجاء المحاولة مرة أخرى.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>اسم المحفظة</FormLabel>
              <FormControl>
                <Input placeholder="أدخل اسم المحفظة" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>رقم الهاتف</FormLabel>
              <FormControl>
                <Input placeholder="رقم الهاتف" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="balance"
          render={({ field }) => (
            <FormItem>
              <FormLabel>رصيد المحفظة (اختياري)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  {...field}
                  onChange={(e) => {
                    const value = e.target.value === "" ? undefined : e.target.value;
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full"
          disabled={isSubmitting}
        >
          {isSubmitting ? "جاري الإضافة..." : "إضافة المحفظة"}
        </Button>
      </form>
    </Form>
  );
};

export default FawryWalletForm;
