
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { DebtsList } from "@/components/debts/DebtsList";
import { DebtFilterBar } from "@/components/debts/DebtFilterBar";
import { Debt } from "@/types/debt.types";

interface DebtListTabProps {
  debts: Debt[];
  loading: boolean;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  onDebtUpdated: () => void;
}

export function DebtListTab({ 
  debts, 
  loading, 
  filterStatus, 
  setFilterStatus, 
  onDebtUpdated 
}: DebtListTabProps) {
  // Filter debts based on status
  const filteredDebts = debts.filter(debt => {
    if (filterStatus === "all") return true;
    if (filterStatus === "pending") return debt.status === "pending";
    if (filterStatus === "overdue") {
      return debt.status === "overdue" || 
             (debt.due_date && new Date(debt.due_date) < new Date() && debt.status !== "paid");
    }
    if (filterStatus === "paid") return debt.status === "paid";
    return true;
  });

  return (
    <div className="golden-border-card relative overflow-hidden animate-fade-in transition-all duration-300">
      {/* تأثير التوهج الذهبي المتحرك */}
      <div className="golden-border-animation absolute inset-0 z-0"></div>
      
      <Card className="relative z-10 border-0 overflow-hidden golden-glow animate-fade-in">
        <CardHeader className="flex flex-col items-start space-y-4">
          <CardTitle>قائمة الديون</CardTitle>
          <DebtFilterBar
            filterStatus={filterStatus}
            setFilterStatus={setFilterStatus}
          />
        </CardHeader>
        <CardContent>
          <DebtsList 
            debts={filteredDebts} 
            loading={loading} 
            onDebtUpdated={onDebtUpdated}
          />
        </CardContent>
      </Card>
    </div>
  );
}
