
import { supabase } from "@/integrations/supabase/client";
import { fetchNotificationRules, fetchTelegramSettings } from './notificationSettings';
import { sendTransactionNotification } from './sendNotifications';
import { TransactionNotification } from './types';

/**
 * Hook for sending notifications related to transactions
 */
export function useNotificationSender() {
  
  /**
   * Check if a transaction should trigger a notification based on rules
   */
  const shouldSendNotificationForTransaction = async (transaction: any, rules: any) => {
    // Check transaction type against rules
    const transactionType = transaction.transaction_type;
    if (rules.transaction_types && rules.transaction_types.length > 0 && !rules.transaction_types.includes(transactionType)) {
      console.log(`❌ نوع المعاملة ${transactionType} ليس في قواعد الإشعارات`);
      return false;
    }
    
    // Check amount against minimum threshold
    if (rules.min_transaction_amount > 0 && transaction.amount < rules.min_transaction_amount) {
      console.log(`❌ المبلغ ${transaction.amount} أقل من الحد الأدنى ${rules.min_transaction_amount}`);
      return false;
    }
    
    return true;
  };

  /**
   * Send notification for a transaction
   */
  const sendTransactionAlert = async (transactionId: string): Promise<boolean> => {
    try {
      // Check if transaction notifications are enabled
      const rules = await fetchNotificationRules();
      
      if (!rules || !rules.transaction_alert_enabled) {
        console.log('❌ إشعارات المعاملات معطلة في الإعدادات');
        return false;
      }
      
      // Check if telegram notifications are enabled
      const telegramSettings = await fetchTelegramSettings();
      
      if (!telegramSettings || !telegramSettings.is_enabled) {
        console.log('❌ إشعارات تليجرام معطلة في الإعدادات');
        return false;
      }
      
      // Fetch full transaction data
      const { data: transaction } = await supabase
        .from('transactions')
        .select(`
          *,
          customer:customers(*),
          wallet:wallets(*),
          sim:sims(*)
        `)
        .eq('id', transactionId)
        .single();
      
      if (!transaction) {
        console.log('❌ لم يتم العثور على المعاملة في قاعدة البيانات');
        return false;
      }
      
      // Check transaction against notification rules
      if (!await shouldSendNotificationForTransaction(transaction, rules)) {
        return false;
      }
      
      console.log('📤 إرسال إشعار بالمعاملة');
      
      // Make sure transaction_type is one of the allowed values
      const validTransactionType = transaction.transaction_type === 'receive' || transaction.transaction_type === 'send' 
        ? (transaction.transaction_type as 'receive' | 'send') 
        : 'receive'; // Safe default value
      
      // Send notification with properly typed transaction data
      const notificationData: TransactionNotification = {
        ...transaction,
        transaction_type: validTransactionType
      };
      
      await sendTransactionNotification(
        telegramSettings.bot_token,
        telegramSettings.chat_id,
        notificationData
      );
      
      return true;
    } catch (error) {
      console.error('Error sending transaction notification:', error);
      return false;
    }
  };

  return {
    sendTransactionAlert
  };
}
