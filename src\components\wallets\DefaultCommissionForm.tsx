import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { saveDefaultCommissionSettings } from "./commissionsUtils";
import { DefaultCommissionFormValues } from "./commissionsTypes";
import { Percent } from "lucide-react";

interface DefaultCommissionFormProps {
  defaultValues: DefaultCommissionFormValues;
  onSave: (data: DefaultCommissionFormValues) => void;
}

const DefaultCommissionForm = ({ 
  defaultValues,
  onSave 
}: DefaultCommissionFormProps) => {
  const [formValues, setFormValues] = useState<DefaultCommissionFormValues>({
    defaultDepositRate: defaultValues.defaultDepositRate,
    defaultWithdrawalRate: defaultValues.defaultWithdrawalRate,
    applyToAll: defaultValues.applyToAll
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // تحديث القيم عند تغير القيم الافتراضية
  useEffect(() => {
    setFormValues({
      defaultDepositRate: defaultValues.defaultDepositRate,
      defaultWithdrawalRate: defaultValues.defaultWithdrawalRate,
      applyToAll: defaultValues.applyToAll
    });
  }, [defaultValues]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    setFormValues((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : parseFloat(value) || 0
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // حفظ في قاعدة البيانات
      await saveDefaultCommissionSettings({
        defaultDepositRate: formValues.defaultDepositRate,
        defaultWithdrawalRate: formValues.defaultWithdrawalRate,
        applyToAll: formValues.applyToAll
      });
      
      // إعلام المكون الأب لتحديث الإعدادات الأخرى
      onSave(formValues);
    } catch (error) {
      console.error("Error saving default commission settings:", error);
      toast.error("حدث خطأ أثناء حفظ إعدادات العمولة");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Percent className="h-5 w-5" />
            نسبة العمولة الأساسية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              هذه النسبة سيتم تطبيقها على جميع العمليات ما لم يتم تحديد نسبة مخصصة للمحفظة
            </p>
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="defaultDepositRate">الإيداع (%)</Label>
              <Input
                id="defaultDepositRate"
                name="defaultDepositRate"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={formValues.defaultDepositRate}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="defaultWithdrawalRate">السحب (%)</Label>
              <Input
                id="defaultWithdrawalRate"
                name="defaultWithdrawalRate"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={formValues.defaultWithdrawalRate}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="applyToAll"
              name="applyToAll"
              checked={formValues.applyToAll}
              onCheckedChange={(checked) =>
                setFormValues({ ...formValues, applyToAll: checked })
              }
            />
            <Label htmlFor="applyToAll" className="mr-2">
              تطبيق على جميع المحافظ
            </Label>
          </div>

          <Button type="submit" className="w-full sm:w-auto" disabled={isSubmitting}>
            {isSubmitting ? "جاري الحفظ..." : "حفظ الإعدادات"}
          </Button>
        </CardContent>
      </Card>
    </form>
  );
};

export default DefaultCommissionForm;
