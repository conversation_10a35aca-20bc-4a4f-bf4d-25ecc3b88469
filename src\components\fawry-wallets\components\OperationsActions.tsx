
import React from "react";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface OperationsActionsProps {
  isAdminOrDeveloper: boolean;
  onAddOperation: () => void;
  onDeleteAllOperations: () => void;
}

const OperationsActions: React.FC<OperationsActionsProps> = ({
  isAdminOrDeveloper,
  onAddOperation,
  onDeleteAllOperations
}) => {
  const isMobile = useIsMobile();
  
  return (
    <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'} ${isMobile ? 'gap-2 w-full rtl-actions-group-mobile' : 'gap-2'}`}>
      {isAdminOrDeveloper && (
        <Button 
          variant="destructive" 
          onClick={onDeleteAllOperations}
          className={`flex items-center gap-1 ${isMobile ? 'w-full text-sm py-2' : ''}`}
          size={isMobile ? "mobile-sm" : "default"}
        >
          <Trash2 size={isMobile ? 14 : 16} />
          حذف جميع العمليات
        </Button>
      )}
      <Button 
        onClick={onAddOperation}
        className={isMobile ? 'w-full text-sm py-2' : ''}
        size={isMobile ? "mobile-sm" : "default"}
      >
        إضافة عملية جديدة
      </Button>
    </div>
  );
};

export default OperationsActions;
