
import { TableRow } from "@/components/ui/table";
import { Transaction } from "@/types/transaction.types";
import {
  TransactionCustomerCell,
  TransactionStatusCell,
  TransactionAmountCell,
  TransactionCommissionCell,
  TransactionTypeCell,
  TransactionWalletCell,
  TransactionDateCell,
  TransactionDescriptionCell
} from "./row";

interface TransactionRowProps {
  transaction: Transaction;
}

export function TransactionRow({ transaction }: TransactionRowProps) {
  // Default to "completed" since we don't have status in the Transaction interface
  const status = "completed";
  
  return (
    <TableRow key={transaction.id}>
      <TransactionCustomerCell 
        customer={transaction.customer} 
        customerName={transaction.customer_name}
        customerPhone={transaction.customer_phone}
      />
      <TransactionStatusCell status={status} />
      <TransactionAmountCell amount={transaction.amount} transactionType={transaction.transaction_type} />
      <TransactionCommissionCell commission={transaction.commission} />
      <TransactionTypeCell transactionType={transaction.transaction_type} />
      <TransactionWalletCell wallet={transaction.wallet} />
      <TransactionDateCell date={transaction.created_at} />
      <TransactionDescriptionCell description={transaction.description} />
    </TableRow>
  );
}
