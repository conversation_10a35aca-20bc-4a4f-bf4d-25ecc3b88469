
// Store last sent notification times to avoid duplicates
const notificationTracker = new Map<string, number>();

/**
 * Track notification to prevent duplicates within a time window
 * @param key Unique identifier for the notification
 * @param timeWindow Time window in ms to prevent duplicate notifications
 * @returns true if notification should be sent, false if it's a duplicate
 */
export const trackNotification = (key: string, timeWindow = 30000): boolean => {
  const now = Date.now();
  const lastSent = notificationTracker.get(key) || 0;
  
  // If notification was sent within the time window, don't send again
  if (now - lastSent < timeWindow) {
    console.log(`⏱️ Skipping duplicate notification for ${key}, last sent ${Math.round((now - lastSent)/1000)}s ago`);
    return false;
  }
  
  // Update tracker with current time
  notificationTracker.set(key, now);
  return true;
};

/**
 * Track low balance notification to prevent duplicates
 * @param id Wallet or SIM ID
 * @returns true if notification should be sent, false if it's a duplicate
 */
export const trackLowBalanceNotification = (id: string): boolean => {
  // Use 1 hour window for low balance notifications
  return trackNotification(`low_balance_${id}`, 60 * 60 * 1000);
};
