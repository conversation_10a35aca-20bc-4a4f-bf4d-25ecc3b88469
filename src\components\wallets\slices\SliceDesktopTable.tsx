
import React from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Edit, Trash2, ToggleLeft, ToggleRight } from "lucide-react";
import { Slice } from "@/types/slice.types";
import { Badge } from "@/components/ui/badge";

interface SliceDesktopTableProps {
  slices: Slice[];
  isLoading: boolean;
  onEdit: (slice: Slice) => void;
  onDelete: (slice: Slice) => void;
  onToggleStatus: (id: string, currentStatus: boolean) => void;
  getBranchName: (branchId: string) => string;
}

export const SliceDesktopTable: React.FC<SliceDesktopTableProps> = ({
  slices,
  isLoading,
  onEdit,
  onDelete,
  onToggleStatus,
  getBranchName
}) => {
  return (
    <div className="modern-table-container animate-fade-in transition-all duration-300 elegant-table">
      <Table dir="rtl" className="rtl-table modern-table-gradient">
        <TableHeader>
          <TableRow className="bg-muted/20 border-b-2 border-amber-500/20 dark:border-amber-400/30">
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">رقم الشريحة</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">الفرع</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">رصيد الشريحة</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">حد الإرسال</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">حد الاستقبال</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">الحالة</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">تفعيل/تعطيل</TableHead>
            <TableHead className="rtl-header whitespace-nowrap py-3 font-bold text-right pr-4">إجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-4">
                جاري تحميل البيانات...
              </TableCell>
            </TableRow>
          ) : slices.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-4">
                لا توجد شرائح مسجلة
              </TableCell>
            </TableRow>
          ) : (
            slices.map((slice, index) => (
              <TableRow 
                key={slice.id} 
                className="hover:bg-amber-50/30 dark:hover:bg-amber-900/10 transition-all duration-200 animate-slide-in debt-card-hover"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <TableCell className="rtl-cell font-medium">
                  <div className="cell-content">
                    <div className="font-medium">{slice.walletName}</div>
                    <div className="text-xs">{slice.number}</div>
                  </div>
                </TableCell>
                <TableCell className="rtl-cell">
                  <div className="cell-content">{getBranchName(slice.branch)}</div>
                </TableCell>
                <TableCell className="rtl-cell">
                  <div className="cell-content">{slice.balance.toLocaleString()} ج.م</div>
                </TableCell>
                <TableCell className="rtl-cell">
                  <div className="cell-content">{slice.send_limit ? `${slice.send_limit.toLocaleString()} ج.م` : "غير محدد"}</div>
                </TableCell>
                <TableCell className="rtl-cell">
                  <div className="cell-content">{slice.receive_limit ? `${slice.receive_limit.toLocaleString()} ج.م` : "غير محدد"}</div>
                </TableCell>
                <TableCell className="rtl-cell">
                  <div className="cell-content">
                    <Badge variant={slice.active_status ? "default" : "outline"}>
                      {slice.active_status ? "نشط" : "غير نشط"}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell className="rtl-cell">
                  <div className="cell-content flex justify-center">
                    <Button
                      variant={slice.active_status ? "success" : "destructive"}
                      size="sm"
                      className="rtl-action-button transition-all duration-200 min-w-[85px] gap-1.5 bg-opacity-100"
                      onClick={() => onToggleStatus(slice.id, slice.active_status)}
                      style={{
                        backgroundColor: slice.active_status ? '#10b981' : '#ef4444',
                        color: 'white',
                        border: 'none'
                      }}
                    >
                      {slice.active_status ? (
                        <>
                          <ToggleRight className="h-4 w-4 ml-1" />
                          <span>مفعّل</span>
                        </>
                      ) : (
                        <>
                          <ToggleLeft className="h-4 w-4 ml-1" />
                          <span>معطّل</span>
                        </>
                      )}
                    </Button>
                  </div>
                </TableCell>
                <TableCell className="rtl-cell">
                  <div className="cell-content flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit(slice)}
                      className="flex items-center gap-1 text-blue-600"
                    >
                      <Edit className="h-4 w-4" />
                      تعديل
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDelete(slice)}
                      className="flex items-center gap-1 text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                      حذف
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};
