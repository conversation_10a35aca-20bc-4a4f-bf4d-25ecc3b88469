
@keyframes toast-enter {
  0% {
    opacity: 0;
    transform: translateY(-16px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes toast-exit {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-16px) scale(0.9);
  }
}

/* Toast styles */
.sonner-toast {
  animation: toast-enter 0.35s cubic-bezier(0.21, 1.02, 0.73, 1) forwards;
}

.sonner-toast[data-dismissing="true"] {
  animation: toast-exit 0.4s cubic-bezier(0.06, 0.71, 0.55, 1) forwards;
}

/* Toast types styling */
.toast-success {
  border-left: 4px solid hsl(var(--success)) !important;
}

.toast-error {
  border-left: 4px solid hsl(var(--destructive)) !important;
}

.toast-warning {
  border-left: 4px solid hsl(var(--warning)) !important;
}

.toast-info {
  border-left: 4px solid hsl(var(--primary)) !important;
}

.sonner-toast:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-2px);
  transition: all 0.2s ease;
}

/* Glass effect on hover */
.sonner-toast:hover {
  backdrop-filter: blur(8px);
  background: rgba(var(--background-rgb), 0.8) !important;
}

/* Close button improvements */
.sonner-toast-close-button {
  border-radius: 50%;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease;
  opacity: 1 !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
  visibility: visible !important;
}

.sonner-toast-close-button:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
  transform: scale(1.1);
}

.sonner-toast-close-button svg {
  width: 16px !important;
  height: 16px !important;
}

/* RTL specific adjustments */
[dir="rtl"] .sonner-toast {
  flex-direction: row-reverse;
  padding-right: 16px;
  padding-left: 40px;
}

[dir="rtl"] .sonner-toast-close-button {
  left: 8px;
  right: auto;
}

