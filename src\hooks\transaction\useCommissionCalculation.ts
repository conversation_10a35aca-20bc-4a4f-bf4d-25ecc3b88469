
import { useState } from 'react';
import { calculateCommission } from '@/services/transaction/transactionService';
import { TransactionType } from '@/utils/transaction/types';

/**
 * Hook للتعامل مع حسابات العمولة
 */
export function useCommissionCalculation() {
  const [isCalculating, setIsCalculating] = useState(false);

  /**
   * حساب العمولة بناءً على المبلغ، ونوع المعاملة، والمحفظة
   */
  const handleCalculateCommission = async (
    amount: number,
    transactionType: TransactionType,
    walletId: string,
    isFawryWallet: boolean,
    setFormCommission: (value: number) => void
  ) => {
    if (amount <= 0 || !walletId) return 0;

    setIsCalculating(true);
    try {
      // إذا كانت محفظة فوري، لا تقم بالحساب التلقائي، واترك المستخدم يدخل يدويًا
      if (isFawryWallet) {
        console.log('Fawry wallet detected, commission should be entered manually');
        setIsCalculating(false);
        return 0;
      }

      console.log('Calculating commission for amount:', amount, 'type:', transactionType, 'wallet:', walletId);
      const calculatedCommission = await calculateCommission(
        amount,
        transactionType,
        walletId
      );
      
      console.log('Calculated commission:', calculatedCommission);
      setFormCommission(calculatedCommission);
      return calculatedCommission;
    } catch (error) {
      console.error('Error calculating commission:', error);
      return 0;
    } finally {
      setIsCalculating(false);
    }
  };

  return {
    isCalculating,
    handleCalculateCommission
  };
}
