
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import { HelpCircle, Phone, MessageSquare, Users } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

// Import our new components
import { SupportHeader } from "@/components/support/SupportHeader";
import { IntroCard } from "@/components/support/IntroCard";
import { ContactTabContent } from "@/components/support/ContactTabContent";
import { FAQSection } from "@/components/support/FAQSection";
import { TestimonialsSection } from "@/components/support/TestimonialsSection";
import { CallToAction } from "@/components/support/CallToAction";

const Support = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("contact");

  // Function to handle opening external links
  const openExternalLink = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };
  
  // Function to scroll to contact section
  const scrollToContact = () => {
    setActiveTab("contact"); // Set the active tab to contact
    const contactSection = document.getElementById("contact-section");
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <div className="container mx-auto p-4 md:p-6 animate-fade-in">
      {/* Header Section */}
      <SupportHeader user={user} />
      
      {/* Hero Card with Introduction */}
      <IntroCard />
      
      {/* Tabs Navigation */}
      <Tabs defaultValue={activeTab} className="mb-8" onValueChange={setActiveTab} value={activeTab}>
        <TabsList className="mb-6 w-full md:w-auto grid grid-cols-3 gap-2">
          <TabsTrigger value="contact" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            <span className="hidden sm:inline">تواصل معنا</span>
          </TabsTrigger>
          <TabsTrigger value="faq" className="flex items-center gap-2">
            <HelpCircle className="h-4 w-4" />
            <span className="hidden sm:inline">أسئلة شائعة</span>
          </TabsTrigger>
          <TabsTrigger value="testimonials" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">آراء العملاء</span>
          </TabsTrigger>
        </TabsList>
        
        {/* Contact Tab */}
        <TabsContent value="contact" className="animate-fade-in" id="contact-section">
          <ContactTabContent openExternalLink={openExternalLink} />
        </TabsContent>
        
        {/* FAQ Tab */}
        <TabsContent value="faq" className="space-y-6 animate-fade-in">
          <FAQSection />
        </TabsContent>
        
        {/* Testimonials Tab */}
        <TabsContent value="testimonials" className="animate-fade-in">
          <TestimonialsSection scrollToContact={scrollToContact} />
        </TabsContent>
      </Tabs>
      
      {/* Call to Action */}
      <CallToAction scrollToContact={scrollToContact} />
    </div>
  );
};

export default Support;
