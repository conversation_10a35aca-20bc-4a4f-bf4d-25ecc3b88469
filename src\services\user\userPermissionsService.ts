
import { supabase } from "@/integrations/supabase/client";
import { UserPermissions, User } from "@/types/user.types";

// Process and complement edge function data with permissions
export const processEdgeUsersData = async (usersData: any[]): Promise<User[]> => {
  try {
    // Get all user permissions
    const { data: userPermissions, error: permissionsError } = await supabase
      .from("user_permissions")
      .select("*");
      
    if (permissionsError) {
      throw permissionsError;
    }
    
    return usersData.map(user => {
      const userPermission = userPermissions?.find(p => p.user_id === user.id);
      
      return {
        id: user.id,
        email: user.email || user.id,
        role: user.role || "employee",
        permissions: userPermission ? {
          dashboard: userPermission.dashboard || false,
          transactions: userPermission.transactions || false,
          customers: userPermission.customers || false,
          debts: userPermission.debts || false,
          reports: userPermission.reports || false,
          notification_logs: userPermission.notification_logs || false,
          wallets: userPermission.wallets || false,
          users: userPermission.users || false,
          settings: userPermission.settings || false,
          database_tools: userPermission.database_tools || false
        } : getDefaultPermissions(),
        created_at: user.created_at || new Date().toISOString(),
        last_sign_in_at: user.last_sign_in_at || null,
        is_active: user.is_active !== undefined ? user.is_active : true
      };
    });
  } catch (error) {
    console.error("Error processing edge users data:", error);
    throw error;
  }
};

// Get default empty permissions object
export const getDefaultPermissions = (): UserPermissions => ({
  dashboard: false,
  transactions: false,
  customers: false,
  debts: false,
  reports: false,
  notification_logs: false,
  wallets: false,
  users: false,
  settings: false,
  database_tools: false
});

// Update a user's permissions in the database
export const updateUserPermissions = async (
  userId: string, 
  permissions: UserPermissions
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("user_permissions")
      .upsert({
        user_id: userId,
        ...permissions
      });
      
    if (error) throw error;
    return true;
  } catch (error) {
    console.error("Error updating user permissions:", error);
    return false;
  }
};

// Check if a user has a specific permission
export const checkUserPermission = async (
  userId: string,
  permission: keyof UserPermissions
): Promise<boolean> => {
  try {
    // أولا، تحقق من دور المستخدم
    // Fix: Instead of using .single() which causes 406 errors, use regular query and check the results
    const { data: roleData, error: roleError } = await supabase
      .from("user_roles")
      .select("role")
      .eq("user_id", userId)
      .eq("role", "admin");
      
    if (roleError) {
      console.error("Error checking user role:", roleError);
    }
    
    // إذا كان المستخدم مطورًا أو مديرًا، امنحه جميع الصلاحيات
    if (roleData && roleData.length > 0) {
      console.log(`User is admin, automatically granting ${permission} permission`);
      return true;
    }
    
    // Check if user is a developer
    const { data: devData, error: devError } = await supabase
      .from("user_roles")
      .select("role")
      .eq("user_id", userId)
      .eq("role", "developer");
      
    if (devError) {
      console.error("Error checking developer role:", devError);
    }
    
    if (devData && devData.length > 0) {
      console.log(`User is developer, automatically granting ${permission} permission`);
      return true;
    }
    
    // إذا لم يكن مديرًا أو مطورًا، تحقق من الصلاحيات المحددة
    const { data, error } = await supabase
      .from("user_permissions")
      .select(permission)
      .eq("user_id", userId)
      .single();
    
    if (error) {
      console.error(`Error checking ${permission} permission:`, error);
      return false;
    }
    
    return !!data?.[permission];
  } catch (error) {
    console.error(`Error checking ${permission} permission:`, error);
    return false;
  }
};

/**
 * Gets the default permissions for a role
 */
export const getPermissionsByRole = (role: string): UserPermissions => {
  // Empty permissions object, all set to false
  const emptyPermissions: UserPermissions = {
    dashboard: false,
    transactions: false,
    reports: false,
    wallets: false,
    customers: false,
    debts: false,
    users: false,
    settings: false,
    database_tools: false,
    notification_logs: false
  };
  
  // Admin gets all permissions
  if (role === 'admin' || role === 'developer') {
    return Object.keys(emptyPermissions).reduce((acc, key) => {
      acc[key as keyof UserPermissions] = true;
      return acc;
    }, { ...emptyPermissions });
  }
  
  // Manager gets most permissions except sensitive ones
  if (role === 'manager') {
    return {
      ...emptyPermissions,
      dashboard: true,
      transactions: true,
      reports: true,
      wallets: true,
      customers: true,
      debts: true,
    };
  }
  
  // Employee gets only basic permissions
  if (role === 'employee') {
    return {
      ...emptyPermissions,
      dashboard: true,
      transactions: true,
    };
  }
  
  // Default is no permissions if role doesn't match
  return emptyPermissions;
};
