
import { useState } from 'react';
import { toast } from 'sonner';
import { submitTransaction } from '@/utils/transactionUtils';
import { TransactionType } from '@/utils/transaction/types';
import { useNotificationSender } from '@/hooks/notifications/useNotificationSender';
import { useB<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/hooks/notifications/useBalanceChecker';

interface TransactionData {
  amount: number;
  commission: number;
  wallet_id: string;
  sim_id: string;
  type: TransactionType;
  customer_name: string;
  customer_phone?: string;
  description?: string;
}

/**
 * Hook for handling transaction submission
 */
export function useTransactionSubmission() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { sendTransactionAlert } = useNotificationSender();
  const { checkSimBalance } = useBalanceChecker();

  /**
   * Submit transaction to the database
   */
  const submitTransactionData = async (transactionData: TransactionData): Promise<boolean> => {
    setIsSubmitting(true);
    try {
      console.log("Transaction data before submission:", transactionData);
      const result = await submitTransaction(transactionData);
      
      if (result && result.transactionId) {
        // Send transaction notification
        await sendTransactionAlert(result.transactionId);
        
        // Check SIM balance after successful transaction
        if (transactionData.sim_id) {
          await checkSimBalance(transactionData.sim_id);
        }
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error submitting transaction:', error);
      toast.error('حدث خطأ أثناء إضافة العملية');
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSubmitting,
    submitTransactionData
  };
}
