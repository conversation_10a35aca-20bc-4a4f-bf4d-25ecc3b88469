
import { ContactMethods } from "./ContactMethods";
import { ServiceInfo } from "./ServiceInfo";
import { SupportAbout } from "./SupportAbout";

interface ContactTabContentProps {
  openExternalLink: (url: string) => void;
}

export function ContactTabContent({ openExternalLink }: ContactTabContentProps) {
  return (
    <>
      <ContactMethods openExternalLink={openExternalLink} />
      <ServiceInfo />
      <SupportAbout />
    </>
  );
}
