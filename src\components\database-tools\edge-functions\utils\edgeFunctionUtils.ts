
import { supabase } from "@/integrations/supabase/client";

/**
 * Fetches the code for a specific edge function
 */
export const getFunctionCode = async (functionName: string) => {
  try {
    console.log(`Fetching code for ${functionName}...`);
    const { data, error } = await supabase.functions.invoke("fetch-function-code", {
      body: { functionName }
    });
    
    if (error) throw error;
    
    if (!data || !data.code || data.code.trim().length < 10 || 
        data.code.includes("No code available") || 
        data.code.includes("This is a placeholder")) {
      
      console.log(`Function ${functionName} returned placeholder code, using predefined code if available...`);
      
      // For securityAlert, we have predefined code available
      if (functionName === "securityAlert") {
        return getSecurityAlertCode();
      }
      
      throw new Error(`Could not retrieve valid code for ${functionName}`);
    }
    
    return data.code;
  } catch (error) {
    console.error(`Error fetching ${functionName} code:`, error);
    throw error;
  }
};

/**
 * Returns the predefined code for the securityAlert function
 */
export const getSecurityAlertCode = () => {
  return `import { serve } from "https://deno.land/std@0.177.0/http/server.ts";

interface SecurityAlertData {
  status: "success" | "failed";
  username: string;
  userId: string;
  ip: string;
  timestamp: string;
  role: string;
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }
  
  try {
    // Parse request body
    const { alertData, chatId, botToken } = await req.json();
    
    if (!chatId || !botToken || !alertData) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Missing required parameters: chatId, botToken, or alertData"
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
      );
    }
    
    const data = alertData as SecurityAlertData;
    
    // Format Telegram message with appropriate emojis
    const statusEmoji = data.status === "success" ? "✅" : "❌";
    const alertEmoji = data.status === "success" ? "⚠️" : "🚨";
    
    const message = \`
\${alertEmoji} <b>تنبيه أمني: محاولة دخول للوحة المطور</b> \${alertEmoji}

<b>الحالة:</b> \${statusEmoji} \${data.status === "success" ? "ناجحة" : "فاشلة"}
<b>المستخدم:</b> \${data.username}
<b>معرف المستخدم:</b> \${data.userId}
<b>الصلاحية:</b> \${data.role}
<b>عنوان IP:</b> \${data.ip}
<b>الوقت:</b> \${data.timestamp}

\${data.status === "success" ? "🔐" : "🔓"} <b>إذا لم تكن أنت من قام بهذه المحاولة، يرجى تغيير كلمة المرور فوراً!</b>
\`;

    // Process chat ID format properly
    let formattedChatId = chatId;
    
    // Send message using Telegram Bot API
    const apiUrl = \`https://api.telegram.org/bot\${botToken}/sendMessage\`;
    
    console.log(\`Sending security alert to Telegram chat ID: \${formattedChatId}\`);
    
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        chat_id: formattedChatId,
        text: message,
        parse_mode: "HTML", // Support HTML formatting in messages
      }),
    });
    
    // Parse Telegram response
    const telegramResponse = await response.json();
    
    if (telegramResponse.ok) {
      return new Response(
        JSON.stringify({ success: true, data: telegramResponse }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    } else {
      let errorMessage = "فشل في إرسال التنبيه الأمني";
      
      if (telegramResponse.description) {
        errorMessage = telegramResponse.description;
      }
      
      console.error("Telegram API error:", telegramResponse);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: errorMessage, 
          details: telegramResponse 
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
      );
    }
  } catch (error) {
    console.error("Error in securityAlert function:", error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || "An unknown error occurred"
      }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
    );
  }
});`;
};
