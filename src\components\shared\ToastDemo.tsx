
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { useState } from "react";

export function ToastDemo() {
  const [isLoading, setIsLoading] = useState(false);

  const showSuccessToast = () => {
    toast.success({
      title: "تمت العملية بنجاح",
      description: "تم حفظ التغييرات بنجاح"
    });
  };

  const showErrorToast = () => {
    toast.error({
      title: "حدث خطأ",
      description: "تعذر إكمال العملية، يرجى المحاولة مرة أخرى"
    });
  };

  const showWarningToast = () => {
    toast.warning({
      title: "تنبيه",
      description: "يرجى التحقق من البيانات المدخلة قبل الحفظ"
    });
  };

  const showInfoToast = () => {
    toast.info({
      title: "معلومات",
      description: "تم تحديث البيانات في الخلفية"
    });
  };

  const showPromiseToast = async () => {
    setIsLoading(true);
    
    toast.promise(
      // Simulating an API call
      new Promise((resolve, reject) => {
        setTimeout(() => {
          Math.random() > 0.3 ? resolve("تم تنفيذ العملية") : reject(new Error("فشل في الاتصال"));
        }, 2000);
      }),
      {
        loading: "جاري تنفيذ العملية...",
        success: () => "تم تنفيذ العملية بنجاح!",
        error: (err) => `فشلت العملية: ${err.message}`
      }
    ).finally(() => setIsLoading(false));
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>عرض توضيحي للإشعارات</CardTitle>
        <CardDescription>اختر نوع الإشعار الذي تريد عرضه</CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col gap-3">
        <Button onClick={showSuccessToast} variant="default" className="bg-green-500 hover:bg-green-600">
          إشعار نجاح
        </Button>
        <Button onClick={showErrorToast} variant="destructive">
          إشعار خطأ
        </Button>
        <Button onClick={showWarningToast} className="bg-amber-500 hover:bg-amber-600 text-white">
          إشعار تحذير
        </Button>
        <Button onClick={showInfoToast} variant="outline" className="border-blue-500 text-blue-500 hover:bg-blue-50">
          إشعار معلومات
        </Button>
        <Button onClick={showPromiseToast} disabled={isLoading} className="bg-purple-500 hover:bg-purple-600 text-white">
          {isLoading ? "جاري التنفيذ..." : "إشعار متقدم مع وعد"}
        </Button>
      </CardContent>
    </Card>
  );
}
