
import { TableCell } from "@/components/ui/table";
import { STATUS_COLORS, STATUS_NAMES } from "@/types/transaction.types";

interface TransactionStatusCellProps {
  status: string;
}

export function TransactionStatusCell({ status }: TransactionStatusCellProps) {
  // التأكد من أن الحالة الصحيحة معروضة (مكتملة بدلاً من معلقة)
  const displayStatus = "completed";
  
  return (
    <TableCell className="rtl-cell">
      <div className="cell-content">
        <span className={`px-2 py-1 rounded-full text-xs ${STATUS_COLORS[displayStatus]}`}>
          {STATUS_NAMES[displayStatus]}
        </span>
      </div>
    </TableCell>
  );
}
