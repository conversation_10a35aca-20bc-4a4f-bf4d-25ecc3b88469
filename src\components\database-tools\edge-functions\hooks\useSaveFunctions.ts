
import { useState } from "react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { EdgeFunction } from "../types";
import { getFunctionCode } from "../utils/edgeFunctionUtils";

export const useSaveFunctions = () => {
  const [isSaving, setIsSaving] = useState(false);
  
  const handleSaveAllFunctions = async (edgeFunctions: EdgeFunction[]) => {
    setIsSaving(true);
    const results = { success: 0, failed: 0, total: edgeFunctions.length };
    
    try {
      // Process each function sequentially
      for (const func of edgeFunctions) {
        try {
          // Try to get function code
          const functionCode = await getFunctionCode(func.name);
          
          if (!functionCode || functionCode.trim().length < 10) {
            console.error(`Invalid code for ${func.name}`);
            results.failed++;
            continue;
          }
          
          // Save to database
          const { error } = await supabase
            .from('supabase_functions_content')
            .upsert({
              name: func.name,
              content: functionCode,
              updated_at: new Date().toISOString()
            }, {
              onConflict: 'name'
            });
            
          if (error) {
            console.error(`Error saving ${func.name} to database:`, error);
            results.failed++;
          } else {
            console.log(`Successfully saved ${func.name} to database`);
            results.success++;
          }
        } catch (funcError) {
          console.error(`Failed to process function ${func.name}:`, funcError);
          results.failed++;
        }
        
        // Small delay to avoid overwhelming the API
        await new Promise(r => setTimeout(r, 500));
      }
      
      // Show results
      if (results.failed === 0) {
        toast.success(`تم حفظ جميع الوظائف (${results.success} من ${results.total}) في قاعدة البيانات بنجاح`);
      } else {
        toast.warning(`تم حفظ ${results.success} وظائف بنجاح. فشل حفظ ${results.failed} وظائف. راجع السجل لمزيد من التفاصيل.`);
      }
    } catch (error: any) {
      console.error("Error in save all functions operation:", error);
      toast.error(`فشل عملية الحفظ: ${error.message}`);
    } finally {
      setIsSaving(false);
    }
  };
  
  return {
    isSaving,
    handleSaveAllFunctions
  };
};
