
import { WalletCommissionFormValues } from "./commissionsTypes";

// Generate wallet-specific form values from settings
export const mapWalletSettingsToFormValues = (
  settings: any
): WalletCommissionFormValues => {
  const result: Partial<WalletCommissionFormValues> = {};
  
  if (!settings) return {} as WalletCommissionFormValues;
  
  Object.keys(settings).forEach(walletName => {
    const commission = settings[walletName];
    if (commission) {
      result[`${walletName}Deposit` as keyof WalletCommissionFormValues] = commission.deposit_rate || 0;
      result[`${walletName}Withdrawal` as keyof WalletCommissionFormValues] = commission.withdrawal_rate || 1;
      result[`${walletName}MinCommission` as keyof WalletCommissionFormValues] = commission.min_commission || 5;
    }
  });
  
  return result as WalletCommissionFormValues;
};

// Apply default settings to all wallets
export const applyDefaultSettingsToAll = (
  defaultDepositRate: number, 
  defaultWithdrawalRate: number
): WalletCommissionFormValues => {
  return {
    vodafoneDeposit: defaultDepositRate,
    vodafoneWithdrawal: defaultWithdrawalRate,
    vodafoneMinCommission: 5,
    etisalatDeposit: defaultDepositRate,
    etisalatWithdrawal: defaultWithdrawalRate,
    etisalatMinCommission: 5,
    orangeDeposit: defaultDepositRate,
    orangeWithdrawal: defaultWithdrawalRate,
    orangeMinCommission: 5,
    instaPayDeposit: defaultDepositRate,
    instaPayWithdrawal: defaultWithdrawalRate,
    instaPayMinCommission: 5,
  };
};
