
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { fetchTelegramSettings } from "@/hooks/notifications/notificationSettings";
import { sendFawryOperationNotification } from "@/hooks/notifications/fawryNotifications";

interface Operation {
  id: string;
  wallet_id: string;
  operation_type: string;
  amount: number;
  commission?: number; // Make commission optional in our interface
}

interface UseDeleteFawryOperationProps {
  operation: Operation;
  onClose: () => void;
  onOperationDeleted: () => void;
}

export const useDeleteFawryOperation = ({
  operation,
  onClose,
  onOperationDeleted,
}: UseDeleteFawryOperationProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      // 1. التحقق من عملية الإرسال ورصيد المحفظة قبل الحذف
      const { data: walletData, error: walletFetchError } = await supabase
        .from("fawry_wallets")
        .select("balance, name")
        .eq("id", operation.wallet_id)
        .single();

      if (walletFetchError) {
        throw walletFetchError;
      }

      const currentBalance = walletData.balance || 0;
      const balanceChange = operation.operation_type === "استلام" ? -operation.amount : operation.amount;
      const newBalance = currentBalance + balanceChange;
      
      // التحقق من أن الحذف لن يؤدي إلى رصيد سالب
      if (newBalance < 0) {
        toast({
          variant: "destructive",
          title: "لا يمكن حذف العملية",
          description: "حذف هذه العملية سيؤدي إلى رصيد سالب في المحفظة. يرجى مراجعة العمليات الأخرى أولاً.",
        });
        setIsDeleting(false);
        return;
      }

      // 2. تحديث رصيد المحفظة إذا كان الحذف ممكنًا
      const { error: walletUpdateError } = await supabase
        .from("fawry_wallets")
        .update({ balance: newBalance })
        .eq("id", operation.wallet_id);

      if (walletUpdateError) {
        throw walletUpdateError;
      }

      // جلب المزيد من المعلومات عن العملية لإرسال إشعار حذف
      const deletedOperationDetails = {
        ...operation,
        commission: operation.commission || 0, // Add commission with fallback to 0 if not provided
        wallet: walletData,
        created_at: new Date().toISOString(),
        operation_type: `حذف ${operation.operation_type}`
      };

      // 3. حذف العملية
      const { error } = await supabase
        .from("fawry_wallet_operations")
        .delete()
        .eq("id", operation.id);

      if (error) {
        throw error;
      }

      // 4. إرسال إشعار عن حذف العملية إذا كان الإشعارات مفعلة
      try {
        const telegramSettings = await fetchTelegramSettings();
        if (telegramSettings && telegramSettings.is_enabled && telegramSettings.bot_token && telegramSettings.chat_id) {
          await sendFawryOperationNotification(
            telegramSettings.bot_token,
            telegramSettings.chat_id,
            deletedOperationDetails
          );
          console.log("تم إرسال إشعار حذف العملية بنجاح");
        }
      } catch (notificationError) {
        console.error("خطأ في إرسال إشعار حذف العملية:", notificationError);
        // نستمر حتى لو فشل إرسال الإشعار
      }

      toast({
        title: "تم الحذف بنجاح",
        description: "تم حذف العملية بنجاح وتحديث رصيد المحفظة",
      });

      onClose();
      onOperationDeleted();
    } catch (error) {
      console.error("Error deleting operation:", error);
      toast({
        variant: "destructive",
        title: "خطأ في الحذف",
        description: "حدث خطأ أثناء حذف العملية. الرجاء المحاولة مرة أخرى.",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    isDeleting,
    handleDelete
  };
};
