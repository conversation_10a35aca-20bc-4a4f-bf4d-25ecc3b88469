
import { supabase } from "@/integrations/supabase/client";

/**
 * Creates a backup of all database tables and returns the data
 */
export const createDatabaseBackup = async () => {
  try {
    // Fetch data from all relevant tables
    const [
      { data: transactions, error: transactionsError },
      { data: customers, error: customersError },
      { data: wallets, error: walletsError },
      { data: sims, error: simsError },
      { data: debts, error: debtsError },
      { data: employees, error: employeesError },
      { data: branches, error: branchesError },
      { data: departments, error: departmentsError },
      { data: commissionSettings, error: commissionSettingsError },
      { data: walletCommissions, error: walletCommissionsError }
    ] = await Promise.all([
      supabase.from('transactions').select('*'),
      supabase.from('customers').select('*'),
      supabase.from('wallets').select('*'),
      supabase.from('sims').select('*'),
      supabase.from('debts').select('*'),
      supabase.from('employees').select('*'),
      supabase.from('branches').select('*'),
      supabase.from('departments').select('*'),
      supabase.from('commission_settings').select('*'),
      supabase.from('wallet_commissions').select('*')
    ]);

    // Check for errors
    const errors = [
      transactionsError, customersError, walletsError, simsError, debtsError, 
      employeesError, branchesError, departmentsError, commissionSettingsError, walletCommissionsError
    ].filter(Boolean);
    
    if (errors.length > 0) {
      console.error("Errors fetching data:", errors);
      throw new Error("حدث خطأ أثناء استرجاع بيانات النسخة الاحتياطية");
    }

    // Create the backup object with real data
    const backupData = {
      backup_date: new Date().toISOString(),
      version: "1.0",
      tables: {
        transactions,
        customers,
        wallets,
        sims,
        debts,
        employees,
        branches,
        departments,
        commission_settings: commissionSettings,
        wallet_commissions: walletCommissions
      }
    };

    return backupData;
  } catch (error) {
    console.error("Error creating backup:", error);
    throw error;
  }
};

/**
 * Creates a full backup of all project data from the real database
 */
export const createFullProjectBackup = async () => {
  try {
    // Define all tables to fetch data from
    const allTables = [
      'branches',
      'commission_settings',
      'telegram_settings',
      'departments',
      'fawry_wallets',
      'notification_logs',
      'notification_rules',
      'user_permissions',
      'debts',
      'employees',
      'fawry_wallet_operations',
      'security_settings',
      'supabase_functions_content',
      'transactions',
      'user_roles',
      'wallet_commissions',
      'customers',
      'sims',
      'wallets'
    ];
    
    console.log("Starting full database backup of all tables:", allTables.length, "tables");
    
    // Create an object to store all table data
    const allTableData: Record<string, any> = {};
    
    // Fetch data from all tables - one table at a time to avoid timeouts and ensure fresh data
    for (const tableName of allTables) {
      try {
        console.log(`Fetching data from table: ${tableName}`);
        
        // Disable cache for this request to ensure we get fresh data
        // Use timestamp as queryKey to force a new request
        const timestamp = new Date().getTime();
        
        // Use type assertion to handle dynamic table names
        const { data, error } = await supabase
          .from(tableName as any)
          .select('*')
          .order('created_at', { ascending: false })
          .throwOnError();
        
        if (error) {
          console.error(`Error fetching data from ${tableName}:`, error);
          throw error;
        }
        
        // Store the data
        allTableData[tableName] = data || [];
        
        console.log(`Backed up ${data?.length || 0} records from ${tableName}`);
      } catch (tableError) {
        console.error(`Error processing table ${tableName}:`, tableError);
        // Continue with other tables even if one fails
        allTableData[tableName] = [];
      }
    }
    
    // Count successful tables
    const successfulTables = Object.keys(allTableData).filter(tableName => 
      Array.isArray(allTableData[tableName]) && allTableData[tableName].length > 0
    );
    
    console.log("Successfully backed up tables:", successfulTables.length);
    
    // Create the backup object with all project data
    const backupData = {
      backup_date: new Date().toISOString(),
      version: "2.0",
      backup_type: "full_database",
      tables: allTableData,
      meta: {
        tables_count: Object.keys(allTableData).length,
        records_count: Object.values(allTableData).reduce((acc: number, table: any) => 
          acc + (Array.isArray(table) ? table.length : 0), 0),
        timestamp: Date.now(),
        tables_list: Object.keys(allTableData).sort()
      }
    };
    
    console.log("Full database backup created with tables:", Object.keys(allTableData).length);
    return backupData;
  } catch (error) {
    console.error("Error creating full database backup:", error);
    throw error;
  }
};
