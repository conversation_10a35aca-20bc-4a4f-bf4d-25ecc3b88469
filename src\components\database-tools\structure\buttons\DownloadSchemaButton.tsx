
import { Button } from "@/components/ui/button";
import { Database } from "lucide-react";
import { toast } from "sonner";
import { downloadDatabaseStructure } from "@/utils/database/downloadUtils";
import { supabase } from "@/integrations/supabase/client";

interface DownloadSchemaButtonProps {
  isGenerating: boolean;
  setIsGenerating: (isGenerating: boolean) => void;
}

export default function DownloadSchemaButton({ 
  isGenerating, 
  setIsGenerating 
}: DownloadSchemaButtonProps) {
  const handleDownloadCompleteSchema = async () => {
    setIsGenerating(true);
    
    try {
      console.log('Starting complete database schema download');
      
      // Get complete database structure from Edge Function
      const { data: edgeData, error: edgeError } = await supabase.functions.invoke(
        "get-database-structure", 
        { body: { complete: true, include_rls: true } }
      );
      
      if (edgeError) {
        console.error("Error fetching database structure:", edgeError);
        throw edgeError;
      }

      if (!edgeData || !edgeData.data) {
        throw new Error("No data returned from database structure API");
      }
      
      const dbStructure = edgeData.data;
      console.log("Complete database structure received:", dbStructure);
      
      // Generate complete SQL schema file
      let sqlContent = `-- Complete Database Schema Export
-- Generated: ${new Date().toISOString()}
-- Database: Supabase Project Schema
-- Tables: ${dbStructure.tables?.length || 0}
-- Relationships: ${dbStructure.relationships?.length || 0}
-- Primary Keys: ${dbStructure.primaryKeys?.length || 0}
-- Indexes: ${dbStructure.indexes?.length || 0}
-- RLS Policies: ${dbStructure.rlsPolicies?.length || 0}
-- Functions: ${dbStructure.functions?.length || 0}

-- =====================================================
-- This file contains the complete database schema
-- Use this file to recreate the database structure
-- =====================================================

`;

      // Add enum definitions first
      sqlContent += `-- =====================================================\n`;
      sqlContent += `-- ENUM TYPES\n`;
      sqlContent += `-- =====================================================\n\n`;
      sqlContent += `CREATE TYPE app_role AS ENUM ('admin', 'employee', 'developer');\n\n`;

      // Add table definitions
      sqlContent += `-- =====================================================\n`;
      sqlContent += `-- TABLE DEFINITIONS\n`;
      sqlContent += `-- =====================================================\n\n`;
      
      if (dbStructure.tables && dbStructure.tables.length > 0) {
        for (const table of dbStructure.tables) {
          sqlContent += `-- Table: ${table.table_name}\n`;
          sqlContent += `CREATE TABLE public.${table.table_name} (\n`;
          
          if (table.columns && table.columns.length > 0) {
            const columnDefs = table.columns.map(col => {
              let colDef = `  ${col.column_name} ${col.data_type}`;
              
              // Handle ARRAY types
              if (col.data_type === 'ARRAY' || col.data_type.includes('ARRAY')) {
                if (col.column_name === 'transaction_types' || col.data_type.toLowerCase().includes('text')) {
                  colDef = `  ${col.column_name} text[]`;
                } else if (col.data_type.toLowerCase().includes('integer')) {
                  colDef = `  ${col.column_name} integer[]`;
                } else if (col.data_type.toLowerCase().includes('numeric')) {
                  colDef = `  ${col.column_name} numeric[]`;
                } else if (col.data_type.toLowerCase().includes('uuid')) {
                  colDef = `  ${col.column_name} uuid[]`;
                } else {
                  colDef = `  ${col.column_name} text[]`;
                }
              }
              
              // Handle USER-DEFINED types (enums)
              if (col.data_type === 'USER-DEFINED') {
                if (col.column_name === 'role' || col.column_default?.includes('app_role')) {
                  colDef = `  ${col.column_name} app_role`;
                } else {
                  const typeMatch = col.column_default?.match(/::([\w_]+)/);
                  if (typeMatch && typeMatch[1]) {
                    colDef = `  ${col.column_name} ${typeMatch[1]}`;
                  } else if (col.udt_name) {
                    colDef = `  ${col.column_name} ${col.udt_name}`;
                  } else {
                    colDef = `  ${col.column_name} text`;
                  }
                }
              }
              
              // Add constraints
              if (col.is_nullable === 'NO') colDef += ' NOT NULL';
              if (col.column_default) colDef += ` DEFAULT ${col.column_default}`;
              
              return colDef;
            });
            
            sqlContent += columnDefs.join(',\n');
          }
          
          sqlContent += '\n);\n\n';
        }
      }
      
      // Add primary keys
      sqlContent += `-- =====================================================\n`;
      sqlContent += `-- PRIMARY KEYS\n`;
      sqlContent += `-- =====================================================\n\n`;
      
      if (dbStructure.primaryKeys && dbStructure.primaryKeys.length > 0) {
        for (const pkTable of dbStructure.primaryKeys) {
          if (pkTable.keys && pkTable.keys.length > 0) {
            const columns = pkTable.keys.map(pk => pk.column_name).join(', ');
            sqlContent += `ALTER TABLE public.${pkTable.table_name} ADD PRIMARY KEY (${columns});\n`;
          }
        }
        sqlContent += '\n';
      }
      
      // Add foreign keys
      sqlContent += `-- =====================================================\n`;
      sqlContent += `-- FOREIGN KEY CONSTRAINTS\n`;
      sqlContent += `-- =====================================================\n\n`;
      
      if (dbStructure.relationships && dbStructure.relationships.length > 0) {
        for (const fkTable of dbStructure.relationships) {
          if (fkTable.foreign_keys && fkTable.foreign_keys.length > 0) {
            for (const fk of fkTable.foreign_keys) {
              sqlContent += `ALTER TABLE public.${fkTable.table_name} ADD CONSTRAINT ${fk.constraint_name} FOREIGN KEY (${fk.column_name}) REFERENCES public.${fk.foreign_table_name}(${fk.foreign_column_name});\n`;
            }
          }
        }
        sqlContent += '\n';
      }
      
      // Add indexes
      sqlContent += `-- =====================================================\n`;
      sqlContent += `-- INDEXES\n`;
      sqlContent += `-- =====================================================\n\n`;
      
      if (dbStructure.indexes && dbStructure.indexes.length > 0) {
        for (const idxTable of dbStructure.indexes) {
          if (idxTable.indexes && idxTable.indexes.length > 0) {
            for (const idx of idxTable.indexes) {
              // Skip primary key indexes as they're automatically created
              if (!idx.indexname.includes('_pkey')) {
                sqlContent += `${idx.indexdef};\n`;
              }
            }
          }
        }
        sqlContent += '\n';
      }
      
      // Add RLS policies
      sqlContent += `-- =====================================================\n`;
      sqlContent += `-- ROW LEVEL SECURITY POLICIES\n`;
      sqlContent += `-- =====================================================\n\n`;
      
      if (dbStructure.rlsPolicies && dbStructure.rlsPolicies.length > 0) {
        for (const rlsTable of dbStructure.rlsPolicies) {
          sqlContent += `-- Enable RLS for table ${rlsTable.table_name}\n`;
          sqlContent += `ALTER TABLE public.${rlsTable.table_name} ENABLE ROW LEVEL SECURITY;\n\n`;
          
          if (rlsTable.policies && rlsTable.policies.length > 0) {
            for (const policy of rlsTable.policies) {
              sqlContent += `-- Policy: ${policy.policy_name}\n`;
              sqlContent += `CREATE POLICY "${policy.policy_name}" ON public.${rlsTable.table_name}\n`;
              sqlContent += `  FOR ${policy.operation}\n`;
              
              if (policy.using_expression) {
                sqlContent += `  USING (${policy.using_expression})\n`;
              }
              
              if (policy.check_expression) {
                sqlContent += `  WITH CHECK (${policy.check_expression})\n`;
              }
              
              sqlContent += `;\n\n`;
            }
          }
        }
      }
      
      // Add database functions
      sqlContent += `-- =====================================================\n`;
      sqlContent += `-- DATABASE FUNCTIONS\n`;
      sqlContent += `-- =====================================================\n\n`;
      
      if (dbStructure.functions && dbStructure.functions.length > 0) {
        for (const func of dbStructure.functions) {
          sqlContent += `${func.definition}\n\n`;
        }
      }
      
      // Add footer
      sqlContent += `-- =====================================================\n`;
      sqlContent += `-- END OF DATABASE SCHEMA\n`;
      sqlContent += `-- File generated: ${new Date().toISOString()}\n`;
      sqlContent += `-- =====================================================\n`;
      
      // Create filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const fileName = `database_schema_${timestamp}.sql`;
      
      // Download the complete schema file
      downloadDatabaseStructure(sqlContent, fileName);
      
      toast.success("تم تحميل ملف هيكل قاعدة البيانات الكامل بنجاح");
    } catch (error) {
      console.error("خطأ في تحميل هيكل قاعدة البيانات:", error);
      toast.error("حدث خطأ أثناء تحميل ملف هيكل قاعدة البيانات");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button 
      onClick={handleDownloadCompleteSchema} 
      disabled={isGenerating}
      className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white font-medium px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 w-full mb-3"
      size="lg"
    >
      <Database className="h-5 w-5" />
      {isGenerating ? "جاري تحميل هيكل قاعدة البيانات..." : "تحميل ملف database_schema.sql"}
    </Button>
  );
}
