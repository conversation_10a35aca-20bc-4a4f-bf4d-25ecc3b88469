
@layer components {
  /* Theme specific components */
  .theme-card {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm transition-all duration-200 ease-in-out;
  }
  
  .theme-sidebar {
    @apply bg-card text-card-foreground border-r transition-all duration-200 ease-in-out;
  }
  
  .theme-header {
    @apply bg-background text-foreground border-b transition-all duration-200 ease-in-out;
  }
  
  .theme-footer {
    @apply bg-muted text-muted-foreground border-t transition-all duration-200 ease-in-out;
  }
  
  /* تنسيقات محسنة للأزرار */
  .theme-button {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80 focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all duration-200 ease-in-out;
  }
  
  .theme-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/70 focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all duration-200 ease-in-out;
  }
  
  .theme-button-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground transition-all duration-200 ease-in-out;
  }
  
  .theme-button-ghost {
    @apply hover:bg-accent hover:text-accent-foreground transition-all duration-200 ease-in-out;
  }
  
  .theme-button-link {
    @apply text-primary underline-offset-4 hover:underline transition-all duration-200 ease-in-out;
  }
  
  .theme-input {
    @apply bg-background text-foreground border rounded-md focus:ring-2 focus:ring-ring transition-all duration-200 ease-in-out;
  }

  /* تنسيقات الأزرار للثيمات */
  .theme-icon-button {
    @apply bg-transparent hover:bg-accent hover:text-accent-foreground rounded-md transition-all duration-200 ease-in-out;
  }
  
  /* الوضع الداكن */
  .dark .theme-icon-button {
    @apply hover:bg-secondary hover:text-secondary-foreground;
  }

  /* أزرار إضافية بالثيمات */
  .theme-action-button {
    @apply rounded-md px-3 py-2 text-sm font-medium shadow-sm bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary transition-all duration-200 ease-in-out;
  }
  
  .dark .theme-action-button {
    @apply shadow-lg shadow-primary/20;
  }
}

/* Special themes per element */
.wallet-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.wallet-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .wallet-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* تحسين أزرار الإجراءات */
.action-btn {
  @apply rounded-md px-3 py-1.5 text-sm font-medium transition-all duration-200 ease-in-out;
}

/* تنسيقات الأزرار المدورة */
.rounded-button {
  @apply rounded-full shadow-sm transition-all duration-200 ease-in-out transform hover:scale-[1.02] active:scale-[0.98];
}

/* تنسيقات أزرار الآيكون */
.icon-button {
  @apply rounded-full p-1.5 transition-all duration-150 ease-in-out;
}

/* تنسيقات للأزرار المسطحة */
.flat-button {
  @apply border-none shadow-none bg-transparent hover:bg-accent/50 active:bg-accent/70 transition-all duration-200 ease-in-out;
}

/* الوضع النهاري - الحدود تبقى كما هي */
.card {
  border: 2px solid #ff4d6d; /* الأحمر */
}

/* الوضع الليلي - تغيير اللون إلى الذهبي */
.dark .card {
  border: 2px solid #d4af37; /* الذهبي */
}
