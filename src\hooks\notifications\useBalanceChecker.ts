
import { supabase } from "@/integrations/supabase/client";
import { fetchNotificationRules, fetchTelegramSettings } from './notificationSettings';
import { sendLowBalanceNotification } from './sendNotifications';
import { LowBalanceSim } from './types';
import { trackLowBalanceNotification } from './utils/notificationTracker';

/**
 * Hook for checking and sending low balance notifications
 */
export function useBalanceChecker() {
  
  /**
   * Check SIM balance and send notification if it's low
   */
  const checkSimBalance = async (simId: string): Promise<boolean> => {
    try {
      // Get notification rules
      const rules = await fetchNotificationRules();
      
      if (!rules || !rules.balance_alert_enabled) {
        console.log('❌ تنبيهات الرصيد المنخفض معطلة في الإعدادات');
        return false;
      }
      
      // Get minimum balance threshold from rules
      const minBalance = rules.min_balance || 0;
      
      // Get SIM data
      const { data: simData, error: simError } = await supabase
        .from('sims')
        .select(`
          id,
          number,
          balance,
          wallets:wallet_id (name)
        `)
        .eq('id', simId)
        .single();
        
      if (simError) {
        console.error('❌ خطأ في الحصول على بيانات الشريحة:', simError);
        return false;
      }
      
      if (!simData) {
        console.log('❌ لم يتم العثور على الشريحة');
        return false;
      }
      
      // Check if balance is below threshold
      if (simData.balance <= minBalance) {
        console.log(`⚠️ رصيد الشريحة منخفض: ${simData.balance} (أقل من ${minBalance})`);
        
        // Check if notification has been sent recently
        if (!trackLowBalanceNotification(simData.id)) {
          console.log(`⏱️ تم إرسال تنبيه مؤخراً لهذه الشريحة، تخطي التنبيه`);
          return false;
        }
        
        // Get Telegram settings
        const telegramSettings = await fetchTelegramSettings();
        
        if (!telegramSettings || !telegramSettings.is_enabled) {
          console.log('❌ إشعارات تليجرام معطلة في الإعدادات');
          return false;
        }
        
        // Prepare SIM data for notification
        const lowBalanceSim: LowBalanceSim = {
          id: simData.id,
          number: simData.number,
          balance: simData.balance,
          wallet_name: simData.wallets?.name || 'غير معروف'
        };
        
        // Send low balance notification
        await sendLowBalanceNotification(
          telegramSettings.bot_token,
          telegramSettings.chat_id,
          lowBalanceSim,
          minBalance
        );
        
        console.log('✅ تم إرسال تنبيه الرصيد المنخفض');
        return true;
      } else {
        console.log(`✅ رصيد الشريحة كافٍ: ${simData.balance} (أعلى من ${minBalance})`);
        return false;
      }
    } catch (error) {
      console.error('خطأ في التحقق من رصيد الشريحة:', error);
      return false;
    }
  };

  return {
    checkSimBalance
  };
}
