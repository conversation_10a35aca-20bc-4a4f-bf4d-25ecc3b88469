
import { ArrowDownCircle, ArrowUpCircle, Coins, ReceiptText, RefreshCw } from "lucide-react";
import { StatCard } from "@/components/dashboard/StatCard";
import { useIsMobile } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";

interface StatItem {
  title: string;
  value: string;
  icon: string;
  description: string;
  valueColor?: "green" | "red" | "default";
  effectColor?: "red" | "green" | "amber" | "blue";
}

interface DashboardStatsProps {
  stats: StatItem[];
  isLoading: boolean;
  onRefresh?: () => void;
}

export function DashboardStats({ stats, isLoading, onRefresh }: DashboardStatsProps) {
  const isMobile = useIsMobile();
  
  return (
    <div className="relative">
      {/* عنوان القسم */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-5">
        <h2 className="text-xl sm:text-2xl font-semibold mb-2 sm:mb-0 rtl-mobile-heading">
          مراجعة البيانات المالية والمعاملات الأخيرة
        </h2>
        
        {/* زر التحديث */}
        {onRefresh && (
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onRefresh} 
            className="hover:bg-transparent flex items-center gap-2 rtl-mobile-button"
            title="تحديث البيانات"
            disabled={isLoading}
          >
            <RefreshCw 
              className={`h-5 w-5 text-primary ${isLoading ? 'animate-spin' : ''}`} 
            />
            <span className="text-sm">تحديث</span>
          </Button>
        )}
      </div>
      
      <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6 ${isMobile ? 'rtl-mobile-stats' : ''}`}>
        {stats.map((stat, index) => {
          const icons = {
            "arrow-down": <ArrowDownCircle className="h-4 w-4 text-green-500" />,
            "arrow-up": <ArrowUpCircle className="h-4 w-4 text-red-500" />,
            "wallet": <Coins className="h-4 w-4 text-primary" />,
            "file-text": <ReceiptText className="h-4 w-4 text-blue-500" />
          };
          
          const effectColors = ["green", "amber", "blue", "red"] as const;
          
          return (
            <StatCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={icons[stat.icon as keyof typeof icons] || <ReceiptText className="h-4 w-4" />}
              description={stat.description}
              className={`animate-fade-in ${index > 0 ? `animate-delay-${index * 100}` : ''}`}
              isLoading={isLoading}
              valueColor={stat.valueColor}
              effectColor={stat.effectColor || effectColors[index % effectColors.length]}
            />
          );
        })}
      </div>
    </div>
  );
}
