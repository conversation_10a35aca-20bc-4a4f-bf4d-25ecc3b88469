
import { UserPermissions } from "@/types/user.types";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface PermissionOption {
  id: keyof UserPermissions;
  label: string;
}

interface UserPermissionCheckboxesProps {
  permissions: UserPermissions;
  onPermissionChange: (permissions: UserPermissions) => void;
  disabledPermissions?: Array<keyof UserPermissions>;
  idPrefix?: string;
}

const permissionOptions: PermissionOption[] = [
  { id: "dashboard", label: "لوحة التحكم" },
  { id: "transactions", label: "المعاملات" },
  { id: "customers", label: "العملاء" },
  { id: "debts", label: "الديون" },
  { id: "reports", label: "التقارير" },
  { id: "notification_logs", label: "سجل الإشعارات" },
  { id: "wallets", label: "المحافظ" },
  { id: "users", label: "المستخدمين" },
  { id: "settings", label: "الإعدادات" },
  { id: "database_tools", label: "أدوات قاعدة البيانات" }
];

export const UserPermissionCheckboxes = ({ 
  permissions, 
  onPermissionChange,
  disabledPermissions = [],
  idPrefix = ""
}: UserPermissionCheckboxesProps) => {
  const handlePermissionChange = (permission: keyof UserPermissions, checked: boolean) => {
    onPermissionChange({
      ...permissions,
      [permission]: checked
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {permissionOptions.map((option) => {
        const isDisabled = disabledPermissions.includes(option.id);
        const checkboxId = `${idPrefix}permission-${option.id}`;
        
        return (
          <div key={option.id} className="flex items-center space-x-2 space-x-reverse">
            <Checkbox 
              id={checkboxId}
              checked={permissions[option.id]} 
              onCheckedChange={(checked) => handlePermissionChange(option.id, !!checked)}
              disabled={isDisabled}
            />
            <Label 
              htmlFor={checkboxId}
              className={isDisabled ? "opacity-70" : ""}
            >
              {option.label}
              {isDisabled && option.id === "dashboard" && " (مطلوب)"}
            </Label>
          </div>
        );
      })}
    </div>
  );
};
