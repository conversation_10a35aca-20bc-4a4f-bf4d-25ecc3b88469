
import { supabase } from "@/integrations/supabase/client";
import { fetchNotificationRules, fetchTelegramSettings } from './notificationSettings';
import { FawryWalletOperation } from './types';
import { sendFawryOperationNotification } from './sendNotifications';
import { mapOperationType } from './utils/operationTypeMapper';
import { trackNotification } from './utils/notificationTracker';
import { checkFawryWalletBalances } from './fawryBalanceWatcher';

/**
 * مراقبة عمليات محافظ فوري وإرسال إشعارات
 */
export const setupFawryWalletWatcher = async () => {
  console.log('🔄 Setting up Fawry wallet watcher');
  
  // الحصول على قواعد الإشعار
  const rules = await fetchNotificationRules();
  
  if (!rules || !rules.transaction_alert_enabled) {
    console.log('❌ Fawry wallet alerts disabled in notification rules');
    return null;
  }
  
  // الحصول على إعدادات تليجرام
  const telegramSettings = await fetchTelegramSettings();
  
  if (!telegramSettings || !telegramSettings.is_enabled) {
    console.log('❌ Telegram notifications disabled in settings');
    return null;
  }

  console.log('✅ Telegram settings found for Fawry wallet:', telegramSettings.chat_id);
  
  // Setup polling for new Fawry wallet operations
  const checkForNewOperations = async () => {
    try {
      // Get the timestamp of the last check
      const lastCheckTime = localStorage.getItem('lastFawryCheckTime') || new Date(0).toISOString();
      
      // Get new operations since last check
      const { data: newOperations, error } = await supabase
        .from('fawry_wallet_operations')
        .select('id')
        .gt('created_at', lastCheckTime)
        .order('created_at', { ascending: true });
      
      if (error) {
        console.error('Error fetching new Fawry operations:', error);
        return;
      }
      
      // Update last check time
      localStorage.setItem('lastFawryCheckTime', new Date().toISOString());
      
      if (!newOperations || newOperations.length === 0) {
        console.log('No new Fawry operations detected');
        return;
      }
      
      console.log(`Found ${newOperations.length} new Fawry operations`);
      
      // Process each new operation
      for (const op of newOperations) {
        await processNewOperation(op.id, rules, telegramSettings);
      }
    } catch (err) {
      console.error('Error in Fawry operation polling:', err);
    }
  };
  
  // Start polling immediately
  await checkForNewOperations();
  
  // Set up interval for polling
  const intervalId = setInterval(checkForNewOperations, 15000); // Check every 15 seconds
  
  // Return function to clean up the interval
  return () => {
    clearInterval(intervalId);
  };
};

/**
 * Process a new Fawry wallet operation
 */
const processNewOperation = async (operationId, rules, telegramSettings) => {
  try {
    // Prevent duplicate notifications
    const notificationKey = `fawry_${operationId}`;
    
    // Avoid sending duplicate notifications within 30 seconds
    if (!trackNotification(notificationKey, 30000)) {
      return;
    }
    
    // Get operation details
    const { data: operation } = await supabase
      .from('fawry_wallet_operations')
      .select(`
        *,
        wallet:wallet_id(name, balance)
      `)
      .eq('id', operationId)
      .single();
    
    if (!operation) {
      console.log('❌ Fawry operation not found in database');
      return;
    }
    
    // Map operation type from Arabic to English for rule checking
    const arabicOperationType = operation.operation_type;
    const mappedOperationType = mapOperationType(arabicOperationType);
    
    console.log(`Operation type ${arabicOperationType} mapped to ${mappedOperationType} for notification rules check`);
    
    // Check if operation type matches rules
    if (rules.transaction_types && rules.transaction_types.length > 0 && !rules.transaction_types.includes(mappedOperationType)) {
      console.log(`❌ Operation type ${arabicOperationType} (mapped to ${mappedOperationType}) not in notification rules:`, rules.transaction_types);
      return;
    }
    
    // Check operation amount against rules
    if (rules.min_transaction_amount > 0 && operation.amount < rules.min_transaction_amount) {
      console.log(`❌ Amount ${operation.amount} below minimum threshold ${rules.min_transaction_amount}`);
      return;
    }
    
    console.log('📤 Sending Fawry operation notification', operation);
    
    // Send notification
    await sendFawryOperationNotification(
      telegramSettings.bot_token,
      telegramSettings.chat_id,
      operation as FawryWalletOperation
    );
    
    // Check wallet balance after operation
    await checkFawryWalletBalances();
  } catch (error) {
    console.error('Error processing Fawry wallet notification:', error);
  }
};
