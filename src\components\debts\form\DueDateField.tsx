
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { CalendarIcon } from "lucide-react";
import { 
  FormField, 
  FormItem, 
  FormLabel, 
  FormControl, 
  FormMessage 
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DueDateFieldProps {
  loading: boolean;
}

export function DueDateField({ loading }: DueDateFieldProps) {
  return (
    <FormField
      name="due_date"
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>تاريخ الاستحقاق</FormLabel>
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={`w-full justify-start text-right ${
                    !field.value ? "text-muted-foreground" : ""
                  }`}
                  disabled={loading}
                >
                  <CalendarIcon className="ml-2 h-4 w-4" />
                  {field.value ? (
                    format(field.value, "dd/MM/yyyy", { locale: ar })
                  ) : (
                    <span>اختر التاريخ</span>
                  )}
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={field.value || undefined}
                onSelect={field.onChange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
