
import { useState } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { DateRange } from "react-day-picker";
import { DateRangeSelector } from "@/components/reports/DateRangeSelector";

interface TransactionFiltersProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  typeFilter: string | null;
  setTypeFilter: (value: string | null) => void;
  walletFilter?: string | null;
  setWalletFilter?: (value: string | null) => void;
  statusFilter?: string | null;
  setStatusFilter?: (value: string | null) => void;
  dateRange?: DateRange | undefined;
  setDateRange?: (range: DateRange | undefined) => void;
  filterStatus?: string | null;
  handleFilterChange?: (status: string) => void;
}

export function TransactionFilters({
  searchTerm,
  setSearchTerm,
  typeFilter,
  setTypeFilter,
  walletFilter,
  setWalletFilter,
  statusFilter,
  setStatusFilter,
  dateRange,
  setDateRange,
  filterStatus,
  handleFilterChange
}: TransactionFiltersProps) {
  const [wallets, setWallets] = useState<string[]>([]);

  // Simulated wallet data for filtering
  useState(() => {
    // In a real app, you would fetch this data from your backend
    setWallets(["فودافون كاش", "اتصالات كاش", "أورانج كاش", "وي", "بنك"]);
  });

  return (
    <div className="mb-6 space-y-4">
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
          <Input
            placeholder="بحث عن معاملة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex flex-col gap-4 sm:flex-row">
          <Select
            value={typeFilter || "all"}
            onValueChange={(value) => setTypeFilter(value === "all" ? null : value)}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="نوع المعاملة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع المعاملات</SelectItem>
              <SelectItem value="receive">استلام</SelectItem>
              <SelectItem value="send">ارسال</SelectItem>
            </SelectContent>
          </Select>

          {setWalletFilter && (
            <Select
              value={walletFilter || "all"}
              onValueChange={(value) => setWalletFilter(value === "all" ? null : value)}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="المحفظة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المحافظ</SelectItem>
                {wallets.map((wallet) => (
                  <SelectItem key={wallet} value={wallet}>
                    {wallet}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          {setStatusFilter && (
            <Select
              value={statusFilter || "all"}
              onValueChange={(value) => setStatusFilter(value === "all" ? null : value)}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="completed">مكتملة</SelectItem>
                <SelectItem value="pending">قيد الانتظار</SelectItem>
                <SelectItem value="failed">فشلت</SelectItem>
              </SelectContent>
            </Select>
          )}
          
          {handleFilterChange && filterStatus !== undefined && (
            <Select
              value={filterStatus || "all"}
              onValueChange={(value) => handleFilterChange(value)}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="مرشح الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="completed">مكتملة</SelectItem>
                <SelectItem value="pending">قيد الانتظار</SelectItem>
                <SelectItem value="failed">فشلت</SelectItem>
              </SelectContent>
            </Select>
          )}
          
          {setDateRange && dateRange && (
            <div className="w-full sm:w-[220px]">
              <DateRangeSelector
                date={dateRange}
                onDateChange={setDateRange}
                className="w-full"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
