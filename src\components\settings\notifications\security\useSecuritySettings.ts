
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface SecuritySettings {
  is_enabled: boolean;
  dev_login_alerts: boolean;
  alert_chat_id: string;
}

export function useSecuritySettings() {
  const [isEnabled, setIsEnabled] = useState(false);
  const [devLoginAlerts, setDevLoginAlerts] = useState(true);
  const [chatId, setChatId] = useState("1064109227"); // Default to the provided ID
  const [isSaving, setIsSaving] = useState(false);
  const [botToken, setBotToken] = useState("");
  const { user } = useAuth();
  const [currentRole, setCurrentRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch security settings and user role
  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        // Check user role
        if (user) {
          const { data: roleData, error: roleError } = await supabase
            .from("user_roles")
            .select("role")
            .eq("user_id", user.id)
            .single();
            
          if (roleError) throw roleError;
          setCurrentRole(roleData?.role || null);
        }

        // Get telegram settings (which contains the bot token)
        const { data: telegramData, error: telegramError } = await supabase
          .from("telegram_settings")
          .select("*")
          .single();
        
        if (!telegramError && telegramData) {
          setBotToken(telegramData.bot_token || "");
        }
        
        // Check if security settings table exists
        const { data: securityData, error: securityError } = await supabase
          .from("security_settings")
          .select("*")
          .single();
        
        if (!securityError && securityData) {
          setIsEnabled(securityData.is_enabled);
          setDevLoginAlerts(securityData.dev_login_alerts !== false);
          setChatId(securityData.alert_chat_id || "1064109227");
        } else if (securityError && securityError.code !== "PGRST116") {
          // PGRST116 is "No rows found" which is OK for empty tables
          throw securityError;
        }
      } catch (error) {
        console.error("Error fetching security settings:", error);
        setError("فشل في تحميل إعدادات الأمان");
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, [user]);

  // Save security settings
  const handleSaveSettings = async () => {
    setIsSaving(true);
    
    try {
      // First check if security_settings table exists by trying to fetch a record
      const { data: checkData, error: checkError } = await supabase
        .from("security_settings")
        .select("id")
        .limit(1);
      
      // Upsert the security settings (will insert if not exists or update if exists)
      const { error } = await supabase
        .from("security_settings")
        .upsert({
          id: 1,
          is_enabled: isEnabled,
          dev_login_alerts: devLoginAlerts,
          alert_chat_id: chatId,
          updated_at: new Date().toISOString()
        });
      
      if (error) throw error;
      
      toast.success("تم حفظ إعدادات الأمان بنجاح");
    } catch (error) {
      console.error("Error saving security settings:", error);
      toast.error("حدث خطأ أثناء حفظ إعدادات الأمان");
    } finally {
      setIsSaving(false);
    }
  };

  // Send test alert
  const sendTestAlert = async () => {
    try {
      if (!botToken) {
        toast.error("يجب إدخال رمز بوت التليجرام أولاً في إعدادات التليجرام");
        return;
      }

      if (!chatId) {
        toast.error("يجب إدخال معرف الدردشة");
        return;
      }

      toast.info("جاري إرسال تنبيه اختباري...");

      // Format timestamp in Gregorian calendar (DD/MM/YYYY, HH:MM:SS)
      const now = new Date();
      const timestamp = now.toLocaleString('ar-EG', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
      });

      const alertData = {
        status: "success",
        username: "اختبار النظام",
        userId: "test-user-id",
        ip: "127.0.0.1",
        timestamp: timestamp,
        role: "developer"
      };

      const { data, error } = await supabase.functions.invoke("securityAlert", {
        body: {
          alertData,
          chatId,
          botToken
        }
      });

      if (error) throw error;

      if (data && data.success) {
        toast.success("تم إرسال التنبيه الاختباري بنجاح");
      } else {
        throw new Error(data?.error || "فشل في إرسال التنبيه");
      }
    } catch (error) {
      console.error("Error sending test alert:", error);
      toast.error("فشل إرسال التنبيه الاختباري: " + (error.message || "خطأ غير معروف"));
    }
  };

  const reloadSettings = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get telegram settings (which contains the bot token)
      const { data: telegramData, error: telegramError } = await supabase
        .from("telegram_settings")
        .select("*")
        .single();
      
      if (!telegramError && telegramData) {
        setBotToken(telegramData.bot_token || "");
      }
      
      // Check if security settings table exists
      const { data: securityData, error: securityError } = await supabase
        .from("security_settings")
        .select("*")
        .single();
      
      if (!securityError && securityData) {
        setIsEnabled(securityData.is_enabled);
        setDevLoginAlerts(securityData.dev_login_alerts !== false);
        setChatId(securityData.alert_chat_id || "1064109227");
      }
    } catch (error) {
      console.error("Error reloading security settings:", error);
      setError("فشل في تحديث إعدادات الأمان");
    } finally {
      setLoading(false);
    }
  };

  return {
    isEnabled,
    setIsEnabled,
    devLoginAlerts,
    setDevLoginAlerts,
    chatId,
    setChatId,
    isSaving,
    botToken,
    currentRole,
    loading,
    error,
    handleSaveSettings,
    sendTestAlert,
    reloadSettings
  };
}
