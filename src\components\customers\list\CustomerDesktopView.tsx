
import { Customer } from "@/pages/Customers";
import { CustomerActions } from "./CustomerActions";
import { ModernTable, TableColumn } from "@/components/ui/modern-table/ModernTable";

interface CustomerDesktopViewProps {
  customers: Customer[];
  loading: boolean;
  onEdit: (customer: Customer) => void;
  onDelete: (customerId: string) => void;
}

export function CustomerDesktopView({
  customers,
  loading,
  onEdit,
  onDelete
}: CustomerDesktopViewProps) {
  const columns: TableColumn<Customer>[] = [
    {
      header: "اسم العميل",
      accessor: "name",
      className: "font-medium"
    },
    {
      header: "رقم الهاتف",
      accessor: (customer) => customer.phone || "—"
    },
    {
      header: "ملاحظات",
      accessor: (customer) => customer.notes || "—"
    },
    {
      header: "إجراءات",
      accessor: (customer) => (
        <CustomerActions 
          customer={customer}
          onEdit={() => onEdit(customer)}
          onDelete={() => onDelete(customer.id)}
        />
      ),
      className: "text-center w-[120px]"
    }
  ];

  return (
    <ModernTable
      data={customers}
      columns={columns}
      loading={loading}
      keyField="id"
      emptyMessage="لا يوجد عملاء مسجلين"
      dir="rtl"
    />
  );
}
